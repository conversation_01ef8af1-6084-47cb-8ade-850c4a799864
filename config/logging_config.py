"""
Enhanced Logging Configuration for API Request/Response Monitoring

This module provides comprehensive logging configuration specifically designed
for diagnosing API authentication and authorization issues, including 403 errors.
"""

from __future__ import annotations

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Dict, Any, Optional
import json
from datetime import datetime

from config.settings import get_settings


class APIRequestFilter(logging.Filter):
    """Filter to identify API request/response logs"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        # Add API-specific context to log records
        if hasattr(record, 'correlation_id'):
            record.api_request = True
        return True


class StructuredAPIFormatter(logging.Formatter):
    """Structured formatter for API logs with JSON output"""
    
    def format(self, record: logging.LogRecord) -> str:
        # Create structured log entry
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add API-specific fields if present
        if hasattr(record, 'correlation_id'):
            log_entry["correlation_id"] = record.correlation_id
        if hasattr(record, 'user_id'):
            log_entry["user_id"] = record.user_id
        if hasattr(record, 'operation'):
            log_entry["operation"] = record.operation
        if hasattr(record, 'service_name'):
            log_entry["service_name"] = record.service_name
        if hasattr(record, 'status_code'):
            log_entry["status_code"] = record.status_code
        if hasattr(record, 'response_time_ms'):
            log_entry["response_time_ms"] = record.response_time_ms
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, ensure_ascii=False)


def setup_api_logging(
    log_level: str = "DEBUG",
    log_dir: Optional[str] = None,
    max_file_size: int = 50 * 1024 * 1024,  # 50MB
    backup_count: int = 10,
    enable_console: bool = True,
    enable_structured: bool = True
) -> Dict[str, logging.Logger]:
    """
    Setup comprehensive API logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory for log files (defaults to logs/)
        max_file_size: Maximum log file size in bytes
        backup_count: Number of backup log files to keep
        enable_console: Whether to enable console logging
        enable_structured: Whether to use structured JSON logging
    
    Returns:
        Dictionary of configured loggers
    """
    settings = get_settings()
    
    # Create log directory
    if log_dir is None:
        log_dir = "logs"
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # Convert log level
    numeric_level = getattr(logging, log_level.upper(), logging.DEBUG)
    
    # Configure loggers
    loggers = {}
    
    # 1. API Request/Response Logger
    api_logger = logging.getLogger("api")
    api_logger.setLevel(numeric_level)
    api_logger.handlers.clear()
    
    # API log file handler with rotation
    api_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "api_requests.log",
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding="utf-8"
    )
    api_file_handler.setLevel(numeric_level)
    
    if enable_structured:
        api_file_handler.setFormatter(StructuredAPIFormatter())
    else:
        api_file_handler.setFormatter(logging.Formatter(
            "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        ))
    
    api_file_handler.addFilter(APIRequestFilter())
    api_logger.addHandler(api_file_handler)
    loggers["api"] = api_logger
    
    # 2. Card Service Logger
    card_logger = logging.getLogger("api.card_service")
    card_logger.setLevel(numeric_level)
    card_logger.handlers.clear()
    
    # Card service specific log file
    card_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "card_service.log",
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding="utf-8"
    )
    card_file_handler.setLevel(numeric_level)
    
    if enable_structured:
        card_file_handler.setFormatter(StructuredAPIFormatter())
    else:
        card_file_handler.setFormatter(logging.Formatter(
            "%(asctime)s [%(levelname)s] CARD_SERVICE: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        ))
    
    card_logger.addHandler(card_file_handler)
    loggers["card_service"] = card_logger
    
    # 3. External API Service Logger
    external_api_logger = logging.getLogger("api.external_api_service")
    external_api_logger.setLevel(numeric_level)
    external_api_logger.handlers.clear()
    
    # External API service specific log file
    external_api_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "external_api_service.log",
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding="utf-8"
    )
    external_api_file_handler.setLevel(numeric_level)
    
    if enable_structured:
        external_api_file_handler.setFormatter(StructuredAPIFormatter())
    else:
        external_api_file_handler.setFormatter(logging.Formatter(
            "%(asctime)s [%(levelname)s] EXTERNAL_API: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        ))
    
    external_api_logger.addHandler(external_api_file_handler)
    loggers["external_api_service"] = external_api_logger
    
    # 4. Authentication/Authorization Logger
    auth_logger = logging.getLogger("api.auth")
    auth_logger.setLevel(numeric_level)
    auth_logger.handlers.clear()
    
    # Authentication specific log file
    auth_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "api_auth.log",
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding="utf-8"
    )
    auth_file_handler.setLevel(numeric_level)
    
    if enable_structured:
        auth_file_handler.setFormatter(StructuredAPIFormatter())
    else:
        auth_file_handler.setFormatter(logging.Formatter(
            "%(asctime)s [%(levelname)s] AUTH: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        ))
    
    auth_logger.addHandler(auth_file_handler)
    loggers["auth"] = auth_logger
    
    # 5. Error-specific Logger (for 403 and other critical errors)
    error_logger = logging.getLogger("api.errors")
    error_logger.setLevel(logging.WARNING)  # Only warnings and above
    error_logger.handlers.clear()
    
    # Error specific log file
    error_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "api_errors.log",
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding="utf-8"
    )
    error_file_handler.setLevel(logging.WARNING)
    
    if enable_structured:
        error_file_handler.setFormatter(StructuredAPIFormatter())
    else:
        error_file_handler.setFormatter(logging.Formatter(
            "%(asctime)s [%(levelname)s] ERROR: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        ))
    
    error_logger.addHandler(error_file_handler)
    loggers["errors"] = error_logger
    
    # Console handler (optional)
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)  # Less verbose for console
        console_handler.setFormatter(logging.Formatter(
            "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
            datefmt="%H:%M:%S"
        ))
        
        # Add console handler to all loggers
        for logger in loggers.values():
            logger.addHandler(console_handler)
    
    # Prevent propagation to root logger to avoid duplicate logs
    for logger in loggers.values():
        logger.propagate = False
    
    # Log configuration summary
    main_logger = logging.getLogger(__name__)
    main_logger.info(f"API logging configured with {len(loggers)} specialized loggers")
    main_logger.info(f"Log directory: {log_path.absolute()}")
    main_logger.info(f"Log level: {log_level}")
    main_logger.info(f"Max file size: {max_file_size / (1024*1024):.1f}MB")
    main_logger.info(f"Backup count: {backup_count}")
    main_logger.info(f"Structured logging: {enable_structured}")
    
    return loggers


def get_api_logger_config() -> Dict[str, Any]:
    """Get API logging configuration for external use"""
    settings = get_settings()
    
    return {
        "log_level": getattr(settings, "API_LOG_LEVEL", "DEBUG"),
        "log_dir": getattr(settings, "API_LOG_DIR", "logs"),
        "max_file_size": getattr(settings, "API_LOG_MAX_SIZE", 50 * 1024 * 1024),
        "backup_count": getattr(settings, "API_LOG_BACKUP_COUNT", 10),
        "enable_console": getattr(settings, "API_LOG_CONSOLE", True),
        "enable_structured": getattr(settings, "API_LOG_STRUCTURED", True),
    }


# Initialize API logging on import
_api_loggers = None

def get_configured_api_loggers() -> Dict[str, logging.Logger]:
    """Get configured API loggers (lazy initialization)"""
    global _api_loggers
    if _api_loggers is None:
        config = get_api_logger_config()
        _api_loggers = setup_api_logging(**config)
    return _api_loggers
