"""
Shared API Client Library

A configurable, reusable API client system that provides:
- Unified HTTP client with retry logic and error handling
- Flexible authentication support
- Configuration-driven API definitions
- Comprehensive logging and monitoring
- Easy extensibility for new APIs

This library extracts and generalizes the core API functionality
from the existing API v1 implementation to enable reuse across
multiple API integrations.
"""

from .core.base_client import BaseAPIClient
from .core.interfaces import APIClientProtocol, APIConfigProtocol
from .config.client_factory import APIClientFactory
from .config.api_config import APIConfiguration, EndpointConfiguration
from .http.client import ConfigurableHTTPClient
from .core.exceptions import (
    SharedAPIException,
    ConfigurationError,
    AuthenticationError,
    HTTPClientError,
    ValidationError,
)

__version__ = "1.0.0"
__all__ = [
    # Core classes
    "BaseAPIClient",
    "APIClientProtocol", 
    "APIConfigProtocol",
    
    # Configuration
    "APIConfiguration",
    "EndpointConfiguration",
    "APIClientFactory",
    
    # HTTP client
    "ConfigurableHTTPClient",
    
    # Exceptions
    "SharedAPIException",
    "ConfigurationError", 
    "AuthenticationError",
    "HTTPClientError",
    "ValidationError",
]
