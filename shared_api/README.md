# Shared API Client Library

A configurable, reusable API client system that provides unified HTTP client functionality with comprehensive error handling, authentication support, and easy extensibility for multiple APIs.

## 🚀 Features

- **Configuration-Driven**: Define APIs through configuration objects or files
- **Multiple Authentication Types**: <PERSON> Key, Bear<PERSON>, Basic Auth, OAuth2, Custom Headers
- **Comprehensive Error Handling**: Categorized errors with recovery suggestions
- **Retry Logic**: Configurable retry with exponential backoff
- **Health Monitoring**: Built-in health checks and monitoring
- **Type Safety**: Full type hints throughout the codebase
- **Backward Compatibility**: Drop-in replacement for existing API v1 code
- **Easy Testing**: Built-in mocking and testing support

## 📦 Installation

The shared API library is included in your project. Simply import and use:

```python
from shared_api import APIClientFactory, APIConfiguration
```

## 🏗️ Architecture

```
shared_api/
├── core/                   # Base classes and interfaces
├── config/                # Configuration management
├── http/                  # HTTP client implementation
├── utils/                 # Utility functions
├── examples/              # Usage examples
└── compatibility.py       # Backward compatibility layer
```

## 🚀 Quick Start

### Using the Compatibility Layer (Easiest Migration)

```python
# Drop-in replacement for existing code
from shared_api.compatibility import get_external_api_service

api_service = get_external_api_service(
    base_url="https://api.example.com",
    login_token="your-token"
)

# Use exactly like before
response = await api_service.list_items(params={"page": 1, "limit": 10})
```

### Creating a New API Client

```python
from shared_api import APIClientFactory, APIConfiguration, EndpointConfiguration
from shared_api.core.constants import HTTPMethod, AuthenticationType

# Define your API
config = APIConfiguration(
    name="my_api",
    base_url="https://api.example.com/v1",
    endpoints={
        "get_users": EndpointConfiguration(
            name="get_users",
            path="/users",
            method=HTTPMethod.GET
        ),
        "create_user": EndpointConfiguration(
            name="create_user", 
            path="/users",
            method=HTTPMethod.POST
        )
    },
    authentication=AuthenticationConfiguration(
        type=AuthenticationType.API_KEY,
        api_key="your-api-key"
    )
)

# Create and use client
factory = APIClientFactory()
async with factory.create_client(config) as client:
    users = await client.get("get_users")
    new_user = await client.post("create_user", data={"name": "John"})
```

### Using the API Registry

```python
from shared_api.config.registry import api_registry

# Register multiple APIs
api_registry.register_api(api_v1_config)
api_registry.register_api(payment_api_config)
api_registry.register_api(inventory_api_config)

# Use any registered API
client = api_registry.get_client("payment_api")
payment = await client.post("process_payment", data=payment_data)

# Monitor health of all APIs
health_status = await api_registry.check_all_health()
```

## 📋 Configuration Examples

### API v1 (Existing Implementation)

```python
from shared_api.examples.api_v1_config import create_api_v1_configuration

config = create_api_v1_configuration(
    base_url="https://ronaldo-club.to/api",
    login_token="your-bearer-token",
    session_cookies={"loginToken": "token-value"}
)
```

### New REST API

```python
from shared_api.config.api_config import APIConfiguration, EndpointConfiguration

config = APIConfiguration(
    name="inventory_api",
    base_url="https://inventory.example.com/api/v1",
    endpoints={
        "list_products": EndpointConfiguration(
            name="list_products",
            path="/products",
            method=HTTPMethod.GET
        ),
        "create_product": EndpointConfiguration(
            name="create_product",
            path="/products", 
            method=HTTPMethod.POST
        )
    },
    authentication=AuthenticationConfiguration(
        type=AuthenticationType.BEARER_TOKEN,
        bearer_token="your-token"
    ),
    timeout=TimeoutConfiguration(connect=10, read=30, total=60),
    retry=RetryConfiguration(max_attempts=3, delay=1.0, backoff_factor=2.0)
)
```

### Configuration from JSON

```python
# config.json
{
    "name": "external_api",
    "base_url": "https://api.external.com/v2",
    "endpoints": {
        "get_data": {
            "name": "get_data",
            "path": "/data",
            "method": "GET"
        }
    },
    "authentication": {
        "type": "api_key",
        "api_key_header": "X-API-Key"
    }
}

# Load and use
factory = APIClientFactory()
config = factory.load_configuration_from_file("config.json")
client = factory.create_client(config)
```

## 🔧 Advanced Usage

### Custom Authentication

```python
auth = AuthenticationConfiguration(
    type=AuthenticationType.CUSTOM_HEADER,
    custom_headers={
        "X-Custom-Auth": "custom-value",
        "X-Client-ID": "client-123"
    }
)
```

### Environment-Specific Configuration

```python
# Development
dev_config = APIConfiguration(
    name="my_api",
    base_url="https://dev-api.example.com",
    environment="development",
    # ... other config
)

# Production
prod_config = APIConfiguration(
    name="my_api", 
    base_url="https://api.example.com",
    environment="production",
    # ... other config
)
```

### Error Handling

```python
from shared_api.core.exceptions import HTTPClientError, AuthenticationError

try:
    response = await client.get("protected_endpoint")
except AuthenticationError as e:
    print(f"Auth failed: {e.message}")
    # Handle authentication error
except HTTPClientError as e:
    print(f"HTTP error {e.status_code}: {e.message}")
    # Handle HTTP error
```

## 🧪 Testing

```python
import pytest
from shared_api.config.client_factory import api_client_factory

@pytest.mark.asyncio
async def test_api_client():
    config = create_test_configuration()
    
    async with api_client_factory.create_client(config) as client:
        # Mock HTTP responses for testing
        response = await client.get("test_endpoint")
        assert response["success"] is True
```

## 📊 Monitoring and Health Checks

```python
from shared_api.config.registry import api_registry

# Register APIs
api_registry.register_api(config1)
api_registry.register_api(config2)

# Check health of specific API
is_healthy = await api_registry.check_api_health("my_api")

# Check all APIs
health_results = await api_registry.check_all_health()

# Get detailed API information
api_info = api_registry.get_api_info("my_api")
```

## 🔄 Migration from Existing Code

### Step 1: Use Compatibility Layer
```python
# Change this:
from services.external_api_service import get_external_api_service

# To this:
from shared_api.compatibility import get_external_api_service

# Everything else stays the same!
```

### Step 2: Gradual Migration
```python
# Start using new patterns gradually
from shared_api.config.client_factory import api_client_factory
from shared_api.examples.api_v1_config import create_api_v1_configuration

config = create_api_v1_configuration(login_token="your-token")
client = api_client_factory.create_client(config)
```

### Step 3: Full Migration
```python
# Use new system for all new APIs
config = APIConfiguration(...)
client = factory.create_client(config)
```

## 🛠️ Adding New APIs

Adding a new API is simple with the shared system:

1. **Create Configuration**:
```python
new_api_config = APIConfiguration(
    name="new_api",
    base_url="https://newapi.example.com",
    endpoints={...},
    authentication=AuthenticationConfiguration(...)
)
```

2. **Register and Use**:
```python
api_registry.register_api(new_api_config)
client = api_registry.get_client("new_api")
```

3. **That's it!** No additional code needed.

## 📚 API Reference

### Core Classes
- `APIConfiguration`: Main configuration class
- `EndpointConfiguration`: Individual endpoint configuration
- `AuthenticationConfiguration`: Authentication settings
- `ConfigurableHTTPClient`: Main HTTP client
- `APIClientFactory`: Factory for creating clients
- `APIRegistry`: Registry for managing multiple APIs

### Authentication Types
- `AuthenticationType.NONE`: No authentication
- `AuthenticationType.API_KEY`: API key authentication
- `AuthenticationType.BEARER_TOKEN`: Bearer token authentication
- `AuthenticationType.BASIC_AUTH`: Basic authentication
- `AuthenticationType.OAUTH2`: OAuth2 authentication
- `AuthenticationType.CUSTOM_HEADER`: Custom header authentication

### HTTP Methods
- `HTTPMethod.GET`, `HTTPMethod.POST`, `HTTPMethod.PUT`, `HTTPMethod.DELETE`, etc.

## 🤝 Contributing

1. Follow the existing code patterns
2. Add tests for new functionality
3. Update documentation
4. Ensure backward compatibility

## 📄 License

This shared API library is part of the bot_v2 project.
