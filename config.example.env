# Demo Wallet & Card Bot — config.example.env
# Copy to .env and fill in values.

BOT_TOKEN=
DEMO_API_BASE=https://demo.api.example
DEMO_API_TOKEN=

# Initial settings
INITIAL_BALANCE=100.00
DEFAULT_CURRENCY=USD

# Sanctions & Compliance (comma-separated values)
SANCTIONED_COUNTRIES=CU,IR,KP,SY,UA-CRIMEA
AML_HOURLY_LIMIT=200
DAILY_SPEND_CAP=500
MONTHLY_SPEND_CAP=2000

# Rate limits (per-user)
PURCHASES_PER_MINUTE=3
PURCHASES_PER_DAY=50
SEARCHES_PER_MINUTE=10
MESSAGES_PER_MINUTE=10
CALLBACKS_PER_MINUTE=20

# Fallback generator
FALLBACK_ENABLED=true

# Observability
LOG_LEVEL=INFO
ENVIRONMENT=development
LOG_TO_FILE=false
LOG_FILE_PATH=logs/bot.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5
LOG_COLOR=true
LOG_STRUCTURED=true
LOG_SHOW_CATEGORY=false
METRICS_ENABLED=true
METRICS_PORT=8000
RETENTION_ENABLED=true
RETENTION_DAYS=45
USE_MONGODB=false
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=demo_wallet_bot
ADMIN_USER_IDS=
ADMIN_PASSPHRASE=

# API Security (Optional - will generate secure defaults if not set)
# API_ENCRYPTION_KEY=your-secure-encryption-key-here
# API_ENCRYPTION_SALT=your-base64-encoded-salt-here

# Centralized API Config encryption (Fernet key, URL-safe base64)
# Generate once: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
API_CONFIG_ENCRYPTION_KEY=
