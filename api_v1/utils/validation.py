"""
API v1 Validation Utilities

Provides validation functions and decorators for API v1 components.
"""

from typing import Any, Dict, List, Optional, Union
import re
from urllib.parse import urlparse


def validate_url(url: str) -> bool:
    """Validate if a string is a valid URL"""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def validate_api_key(api_key: str) -> bool:
    """Validate API key format"""
    if not api_key or not isinstance(api_key, str):
        return False
    
    # Basic validation - at least 8 characters, alphanumeric
    return len(api_key) >= 8 and re.match(r'^[a-zA-Z0-9_-]+$', api_key)


def validate_service_name(name: str) -> bool:
    """Validate service name format"""
    if not name or not isinstance(name, str):
        return False
    
    # Service name should be alphanumeric with underscores/hyphens
    return re.match(r'^[a-zA-Z0-9_-]+$', name) and len(name) <= 50


def validate_endpoint_config(endpoint: Dict[str, Any]) -> tuple[bool, Optional[str]]:
    """Validate endpoint configuration"""
    required_fields = ['name', 'url', 'method']
    
    for field in required_fields:
        if field not in endpoint:
            return False, f"Missing required field: {field}"
    
    # Validate HTTP method
    valid_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
    if endpoint['method'].upper() not in valid_methods:
        return False, f"Invalid HTTP method: {endpoint['method']}"
    
    # Validate URL format (should start with /)
    if not endpoint['url'].startswith('/'):
        return False, "Endpoint URL should start with '/'"
    
    return True, None


def validate_credentials(credentials: Dict[str, Any]) -> tuple[bool, Optional[str]]:
    """Validate credentials configuration"""
    if not isinstance(credentials, dict):
        return False, "Credentials must be a dictionary"
    
    # Check for at least one authentication method
    auth_methods = ['api_key', 'bearer_token', 'basic_auth', 'oauth2', 'custom_headers']
    has_auth = any(method in credentials for method in auth_methods)
    
    if not has_auth and not credentials.get('headers'):
        return False, "At least one authentication method or headers must be provided"
    
    return True, None


__all__ = [
    'validate_url',
    'validate_api_key', 
    'validate_service_name',
    'validate_endpoint_config',
    'validate_credentials',
]
