"""
Unified Encryption Service

Consolidates all encryption/decryption functionality from across the codebase
into a single, reusable service with consistent behavior.
"""

import base64
import logging
import os
from typing import Optional

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from config.settings import get_settings

logger = logging.getLogger(__name__)


class EncryptionService:
    """
    Unified encryption service for all API-related sensitive data.
    
    Consolidates encryption logic from:
    - api_service.py
    - api_config_service.py  
    - auth_profile_service.py
    - api_security.py
    """

    def __init__(self):
        self.settings = get_settings()
        self._fernet = None
        self._init_encryption()

    def _init_encryption(self):
        """Initialize encryption with a key derived from settings"""
        # Get encryption key from environment or generate a secure default
        encryption_key = os.getenv("API_ENCRYPTION_KEY")

        if not encryption_key:
            logger.warning(
                "API_ENCRYPTION_KEY not set. Using generated key. "
                "Set API_ENCRYPTION_KEY environment variable for production use."
            )
            # Generate a secure random key for this session
            encryption_key = base64.urlsafe_b64encode(os.urandom(32)).decode()

        # Get salt from environment or generate a secure default
        salt_env = os.getenv("API_ENCRYPTION_SALT")
        if salt_env:
            try:
                salt = base64.urlsafe_b64decode(salt_env.encode())
            except Exception as e:
                logger.warning(
                    f"Invalid API_ENCRYPTION_SALT format: {e}. Using generated salt. "
                    "Ensure API_ENCRYPTION_SALT is valid base64."
                )
                # Generate a secure random salt for this session
                salt = os.urandom(16)
        else:
            logger.warning(
                "API_ENCRYPTION_SALT not set. Using generated salt. "
                "Set API_ENCRYPTION_SALT environment variable for production use."
            )
            # Generate a secure random salt for this session
            salt = os.urandom(16)

        try:
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(encryption_key.encode()))
            self._fernet = Fernet(key)
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
            raise RuntimeError("Encryption initialization failed")

    def encrypt(self, data: str) -> str:
        """
        Encrypt sensitive data
        
        Args:
            data: Plain text data to encrypt
            
        Returns:
            Encrypted data as base64 string
        """
        if not data:
            return ""
        
        try:
            return self._fernet.encrypt(data.encode()).decode()
        except Exception as e:
            logger.error(f"Failed to encrypt data: {e}")
            raise RuntimeError("Encryption failed")

    def decrypt(self, encrypted_data: str) -> str:
        """
        Decrypt sensitive data
        
        Args:
            encrypted_data: Base64 encrypted data
            
        Returns:
            Decrypted plain text data
        """
        if not encrypted_data:
            return ""

        # If data doesn't appear to be encrypted, return failure marker
        if not self._is_encrypted_data(encrypted_data):
            logger.debug("Data does not appear encrypted; returning failure marker")
            return "[DECRYPTION_FAILED]"

        try:
            return self._fernet.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            logger.warning(f"Failed to decrypt data: {e}")
            return "[DECRYPTION_FAILED]"

    def _is_encrypted_data(self, data: str) -> bool:
        """
        Check if data appears to be encrypted (Fernet format)
        
        Args:
            data: Data to check
            
        Returns:
            True if data appears to be encrypted
        """
        if not data:
            return False

        try:
            # Fernet tokens are base64 encoded and have a specific structure
            decoded = base64.urlsafe_b64decode(data.encode())
            # Fernet tokens have a specific minimum length
            return len(decoded) >= 32  # Fernet minimum is around 60 bytes encoded
        except:
            return False

    def encrypt_dict_fields(self, data: dict, sensitive_fields: list) -> dict:
        """
        Encrypt specific fields in a dictionary
        
        Args:
            data: Dictionary containing data to encrypt
            sensitive_fields: List of field names to encrypt
            
        Returns:
            Dictionary with specified fields encrypted
        """
        encrypted_data = data.copy()
        
        for field in sensitive_fields:
            if field in encrypted_data and encrypted_data[field]:
                try:
                    encrypted_data[field] = self.encrypt(str(encrypted_data[field]))
                except Exception as e:
                    logger.warning(f"Failed to encrypt field {field}: {e}")
                    
        return encrypted_data

    def decrypt_dict_fields(self, data: dict, sensitive_fields: list) -> dict:
        """
        Decrypt specific fields in a dictionary
        
        Args:
            data: Dictionary containing encrypted data
            sensitive_fields: List of field names to decrypt
            
        Returns:
            Dictionary with specified fields decrypted
        """
        decrypted_data = data.copy()
        
        for field in sensitive_fields:
            if field in decrypted_data and decrypted_data[field]:
                try:
                    decrypted_data[field] = self.decrypt(str(decrypted_data[field]))
                except Exception as e:
                    logger.warning(f"Failed to decrypt field {field}: {e}")
                    decrypted_data[field] = "[DECRYPTION_FAILED]"
                    
        return decrypted_data

    def get_sensitive_fields_for_auth_type(self, auth_type: str) -> list:
        """
        Get list of sensitive fields for a given authentication type
        
        Args:
            auth_type: Authentication type (e.g., 'bearer_token', 'api_key')
            
        Returns:
            List of field names that should be encrypted for this auth type
        """
        sensitive_fields_map = {
            "bearer_token": ["bearer_token", "token"],
            "api_key": ["api_key", "key"],
            "basic_auth": ["password"],
            "oauth2": ["oauth2_client_secret", "client_secret", "refresh_token"],
            "custom_header": ["custom_headers"],
        }
        
        # Always encrypt these fields regardless of auth type
        always_encrypt = ["password", "secret", "token", "key"]
        
        fields = sensitive_fields_map.get(auth_type, [])
        fields.extend(always_encrypt)
        
        return list(set(fields))  # Remove duplicates


# Singleton instance
_encryption_service_instance = None


def get_encryption_service() -> EncryptionService:
    """Get singleton instance of the encryption service"""
    global _encryption_service_instance
    if _encryption_service_instance is None:
        _encryption_service_instance = EncryptionService()
    return _encryption_service_instance
