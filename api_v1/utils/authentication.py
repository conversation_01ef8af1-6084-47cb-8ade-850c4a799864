"""
Unified Authentication Utilities

Consolidates authentication header building and credential handling
from across the codebase into reusable utility functions.
"""

import base64
import logging
from typing import Dict, Any, Optional

from models.api import AuthenticationType

logger = logging.getLogger(__name__)


class AuthenticationHelper:
    """
    Unified authentication helper for building headers and handling credentials.
    
    Consolidates authentication logic from:
    - api_service.py
    - external_api_service.py
    - card_service.py
    """

    @staticmethod
    def build_auth_headers(auth_config: Dict[str, Any]) -> Dict[str, str]:
        """
        Build authentication headers from configuration
        
        Args:
            auth_config: Authentication configuration dictionary
            
        Returns:
            Dictionary of headers to add to requests
        """
        headers = {}
        
        if not auth_config:
            return headers

        auth_type = auth_config.get("type")

        try:
            if auth_type == AuthenticationType.API_KEY:
                api_key = auth_config.get("api_key")
                header_name = auth_config.get("api_key_header", "X-API-Key")
                if api_key:
                    headers[header_name] = api_key

            elif auth_type == AuthenticationType.BEARER_TOKEN:
                token = auth_config.get("bearer_token")
                if token:
                    headers["Authorization"] = f"Bearer {token}"

            elif auth_type == AuthenticationType.BASIC_AUTH:
                username = auth_config.get("username")
                password = auth_config.get("password")
                if username and password:
                    credentials = base64.b64encode(
                        f"{username}:{password}".encode()
                    ).decode()
                    headers["Authorization"] = f"Basic {credentials}"

            elif auth_type == AuthenticationType.CUSTOM_HEADER:
                custom_headers = auth_config.get("custom_headers", {})
                headers.update(custom_headers)

            elif auth_type == AuthenticationType.OAUTH2:
                # OAuth2 typically uses bearer token format
                access_token = auth_config.get("access_token")
                if access_token:
                    headers["Authorization"] = f"Bearer {access_token}"

        except Exception as e:
            logger.warning(f"Failed to build auth headers for type {auth_type}: {e}")

        return headers

    @staticmethod
    def build_default_headers(operation: Optional[str] = None) -> Dict[str, str]:
        """
        Build default headers for API requests
        
        Args:
            operation: Optional operation name for context
            
        Returns:
            Dictionary of default headers
        """
        headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "origin": "https://ronaldo-club.to",
            "referer": "https://ronaldo-club.to/store/cards/hq",
            "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
            "sec-ch-ua-mobile": "?1",
            "sec-ch-ua-platform": '"Android"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "sec-gpc": "1",
            "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
                         "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36",
        }
        # Do NOT set Content-Type or Content-Length here; per-request code will set them as needed
        return headers

    @staticmethod
    def build_cookies(credentials: Dict[str, Any]) -> Dict[str, str]:
        """
        Build cookies from credentials
        
        Args:
            credentials: Credentials dictionary
            
        Returns:
            Dictionary of cookies
        """
        cookies = {}
        
        if not credentials:
            return cookies
            
        # Handle session cookies
        session_cookies = credentials.get("session_cookies", {})
        if session_cookies:
            cookies.update(session_cookies)
            
        # Handle login token as cookie
        login_token = credentials.get("login_token")
        if login_token:
            cookies["loginToken"] = login_token
            
        return cookies

    @staticmethod
    def validate_auth_config(auth_config: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """
        Validate authentication configuration
        
        Args:
            auth_config: Authentication configuration to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not auth_config:
            return True, None  # No auth is valid
            
        auth_type = auth_config.get("type")
        
        if not auth_type:
            return False, "Authentication type is required"
            
        if auth_type == AuthenticationType.API_KEY:
            if not auth_config.get("api_key"):
                return False, "API key is required for API key authentication"
                
        elif auth_type == AuthenticationType.BEARER_TOKEN:
            if not auth_config.get("bearer_token"):
                return False, "Bearer token is required for bearer token authentication"
                
        elif auth_type == AuthenticationType.BASIC_AUTH:
            if not auth_config.get("username") or not auth_config.get("password"):
                return False, "Username and password are required for basic authentication"
                
        elif auth_type == AuthenticationType.OAUTH2:
            if not auth_config.get("access_token"):
                return False, "Access token is required for OAuth2 authentication"
                
        return True, None

    @staticmethod
    def get_auth_method_display_name(auth_type: str) -> str:
        """
        Get human-readable display name for authentication method
        
        Args:
            auth_type: Authentication type
            
        Returns:
            Human-readable display name
        """
        display_names = {
            AuthenticationType.NONE: "No Authentication",
            AuthenticationType.API_KEY: "API Key",
            AuthenticationType.BEARER_TOKEN: "Bearer Token",
            AuthenticationType.BASIC_AUTH: "Basic Authentication",
            AuthenticationType.OAUTH2: "OAuth 2.0",
            AuthenticationType.CUSTOM_HEADER: "Custom Headers",
        }
        
        return display_names.get(auth_type, auth_type.title())

    @staticmethod
    def mask_sensitive_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Mask sensitive data for logging/display purposes
        
        Args:
            data: Data dictionary that may contain sensitive information
            
        Returns:
            Dictionary with sensitive fields masked
        """
        masked_data = data.copy()
        
        sensitive_fields = [
            "password", "token", "key", "secret", "bearer_token", 
            "api_key", "access_token", "refresh_token", "client_secret"
        ]
        
        for field in sensitive_fields:
            if field in masked_data and masked_data[field]:
                value = str(masked_data[field])
                if len(value) > 8:
                    # Use last 3 alphanumeric characters for masking consistency
                    tail_alnum = ''.join([c for c in value if c.isalnum()])[-3:]
                    tail = tail_alnum if tail_alnum else value[-3:]
                    masked_data[field] = f"{value[:4]}...{tail}"
                else:
                    masked_data[field] = "***"
        
        return masked_data


# Singleton instance
_auth_helper_instance = None


def get_auth_helper() -> AuthenticationHelper:
    """Get singleton instance of the authentication helper"""
    global _auth_helper_instance
    if _auth_helper_instance is None:
        _auth_helper_instance = AuthenticationHelper()
    return _auth_helper_instance
