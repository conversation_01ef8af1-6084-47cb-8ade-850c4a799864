"""
Unified Error Handling Utilities

Consolidates error handling patterns from across the codebase
into reusable utilities with consistent error categorization and recovery.
"""

import logging
from enum import Enum
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from ..core.exceptions import (
    APIv1Exception,
    APIConfigurationError,
    AuthenticationError,
    HTTPClientError,
    ValidationError,
    HealthCheckError,
    SecurityError,
    RateLimitError,
    DatabaseError,
    ExternalAPIError,
)

logger = logging.getLogger(__name__)


class ErrorCategory(str, Enum):
    """Error categories for classification"""
    CONFIGURATION = "configuration"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    NETWORK = "network"
    DATABASE = "database"
    SECURITY = "security"
    RATE_LIMIT = "rate_limit"
    SYSTEM = "system"
    EXTERNAL_API = "external_api"


class ErrorSeverity(str, Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorInfo:
    """Structured error information"""
    code: str
    category: ErrorCategory
    severity: ErrorSeverity
    title: str
    message: str
    recovery_suggestions: List[str]
    technical_details: Optional[str] = None
    retry_after: Optional[int] = None


class UnifiedErrorHandler:
    """
    Unified error handler for consistent error processing across all services.
    
    Consolidates error handling from:
    - services/api_config_errors.py
    - middleware/error_handling.py
    - Various service error handling patterns
    """

    def __init__(self):
        self._error_catalog = self._build_error_catalog()

    def _build_error_catalog(self) -> Dict[str, ErrorInfo]:
        """Build comprehensive error catalog"""
        return {
            # Configuration errors
            "CONFIG_NOT_FOUND": ErrorInfo(
                code="CONFIG_NOT_FOUND",
                category=ErrorCategory.CONFIGURATION,
                severity=ErrorSeverity.HIGH,
                title="Configuration Not Found",
                message="The requested API configuration could not be found.",
                recovery_suggestions=[
                    "Check if the configuration name is correct",
                    "Verify the configuration exists in the system",
                    "Create the configuration if it doesn't exist",
                ],
            ),
            "INVALID_CONFIG_FORMAT": ErrorInfo(
                code="INVALID_CONFIG_FORMAT",
                category=ErrorCategory.CONFIGURATION,
                severity=ErrorSeverity.MEDIUM,
                title="Invalid Configuration Format",
                message="The configuration format is invalid or contains errors.",
                recovery_suggestions=[
                    "Check the configuration syntax",
                    "Validate required fields are present",
                    "Use a configuration template as reference",
                ],
            ),
            
            # Authentication errors
            "INVALID_CREDENTIALS": ErrorInfo(
                code="INVALID_CREDENTIALS",
                category=ErrorCategory.AUTHENTICATION,
                severity=ErrorSeverity.HIGH,
                title="Invalid Credentials",
                message="The provided credentials are invalid or expired.",
                recovery_suggestions=[
                    "Check your API key or token",
                    "Verify credentials haven't expired",
                    "Contact administrator for new credentials",
                ],
            ),
            "TOKEN_EXPIRED": ErrorInfo(
                code="TOKEN_EXPIRED",
                category=ErrorCategory.AUTHENTICATION,
                severity=ErrorSeverity.MEDIUM,
                title="Token Expired",
                message="The authentication token has expired.",
                recovery_suggestions=[
                    "Refresh your authentication token",
                    "Re-authenticate with the service",
                    "Check token expiration settings",
                ],
            ),
            
            # Network errors
            "CONNECTION_TIMEOUT": ErrorInfo(
                code="CONNECTION_TIMEOUT",
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.MEDIUM,
                title="Connection Timeout",
                message="The request timed out while connecting to the API.",
                recovery_suggestions=[
                    "Check your internet connection",
                    "Verify the API endpoint is accessible",
                    "Try again in a few moments",
                    "Increase timeout settings if needed",
                ],
            ),
            "CONNECTION_REFUSED": ErrorInfo(
                code="CONNECTION_REFUSED",
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.HIGH,
                title="Connection Refused",
                message="The connection to the API was refused.",
                recovery_suggestions=[
                    "Check if the API service is running",
                    "Verify the URL and port are correct",
                    "Check firewall settings",
                    "Contact the API provider",
                ],
            ),
            
            # Rate limiting errors
            "RATE_LIMIT_EXCEEDED": ErrorInfo(
                code="RATE_LIMIT_EXCEEDED",
                category=ErrorCategory.RATE_LIMIT,
                severity=ErrorSeverity.MEDIUM,
                title="Rate Limit Exceeded",
                message="Too many requests have been made. Please slow down.",
                recovery_suggestions=[
                    "Wait before making more requests",
                    "Implement request throttling",
                    "Check rate limit settings",
                    "Consider upgrading your plan",
                ],
            ),
            
            # Validation errors
            "VALIDATION_FAILED": ErrorInfo(
                code="VALIDATION_FAILED",
                category=ErrorCategory.VALIDATION,
                severity=ErrorSeverity.MEDIUM,
                title="Validation Failed",
                message="The provided data failed validation checks.",
                recovery_suggestions=[
                    "Check required fields are provided",
                    "Verify data formats are correct",
                    "Review validation rules",
                    "Use valid example data as reference",
                ],
            ),
            
            # Database errors
            "DATABASE_ERROR": ErrorInfo(
                code="DATABASE_ERROR",
                category=ErrorCategory.DATABASE,
                severity=ErrorSeverity.CRITICAL,
                title="Database Error",
                message="A database operation failed.",
                recovery_suggestions=[
                    "Try the operation again",
                    "Check database connectivity",
                    "Contact system administrator",
                ],
            ),
            
            # Security errors
            "PERMISSION_DENIED": ErrorInfo(
                code="PERMISSION_DENIED",
                category=ErrorCategory.SECURITY,
                severity=ErrorSeverity.HIGH,
                title="Permission Denied",
                message="You don't have permission to perform this operation.",
                recovery_suggestions=[
                    "Check your user permissions",
                    "Contact administrator for access",
                    "Verify you're using the correct account",
                ],
            ),
        }

    def categorize_error(self, error: Exception) -> ErrorInfo:
        """Categorize an exception and return appropriate error info"""
        error_str = str(error).lower()
        error_type = type(error).__name__

        # Handle known API v1 exceptions
        if isinstance(error, APIConfigurationError):
            return self._error_catalog.get("CONFIG_NOT_FOUND", self._get_unknown_error(error))
        elif isinstance(error, AuthenticationError):
            return self._error_catalog.get("INVALID_CREDENTIALS", self._get_unknown_error(error))
        elif isinstance(error, ValidationError):
            return self._error_catalog.get("VALIDATION_FAILED", self._get_unknown_error(error))
        elif isinstance(error, RateLimitError):
            return self._error_catalog.get("RATE_LIMIT_EXCEEDED", self._get_unknown_error(error))
        elif isinstance(error, DatabaseError):
            return self._error_catalog.get("DATABASE_ERROR", self._get_unknown_error(error))
        elif isinstance(error, SecurityError):
            return self._error_catalog.get("PERMISSION_DENIED", self._get_unknown_error(error))

        # Pattern matching for other exceptions
        if "timeout" in error_str or "timed out" in error_str:
            return self._error_catalog["CONNECTION_TIMEOUT"]
        elif "connection refused" in error_str or "refused" in error_str:
            return self._error_catalog["CONNECTION_REFUSED"]
        elif "401" in error_str or "unauthorized" in error_str:
            return self._error_catalog["INVALID_CREDENTIALS"]
        elif "403" in error_str or "forbidden" in error_str:
            return self._error_catalog["PERMISSION_DENIED"]
        elif "rate limit" in error_str or "too many" in error_str:
            return self._error_catalog["RATE_LIMIT_EXCEEDED"]
        elif error_type in ["ValueError", "ValidationError"]:
            return self._error_catalog["VALIDATION_FAILED"]
        elif "database" in error_str or "db" in error_str:
            return self._error_catalog["DATABASE_ERROR"]
        else:
            return self._get_unknown_error(error)

    def _get_unknown_error(self, error: Exception) -> ErrorInfo:
        """Get error info for unknown errors"""
        return ErrorInfo(
            code="UNKNOWN_ERROR",
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.MEDIUM,
            title="Unexpected Error",
            message=f"An unexpected error occurred: {str(error)}",
            recovery_suggestions=[
                "Try the operation again",
                "Check your input for any issues",
                "Contact support if the problem persists",
            ],
            technical_details=f"Error type: {type(error).__name__}, Message: {str(error)}",
        )

    def format_error_message(
        self, 
        error_info: ErrorInfo, 
        include_technical: bool = False,
        format_type: str = "html"
    ) -> str:
        """Format error information into a user-friendly message"""
        severity_icons = {
            ErrorSeverity.LOW: "ℹ️",
            ErrorSeverity.MEDIUM: "⚠️",
            ErrorSeverity.HIGH: "❌",
            ErrorSeverity.CRITICAL: "🚨",
        }

        icon = severity_icons.get(error_info.severity, "❌")

        if format_type == "html":
            message = f"{icon} <b>{error_info.title}</b>\n\n"
            message += f"{error_info.message}\n\n"

            if error_info.recovery_suggestions:
                message += "💡 <b>How to fix this:</b>\n"
                for suggestion in error_info.recovery_suggestions:
                    message += f"• {suggestion}\n"
                message += "\n"

            if include_technical and error_info.technical_details:
                message += f"🔧 <b>Technical Details:</b>\n{error_info.technical_details}\n\n"
        else:
            # Plain text format
            message = f"{icon} {error_info.title}\n\n"
            message += f"{error_info.message}\n\n"

            if error_info.recovery_suggestions:
                message += "How to fix this:\n"
                for suggestion in error_info.recovery_suggestions:
                    message += f"• {suggestion}\n"
                message += "\n"

            if include_technical and error_info.technical_details:
                message += f"Technical Details:\n{error_info.technical_details}\n\n"

        return message.strip()

    def should_retry(self, error_info: ErrorInfo) -> bool:
        """Determine if an operation should be retried based on error type"""
        retry_categories = {
            ErrorCategory.NETWORK,
            ErrorCategory.RATE_LIMIT,
            ErrorCategory.SYSTEM,
        }
        return error_info.category in retry_categories and error_info.severity != ErrorSeverity.CRITICAL


# Singleton instance
_error_handler_instance = None


def get_error_handler() -> UnifiedErrorHandler:
    """Get singleton instance of the unified error handler"""
    global _error_handler_instance
    if _error_handler_instance is None:
        _error_handler_instance = UnifiedErrorHandler()
    return _error_handler_instance
