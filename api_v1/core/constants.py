"""
API v1 Core Constants

Defines constants used throughout the API v1 system.
"""

# API Version
API_VERSION = "1.0.0"

# Default timeouts (in seconds)
DEFAULT_REQUEST_TIMEOUT = 30
DEFAULT_HEALTH_CHECK_TIMEOUT = 10
DEFAULT_CONNECTION_TIMEOUT = 5

# Retry configuration
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAYS = [1, 2, 4]  # Exponential backoff

# Cache TTL (in seconds)
DEFAULT_CACHE_TTL = 300  # 5 minutes
CONFIG_CACHE_TTL = 600   # 10 minutes

# Rate limiting
DEFAULT_RATE_LIMIT_PER_MINUTE = 60
DEFAULT_RATE_LIMIT_BURST = 10

# Pagination
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100

# Health check intervals (in seconds)
HEALTH_CHECK_INTERVAL = 300      # 5 minutes
HEALTH_CHECK_FAILURE_THRESHOLD = 3
HEALTH_CHECK_SUCCESS_THRESHOLD = 2

# Encryption
ENCRYPTION_KEY_LENGTH = 32
ENCRYPTION_SALT_LENGTH = 16

# Logging
LOG_MAX_BODY_SIZE = 1024  # Maximum size of request/response body to log
LOG_SENSITIVE_FIELDS = [
    "password", "token", "key", "secret", "bearer_token",
    "api_key", "access_token", "refresh_token", "client_secret"
]

# HTTP Status Codes
HTTP_SUCCESS_CODES = range(200, 300)
HTTP_CLIENT_ERROR_CODES = range(400, 500)
HTTP_SERVER_ERROR_CODES = range(500, 600)

# Default headers for external API requests
DEFAULT_API_HEADERS = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9",
    "content-type": "application/json",
    "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
                 "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36",
}

# API Configuration Categories
API_CATEGORIES = [
    "ecommerce",
    "payment",
    "authentication",
    "analytics",
    "notification",
    "storage",
    "general",
]

# API Environments
API_ENVIRONMENTS = [
    "development",
    "staging", 
    "production",
    "testing",
]

# Error messages
ERROR_MESSAGES = {
    "CONFIG_NOT_FOUND": "API configuration not found",
    "INVALID_AUTH": "Invalid authentication configuration",
    "REQUEST_TIMEOUT": "Request timed out",
    "CONNECTION_FAILED": "Failed to connect to API",
    "RATE_LIMIT_EXCEEDED": "Rate limit exceeded",
    "VALIDATION_FAILED": "Data validation failed",
    "ENCRYPTION_FAILED": "Failed to encrypt/decrypt data",
    "HEALTH_CHECK_FAILED": "Health check failed",
    "PERMISSION_DENIED": "Permission denied",
    "UNKNOWN_ERROR": "An unknown error occurred",
}

# Success messages
SUCCESS_MESSAGES = {
    "CONFIG_CREATED": "API configuration created successfully",
    "CONFIG_UPDATED": "API configuration updated successfully", 
    "CONFIG_DELETED": "API configuration deleted successfully",
    "HEALTH_CHECK_PASSED": "Health check passed",
    "REQUEST_SUCCESSFUL": "Request completed successfully",
}

# File extensions for import/export
SUPPORTED_EXPORT_FORMATS = [".json", ".yaml", ".yml"]
SUPPORTED_IMPORT_FORMATS = [".json", ".yaml", ".yml"]

# Database collection names
COLLECTIONS = {
    "API_CONFIGS": "api_configurations",
    "API_ENDPOINTS": "api_endpoints", 
    "API_HEALTH": "api_health_status",
    "API_METRICS": "api_usage_metrics",
    "API_LOGS": "api_request_logs",
    "API_CREDENTIALS": "api_credentials",
    "API_AUDIT": "api_audit_logs",
    "API_SECURITY_EVENTS": "api_security_events",
}

# Monitoring and alerting thresholds
MONITORING_THRESHOLDS = {
    "RESPONSE_TIME_WARNING": 1000,    # 1 second
    "RESPONSE_TIME_CRITICAL": 5000,   # 5 seconds
    "ERROR_RATE_WARNING": 0.05,       # 5%
    "ERROR_RATE_CRITICAL": 0.10,      # 10%
    "AVAILABILITY_WARNING": 0.95,     # 95%
    "AVAILABILITY_CRITICAL": 0.90,    # 90%
}

# Security settings
SECURITY_SETTINGS = {
    "PASSWORD_MIN_LENGTH": 8,
    "TOKEN_EXPIRY_HOURS": 24,
    "MAX_LOGIN_ATTEMPTS": 5,
    "LOCKOUT_DURATION_MINUTES": 30,
    "SESSION_TIMEOUT_MINUTES": 60,
}

# Feature flags
FEATURE_FLAGS = {
    "ENABLE_CACHING": True,
    "ENABLE_RATE_LIMITING": True,
    "ENABLE_HEALTH_MONITORING": True,
    "ENABLE_AUDIT_LOGGING": True,
    "ENABLE_ENCRYPTION": True,
    "ENABLE_ANALYTICS": True,
}
