"""
API v1 Core Exceptions

Defines all custom exceptions used throughout the API v1 system.
Provides consistent error handling and messaging.
"""


class APIv1Exception(Exception):
    """Base exception for all API v1 related errors"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "API_ERROR"
        self.details = details or {}


class APIConfigurationError(APIv1Exception):
    """Raised when there are issues with API configuration"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "CONFIG_ERROR", details)


class AuthenticationError(APIv1Exception):
    """Raised when authentication fails"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "AUTH_ERROR", details)


class EncryptionError(APIv1Exception):
    """Raised when encryption/decryption operations fail"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "ENCRYPTION_ERROR", details)


class HTTPClientError(APIv1Exception):
    """Raised when HTTP client operations fail"""
    
    def __init__(self, message: str, status_code: int = None, details: dict = None):
        super().__init__(message, "HTTP_ERROR", details)
        self.status_code = status_code


class ValidationError(APIv1Exception):
    """Raised when data validation fails"""
    
    def __init__(self, message: str, field: str = None, details: dict = None):
        super().__init__(message, "VALIDATION_ERROR", details)
        self.field = field


class HealthCheckError(APIv1Exception):
    """Raised when health check operations fail"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "HEALTH_CHECK_ERROR", details)


class SecurityError(APIv1Exception):
    """Raised when security-related operations fail"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "SECURITY_ERROR", details)


class RateLimitError(APIv1Exception):
    """Raised when rate limits are exceeded"""
    
    def __init__(self, message: str, retry_after: int = None, details: dict = None):
        super().__init__(message, "RATE_LIMIT_ERROR", details)
        self.retry_after = retry_after


class DatabaseError(APIv1Exception):
    """Raised when database operations fail"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "DATABASE_ERROR", details)


class ExternalAPIError(APIv1Exception):
    """Raised when external API calls fail"""
    
    def __init__(self, message: str, api_name: str = None, details: dict = None):
        super().__init__(message, "EXTERNAL_API_ERROR", details)
        self.api_name = api_name
