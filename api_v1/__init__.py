"""
API v1 Package

This package contains all API v1 related functionality organized into logical modules:

- core: Core API functionality and base classes
- services: Business logic and service implementations
- handlers: Request handlers and controllers
- models: Data models and schemas
- utils: Utility functions and helpers
- config: Configuration management
- tests: Test suites for API functionality

This reorganization consolidates duplicate code, improves maintainability,
and provides clear separation of concerns.
"""

__version__ = "1.0.0"
__author__ = "Bot v2 Team"

# Core imports for easy access
from .core import *
from .services import *

__all__ = [
    "core",
    "services",
    "handlers",
    "models",
    "utils",
    "config",
]
