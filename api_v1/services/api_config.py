"""
Unified API Configuration Service

Consolidates functionality from api_config_service.py and api_service.py
into a single, well-organized service with clear separation of concerns.
"""

from __future__ import annotations

import asyncio
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass
from enum import Enum

from bson import ObjectId
from database.connection import get_collection
from models.base import now_utc
from models.api import (
    APIConfiguration,
    APIEndpoint as APIEndpointModel,
    APIHealthStatus,
    APIUsageMetrics,
    APIRequestLog,
    APICredential,
    APIAuditLog,
    AuthenticationType,
    AuthenticationConfig,
    APIEnvironment,
    APIStatus,
    HTTPMethod,
)
from config.settings import get_settings
from utils.validation import ValidationError
from ..utils.encryption import EncryptionService
from ..core.exceptions import APIConfigurationError

logger = logging.getLogger(__name__)


class ConfigSource(str, Enum):
    """Configuration source priority"""

    ADMIN_PANEL = "admin_panel"
    DATABASE = "database"
    ENVIRONMENT = "environment"
    DEFAULT = "default"


@dataclass
class APIEndpointConfig:
    """API endpoint configuration"""

    name: str
    url: str
    method: str = "GET"
    timeout: int = 30
    retry_count: int = 3
    retry_delays: List[int] = None

    def __post_init__(self):
        if self.retry_delays is None:
            self.retry_delays = [1, 2, 4]


@dataclass
class APICredentials:
    """API authentication credentials"""

    login_token: str = ""
    session_cookies: Dict[str, str] = None
    headers: Dict[str, str] = None
    auth_profile_id: Optional[str] = None
    use_profile: bool = False
    credential_overrides: Dict[str, Any] = None

    def __post_init__(self):
        if self.session_cookies is None:
            self.session_cookies = {}
        if self.headers is None:
            self.headers = {}
        if self.credential_overrides is None:
            self.credential_overrides = {}


@dataclass
class UnifiedAPIConfiguration:
    """Complete API configuration combining both legacy formats"""

    service_name: str
    base_url: str
    endpoints: Dict[str, APIEndpointConfig]
    credentials: APICredentials
    enabled: bool = True
    last_updated: datetime = None
    source: ConfigSource = ConfigSource.DEFAULT
    display_name: str = ""
    description: str = ""
    category: str = "general"
    tags: List[str] = None
    environment: str = "development"
    version: str = "1.0"
    health_check_endpoint: str = ""
    documentation_url: str = ""

    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.last_updated is None:
            self.last_updated = now_utc()


class UnifiedAPIConfigurationService:
    """
    Unified API Configuration Service

    Consolidates functionality from both legacy services:
    - api_config_service.py (dataclass-based, admin UI focused)
    - api_service.py (Pydantic-based, database focused)

    Provides a single interface for all API configuration operations.
    """

    def __init__(self):
        self.settings = get_settings()
        self.encryption = EncryptionService()

        # Database collections
        self.api_configs = get_collection("api_configurations")
        self.api_endpoints = get_collection("api_endpoints")
        self.api_health = get_collection("api_health_status")
        self.api_metrics = get_collection("api_usage_metrics")
        self.api_logs = get_collection("api_request_logs")
        self.api_credentials = get_collection("api_credentials")
        self.api_audit = get_collection("api_audit_logs")

        # Configuration cache and locks
        self._config_cache = {}
        self._all_configs_cache = {}
        self._config_lock = asyncio.Lock()
        self._cache_ttl = 300  # 5 minutes

    async def get_api_config_by_name(
        self, name: str, decrypt_sensitive: bool = False
    ) -> Optional[APIConfiguration]:
        """Fetch an API configuration by its name (case-insensitive).

        Provides compatibility with legacy code that retrieved configs by name.
        """
        try:
            # Prefer an exact, case-insensitive match via query
            query = {"name": {"$regex": f"^{name}$", "$options": "i"}, "is_deleted": {"$ne": True}}
            doc = await self.api_configs.find_one(query)
            if not doc:
                # Fallback: search using list_api_configs
                configs, _ = await self.list_api_configs(page=1, per_page=50, search=name)
                match = next((c for c in configs if (c.name or "").lower() == name.lower()), None)
                if not match:
                    return None
                # Fetch full config by ID to apply decryption/caching behavior
                return await self.get_api_config(str(match.id), decrypt_sensitive=decrypt_sensitive)

            api_config = APIConfiguration.from_mongo(doc)
            if decrypt_sensitive and api_config.authentication:
                api_config.authentication = self._decrypt_auth_config(api_config.authentication)
            return api_config
        except Exception as e:
            logger.error(f"Failed to get API configuration by name '{name}': {e}")
            return None

    async def create_api_config(
        self, config_data: Dict[str, Any], created_by: str
    ) -> APIConfiguration:
        """Create a new API configuration"""
        try:
            # Encrypt sensitive authentication data
            auth_config = config_data.get("authentication", {})
            if auth_config:
                auth_config = self._encrypt_auth_config(auth_config)
                config_data["authentication"] = auth_config

            # Set system fields
            config_data["created_by"] = created_by
            config_data["created_at"] = now_utc()

            # Create and validate the configuration
            api_config = APIConfiguration(**config_data)

            # Insert into database
            result = await self.api_configs.insert_one(api_config.to_mongo())
            api_config.id = result.inserted_id

            # Create initial health status
            await self._create_initial_health_status(str(api_config.id))

            # Log the creation
            await self._audit_log(
                operation="create",
                resource_type="api_config",
                resource_id=str(api_config.id),
                actor_id=created_by,
                new_values={"name": api_config.name, "base_url": api_config.base_url},
                success=True,
            )

            # Clear cache
            self._invalidate_caches()

            logger.info(
                f"Created API configuration: {api_config.name} (ID: {api_config.id})"
            )
            return api_config

        except Exception as e:
            logger.error(f"Failed to create API configuration: {e}")
            await self._audit_log(
                operation="create",
                resource_type="api_config",
                resource_id="",
                actor_id=created_by,
                new_values=config_data.get("name", "unknown"),
                success=False,
                error_message=str(e),
            )
            raise APIConfigurationError(f"Failed to create API configuration: {e}")

    async def get_api_config(
        self,
        config_id: str,
        decrypt_sensitive: bool = False,
        as_unified: bool = False,
    ) -> Optional[Union[APIConfiguration, UnifiedAPIConfiguration]]:
        """Get API configuration by ID or name.

        Args:
            config_id: Document `_id` or service name.
            decrypt_sensitive: Whether to decrypt stored secrets.
            as_unified: Force returning the unified dataclass representation.

        Returns:
            APIConfiguration when working with raw database models, or
            UnifiedAPIConfiguration when `as_unified` is True or a name lookup is performed.
        """

        try:
            cache_key = f"{config_id}_{int(decrypt_sensitive)}_{int(as_unified)}"
            cached_entry = self._config_cache.get(cache_key)
            if cached_entry:
                cached_config, cached_time = cached_entry
                if (datetime.now() - cached_time).seconds < self._cache_ttl:
                    return cached_config

            config_doc: Optional[Dict[str, Any]] = None

            normalized_id = self._normalize_object_id(config_id) if not as_unified else None
            if normalized_id is not None:
                config_doc = await self.api_configs.find_one(
                    {"_id": normalized_id, "is_deleted": {"$ne": True}}
                )

            if config_doc is None:
                config_doc = await self.api_configs.find_one(
                    {
                        "name": {
                            "$regex": f"^{re.escape(config_id)}$",
                            "$options": "i",
                        },
                        "is_deleted": {"$ne": True},
                    }
                )
                if config_doc is None:
                    return None
                as_unified = True

            api_config = APIConfiguration.from_mongo(config_doc)

            if as_unified:
                result = await self._to_unified_config(
                    api_config, decrypt_sensitive=decrypt_sensitive
                )
            else:
                if decrypt_sensitive and api_config.authentication:
                    decrypted_auth = self._decrypt_auth_config(api_config.authentication)
                    api_config.authentication = AuthenticationConfig(**decrypted_auth)
                result = api_config

            self._config_cache[cache_key] = (result, datetime.now())
            return result

        except Exception as e:
            logger.error(f"Failed to get API configuration {config_id}: {e}")
            return None

    async def list_api_configs(
        self,
        page: int = 1,
        per_page: int = 20,
        search: Optional[str] = None,
        status: Optional[APIStatus] = None,
        environment: Optional[APIEnvironment] = None,
    ) -> Tuple[List[APIConfiguration], int]:
        """List API configurations with filtering and pagination"""
        try:
            # Build query
            query = {}
            if search:
                query["$or"] = [
                    {"name": {"$regex": search, "$options": "i"}},
                    {"description": {"$regex": search, "$options": "i"}},
                    {"base_url": {"$regex": search, "$options": "i"}},
                ]
            if status:
                query["status"] = status.value
            if environment:
                query["environment"] = environment.value

            # Get total count
            total = await self.api_configs.count_documents(query)

            # Get paginated results
            skip = (page - 1) * per_page
            cursor = self.api_configs.find(query).skip(skip).limit(per_page)

            configs = []
            # Support both Motor cursors and in-memory simulation cursors
            try:
                docs = await cursor.to_list(length=per_page)
            except TypeError:
                # Fallback for any cursor lacking to_list signature
                docs = []
            for doc in docs:
                try:
                    config = APIConfiguration.from_mongo(doc)
                    configs.append(config)
                except Exception as e:
                    logger.warning(f"Failed to parse config document: {e}")
                    continue

            return configs, total

        except Exception as e:
            logger.error(f"Failed to list API configurations: {e}")
            return [], 0

    async def get_all_configurations(
        self,
        include_inactive: bool = True,
        decrypt_sensitive: bool = False,
        refresh_cache: bool = False,
    ) -> Dict[str, UnifiedAPIConfiguration]:
        """Retrieve all API configurations as unified dataclasses."""
        try:
            cache_key = f"all_{int(include_inactive)}_{int(decrypt_sensitive)}"
            cached_entry = self._all_configs_cache.get(cache_key)
            if cached_entry and not refresh_cache:
                configs_map, cached_time = cached_entry
                if (datetime.now() - cached_time).seconds < self._cache_ttl:
                    return configs_map

            query: Dict[str, Any] = {"is_deleted": {"$ne": True}}
            if not include_inactive:
                query["status"] = APIStatus.ACTIVE.value

            cursor = self.api_configs.find(query)
            config_docs = await self._collect_cursor_docs(cursor)

            unified_configs: Dict[str, UnifiedAPIConfiguration] = {}
            for doc in config_docs:
                try:
                    api_config = APIConfiguration.from_mongo(doc)
                except Exception as exc:
                    logger.warning(f"Failed to parse API configuration document: {exc}")
                    continue

                unified_config = await self._to_unified_config(
                    api_config, decrypt_sensitive=decrypt_sensitive
                )
                unified_configs[unified_config.service_name] = unified_config

            self._all_configs_cache[cache_key] = (unified_configs, datetime.now())
            return unified_configs

        except Exception as e:
            logger.error(f"Failed to get all API configurations: {e}")
            return {}

    async def save_api_config(
        self,
        config: Union[UnifiedAPIConfiguration, Dict[str, Any]],
        source: ConfigSource = ConfigSource.ADMIN_PANEL,
        user_id: Optional[str] = None,
    ) -> bool:
        """Persist unified configuration changes via the consolidated service."""

        if isinstance(config, dict):
            try:
                config = UnifiedAPIConfiguration(**config)
            except Exception as exc:
                logger.error(f"Invalid configuration payload: {exc}")
                return False

        if not isinstance(config, UnifiedAPIConfiguration):
            logger.error("save_api_config expects UnifiedAPIConfiguration payload")
            return False

        actor = str(user_id or "admin_ui")

        try:
            async with self._config_lock:
                name_query = {
                    "name": {"$regex": f"^{re.escape(config.service_name)}$", "$options": "i"},
                    "is_deleted": {"$ne": True},
                }
                existing_doc = await self.api_configs.find_one(name_query)

                status_value = (
                    APIStatus.ACTIVE.value if config.enabled else APIStatus.INACTIVE.value
                )

                auth_payload, has_auth_payload = self._build_auth_config_from_credentials(
                    config.credentials
                )

                default_headers = dict((config.credentials.headers or {}))
                session_cookies = dict((config.credentials.session_cookies or {}))
                environment_value = str(
                    config.environment or APIEnvironment.DEVELOPMENT.value
                )

                timestamp_now = now_utc()

                common_updates: Dict[str, Any] = {
                    "base_url": config.base_url,
                    "description": config.description or "",
                    "default_headers": default_headers,
                    "status": status_value,
                    "environment": environment_value,
                    "documentation_url": config.documentation_url or "",
                    "tags": list(config.tags or []),
                    "display_name": config.display_name or config.service_name,
                    "category": config.category or "general",
                    "version": config.version or "1.0",
                    "source": source.value,
                    "session_cookies": session_cookies,
                    "last_updated": config.last_updated or timestamp_now,
                }

                if config.health_check_endpoint is not None:
                    common_updates["health_check.endpoint"] = (
                        config.health_check_endpoint or ""
                    )

                # Determine whether authentication needs to be updated
                should_update_auth = has_auth_payload or not existing_doc
                if existing_doc and not has_auth_payload:
                    existing_auth = existing_doc.get("authentication", {}) or {}
                    use_profile_requested = bool(auth_payload.get("use_profile"))
                    existing_profile = bool(existing_auth.get("use_profile") and existing_auth.get("auth_profile_id"))
                    remove_profile = existing_profile and not use_profile_requested
                    if not remove_profile:
                        should_update_auth = False

                if should_update_auth:
                    common_updates["authentication"] = auth_payload

                if existing_doc is None:
                    create_payload = {
                        "name": config.service_name,
                        "base_url": config.base_url,
                        "description": config.description or "",
                        "authentication": auth_payload,
                        "default_headers": default_headers,
                        "status": status_value,
                        "environment": environment_value,
                        "documentation_url": config.documentation_url or "",
                        "tags": list(config.tags or []),
                    }

                    created = await self.create_api_config(create_payload, actor)
                    config_id = created.id

                    # Apply extended metadata not covered by create payload
                    post_create_updates = common_updates.copy()
                    for key in [
                        "authentication",
                        "base_url",
                        "default_headers",
                        "description",
                        "status",
                        "environment",
                        "documentation_url",
                        "tags",
                    ]:
                        post_create_updates.pop(key, None)

                    if post_create_updates:
                        post_create_updates.setdefault("updated_at", timestamp_now)
                        post_create_updates.setdefault("updated_by", actor)
                        await self.api_configs.update_one(
                            {"_id": config_id}, {"$set": post_create_updates}
                        )

                    cfg_id_str = str(config_id)
                else:
                    cfg_id = existing_doc.get("_id")
                    cfg_id_str = str(cfg_id)

                    update_payload = common_updates.copy()
                    update_payload["last_updated"] = timestamp_now
                    update_payload["updated_at"] = timestamp_now
                    update_payload["updated_by"] = actor

                    update_success = await self.update_api_config(
                        cfg_id_str, update_payload, actor
                    )
                    if not update_success:
                        return False

                # Persist endpoint definitions
                try:
                    await self._persist_endpoints(cfg_id_str, config)
                except Exception as exc:
                    logger.warning(
                        f"Failed to persist endpoints for {config.service_name}: {exc}"
                    )

                self._invalidate_caches()
                return True

        except Exception as e:
            logger.error(f"Failed to save API configuration {config.service_name}: {e}")
            return False

    def _encrypt_auth_config(self, auth_config: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive fields in authentication configuration"""
        encrypted_config = auth_config.copy()

        # Fields that need encryption
        sensitive_fields = [
            "api_key",
            "bearer_token",
            "password",
            "oauth2_client_secret",
        ]

        for field in sensitive_fields:
            if field in encrypted_config and encrypted_config[field]:
                encrypted_config[field] = self.encryption.encrypt(
                    encrypted_config[field]
                )

        return encrypted_config

    def _decrypt_auth_config(self, auth_config) -> Dict[str, Any]:
        """Decrypt sensitive fields in authentication configuration"""
        if hasattr(auth_config, "dict"):
            decrypted_config = auth_config.dict()
        else:
            decrypted_config = dict(auth_config)

        # Fields that need decryption
        sensitive_fields = [
            "api_key",
            "bearer_token",
            "password",
            "oauth2_client_secret",
        ]

        for field in sensitive_fields:
            if field in decrypted_config and decrypted_config[field]:
                decrypted_config[field] = self.encryption.decrypt(
                    decrypted_config[field]
                )

        return decrypted_config

    def _normalize_object_id(self, value: Any) -> Optional[ObjectId]:
        """Try to normalize arbitrary value into an ObjectId, if possible."""
        if isinstance(value, ObjectId):
            return value
        if isinstance(value, str) and ObjectId.is_valid(value):
            try:
                return ObjectId(value)
            except Exception:
                return None
        return None

    @staticmethod
    def _mask_secret(secret: Optional[str], reveal: bool) -> str:
        """Mask or reveal a sensitive string."""
        if not secret:
            return ""
        return secret if reveal else "***ENCRYPTED***"

    @staticmethod
    def _is_masked_secret(value: Optional[str]) -> bool:
        """Check if a value appears to be a placeholder for hidden secrets."""
        if not value:
            return False
        masked_tokens = {"***", "***ENCRYPTED***", "***REDACTED***", "[REDACTED]"}
        normalized = str(value).strip()
        if normalized in masked_tokens:
            return True
        return normalized.startswith("***") and normalized.endswith("***")

    @staticmethod
    async def _collect_cursor_docs(cursor, length: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect documents from a Motor cursor, resilient to mocks/stubs."""
        if cursor is None:
            return []
        try:
            return await cursor.to_list(length=length)
        except TypeError:
            docs: List[Dict[str, Any]] = []
            try:
                async for doc in cursor:
                    docs.append(doc)
            except TypeError:
                # Fall back to treating cursor as iterable (for mocks)
                try:
                    docs.extend(list(cursor))
                except Exception:
                    pass
            return docs

    @staticmethod
    def _build_endpoint_url(base_url: str, path: Optional[str]) -> str:
        """Construct a full endpoint URL from base URL and relative path."""
        if not base_url:
            return path or ""
        base = base_url.rstrip("/")
        if not path:
            return base
        if not path.startswith("/"):
            path = f"/{path}"
        return f"{base}{path}"

    async def _load_endpoints(
        self, api_config: APIConfiguration
    ) -> Dict[str, APIEndpointConfig]:
        """Load endpoint definitions for an API configuration."""
        query = {"api_config_id": str(api_config.id)}
        try:
            query["is_deleted"] = {"$ne": True}
        except Exception:
            # Ignore if dict is not mutable (unlikely)
            pass

        cursor = self.api_endpoints.find(query)
        endpoint_docs = await self._collect_cursor_docs(cursor)

        endpoints: Dict[str, APIEndpointConfig] = {}
        for doc in endpoint_docs:
            try:
                endpoint_model = APIEndpointModel.from_mongo(doc)
            except Exception as exc:
                logger.warning(f"Failed to parse endpoint document: {exc}")
                continue

            name = endpoint_model.name or endpoint_model.path.strip("/") or "endpoint"
            if name in endpoints:
                # Ensure unique keys when duplicate names exist
                suffix = 2
                while f"{name}_{suffix}" in endpoints:
                    suffix += 1
                name = f"{name}_{suffix}"

            method = (
                endpoint_model.method.value
                if isinstance(endpoint_model.method, Enum)
                else str(endpoint_model.method)
            )

            # Prefer endpoint-specific timeout if available
            timeout_config = getattr(endpoint_model, "custom_timeout", None)
            if timeout_config and hasattr(timeout_config, "total_timeout"):
                timeout = timeout_config.total_timeout
            else:
                timeout = getattr(getattr(api_config, "timeout", None), "total_timeout", 30)

            endpoints[name] = APIEndpointConfig(
                name=name,
                url=self._build_endpoint_url(api_config.base_url, endpoint_model.path),
                method=method,
                timeout=timeout,
            )

        return endpoints

    def _build_credentials(
        self,
        api_config: APIConfiguration,
        auth_data: Dict[str, Any],
        decrypt_sensitive: bool,
    ) -> APICredentials:
        """Convert authentication config into unified credential payload."""
        headers = dict(getattr(api_config, "default_headers", {}) or {})
        headers.update(auth_data.get("custom_headers") or {})

        session_cookies = {}
        candidate_session = getattr(api_config, "session_cookies", None)
        if isinstance(candidate_session, dict):
            session_cookies = dict(candidate_session)
        elif isinstance(auth_data, dict) and isinstance(auth_data.get("session_cookies"), dict):
            session_cookies = dict(auth_data["session_cookies"])

        credential_overrides: Dict[str, Any] = dict(
            getattr(api_config, "credential_overrides", {}) or {}
        )
        if isinstance(auth_data.get("credential_overrides"), dict):
            for key, value in auth_data["credential_overrides"].items():
                credential_overrides.setdefault(key, value)
        raw_type = auth_data.get("type") or AuthenticationType.NONE.value
        try:
            auth_type = AuthenticationType(raw_type)
        except Exception:
            auth_type = AuthenticationType.NONE

        login_token = ""

        if auth_type == AuthenticationType.API_KEY:
            login_token = self._mask_secret(auth_data.get("api_key"), decrypt_sensitive)
            credential_overrides["api_key_header"] = auth_data.get("api_key_header", "X-API-Key")
        elif auth_type == AuthenticationType.BEARER_TOKEN:
            login_token = self._mask_secret(auth_data.get("bearer_token"), decrypt_sensitive)
        elif auth_type == AuthenticationType.BASIC_AUTH:
            credential_overrides["username"] = auth_data.get("username", "")
            credential_overrides["password"] = self._mask_secret(
                auth_data.get("password"), decrypt_sensitive
            )
            login_token = credential_overrides["username"]
        elif auth_type == AuthenticationType.OAUTH2:
            credential_overrides.update(
                {
                    "client_id": auth_data.get("oauth2_client_id", ""),
                    "client_secret": self._mask_secret(
                        auth_data.get("oauth2_client_secret"), decrypt_sensitive
                    ),
                    "token_url": auth_data.get("oauth2_token_url", ""),
                    "scope": auth_data.get("oauth2_scope", ""),
                }
            )
            login_token = credential_overrides.get("client_id", "")

        profile_id = auth_data.get("auth_profile_id")
        use_profile = bool(auth_data.get("use_profile") or profile_id)

        return APICredentials(
            login_token=login_token,
            session_cookies=session_cookies,
            headers=headers,
            auth_profile_id=profile_id,
            use_profile=use_profile,
            credential_overrides=credential_overrides,
        )

    def _build_auth_config_from_credentials(
        self, credentials: Optional[APICredentials]
    ) -> Tuple[Dict[str, Any], bool]:
        """Prepare authentication payload and indicate if meaningful data is present."""
        base_headers: Dict[str, Any] = {}
        use_profile = False
        profile_id: Optional[str] = None
        credential_overrides: Dict[str, Any] = {}
        token: Optional[str] = None

        if credentials:
            base_headers = dict(credentials.headers or {})
            use_profile = bool(credentials.use_profile)
            profile_id = credentials.auth_profile_id
            credential_overrides = dict(credentials.credential_overrides or {})
            token = credentials.login_token

        auth_config: Dict[str, Any] = {
            "type": AuthenticationType.NONE.value,
            "custom_headers": base_headers,
            "session_cookies": dict(getattr(credentials, "session_cookies", {}) or {}),
            "auth_profile_id": profile_id,
            "use_profile": use_profile,
        }

        if use_profile and profile_id:
            return auth_config, True

        if token and self._is_masked_secret(token):
            token = None

        api_key_header = credential_overrides.get("api_key_header")
        username = credential_overrides.get("username")
        password = credential_overrides.get("password")
        client_id = credential_overrides.get("client_id")
        client_secret = credential_overrides.get("client_secret")
        token_url = credential_overrides.get("token_url")
        scope = credential_overrides.get("scope")

        if isinstance(password, str) and self._is_masked_secret(password):
            password = None
        if isinstance(client_secret, str) and self._is_masked_secret(client_secret):
            client_secret = None

        has_meaningful = False

        if api_key_header and token:
            auth_config.update(
                {
                    "type": AuthenticationType.API_KEY.value,
                    "api_key": token,
                    "api_key_header": api_key_header,
                }
            )
            has_meaningful = True
        elif username and password:
            auth_config.update(
                {
                    "type": AuthenticationType.BASIC_AUTH.value,
                    "username": username,
                    "password": password,
                }
            )
            has_meaningful = True
        elif client_id and client_secret and token_url:
            auth_config.update(
                {
                    "type": AuthenticationType.OAUTH2.value,
                    "oauth2_client_id": client_id,
                    "oauth2_client_secret": client_secret,
                    "oauth2_token_url": token_url,
                }
            )
            if scope:
                auth_config["oauth2_scope"] = scope
            has_meaningful = True
        elif token:
            auth_config.update(
                {
                    "type": AuthenticationType.BEARER_TOKEN.value,
                    "bearer_token": token,
                }
            )
            has_meaningful = True

        return auth_config, has_meaningful

    async def _to_unified_config(
        self, api_config: APIConfiguration, decrypt_sensitive: bool = False
    ) -> UnifiedAPIConfiguration:
        """Build the unified configuration dataclass from database model."""
        auth_data = (
            self._decrypt_auth_config(api_config.authentication)
            if decrypt_sensitive and api_config.authentication
            else (
                api_config.authentication.dict()
                if getattr(api_config, "authentication", None)
                else {}
            )
        )

        endpoints = await self._load_endpoints(api_config)
        credentials = self._build_credentials(api_config, auth_data, decrypt_sensitive)

        last_updated = (
            api_config.updated_at
            if getattr(api_config, "updated_at", None)
            else getattr(api_config, "created_at", None)
        ) or now_utc()

        environment = api_config.environment
        if isinstance(environment, Enum):
            environment = environment.value

        health_endpoint = ""
        health_config = getattr(api_config, "health_check", None)
        if health_config:
            health_endpoint = getattr(health_config, "endpoint", "") or ""

        documentation_url = getattr(api_config, "documentation_url", None) or ""

        tags = list(getattr(api_config, "tags", []) or [])
        category = getattr(api_config, "category", None) or "general"
        display_name = getattr(api_config, "display_name", None) or api_config.name

        return UnifiedAPIConfiguration(
            service_name=api_config.name,
            base_url=api_config.base_url,
            endpoints=endpoints,
            credentials=credentials,
            enabled=(api_config.status == APIStatus.ACTIVE),
            last_updated=last_updated,
            source=ConfigSource.DATABASE,
            display_name=display_name,
            description=getattr(api_config, "description", "") or "",
            category=category,
            tags=tags,
            environment=str(environment or APIEnvironment.DEVELOPMENT.value),
            version=getattr(api_config, "version", "1.0") or "1.0",
            health_check_endpoint=health_endpoint,
            documentation_url=documentation_url,
        )

    async def update_api_config(
        self, config_id: str, update_data: Dict[str, Any], updated_by: str
    ) -> bool:
        """Update an existing API configuration"""
        try:
            normalized_id = self._normalize_object_id(config_id) or config_id

            # Get existing config for audit trail
            existing_config = await self.get_api_config(config_id)
            if not existing_config:
                raise APIConfigurationError(f"API configuration {config_id} not found")

            # Encrypt sensitive authentication data if present
            if "authentication" in update_data:
                update_data["authentication"] = self._encrypt_auth_config(
                    update_data["authentication"]
                )

            # Set system fields
            update_data["updated_by"] = updated_by
            update_data["updated_at"] = now_utc()

            # Update in database
            result = await self.api_configs.update_one(
                {"_id": normalized_id}, {"$set": update_data}
            )

            if result.modified_count > 0:
                # Log the update
                await self._audit_log(
                    operation="update",
                    resource_type="api_config",
                    resource_id=config_id,
                    actor_id=updated_by,
                    old_values={"name": existing_config.name},
                    new_values=update_data,
                    success=True,
                )

                # Clear cache
                self._invalidate_caches()

                logger.info(f"Updated API configuration: {config_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to update API configuration {config_id}: {e}")
            await self._audit_log(
                operation="update",
                resource_type="api_config",
                resource_id=config_id,
                actor_id=updated_by,
                success=False,
                error_message=str(e),
            )
            raise APIConfigurationError(f"Failed to update API configuration: {e}")

    async def delete_api_config(self, config_id: str, deleted_by: str) -> bool:
        """Delete an API configuration (soft delete)"""
        try:
            normalized_id = self._normalize_object_id(config_id) or config_id

            # Get existing config for audit trail
            existing_config = await self.get_api_config(config_id)
            if not existing_config:
                return False

            # Soft delete
            result = await self.api_configs.update_one(
                {"_id": normalized_id},
                {
                    "$set": {
                        "deleted_at": now_utc(),
                        "deleted_by": deleted_by,
                        "status": APIStatus.INACTIVE.value,
                    }
                },
            )

            if result.modified_count > 0:
                # Log the deletion
                await self._audit_log(
                    operation="delete",
                    resource_type="api_config",
                    resource_id=config_id,
                    actor_id=deleted_by,
                    old_values={"name": existing_config.name},
                    success=True,
                )

                # Clear cache
                self._invalidate_caches()

                logger.info(f"Deleted API configuration: {config_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to delete API configuration {config_id}: {e}")
            await self._audit_log(
                operation="delete",
                resource_type="api_config",
                resource_id=config_id,
                actor_id=deleted_by,
                success=False,
                error_message=str(e),
            )
            return False

    async def get_default_config(
        self, service_name: str
    ) -> Optional[UnifiedAPIConfiguration]:
        """Get default configuration for a service"""
        defaults = {
            "api1": UnifiedAPIConfiguration(
                service_name="api1",
                base_url="https://ronaldo-club.to/api",
                endpoints={
                    "list_items": APIEndpointConfig(
                        "list_items",
                        "https://ronaldo-club.to/api/cards/hq/list",
                        "POST",
                        30,
                    ),
                    "cart_view": APIEndpointConfig(
                        "cart_view", "https://ronaldo-club.to/api/cart/", "GET", 30
                    ),
                    "cart_add": APIEndpointConfig(
                        "cart_add", "https://ronaldo-club.to/api/cart/", "POST", 30
                    ),
                    "cart_remove": APIEndpointConfig(
                        "cart_remove", "https://ronaldo-club.to/api/cart/", "DELETE", 30
                    ),
                    "user_info": APIEndpointConfig(
                        "user_info", "https://ronaldo-club.to/api/user/getme", "GET", 30
                    ),
                    "checkout": APIEndpointConfig(
                        "checkout", "https://ronaldo-club.to/api/checkout/", "POST", 60
                    ),
                },
                credentials=APICredentials(),
                source=ConfigSource.DEFAULT,
                display_name="API 1 - External Cart API",
                description="API 1: External API for cart operations, item listing, and user management (Ronaldo Club)",
                category="ecommerce",
                tags=["api1", "cart", "external", "api", "ecommerce", "ronaldo"],
                environment="development",
                version="1.0",
                health_check_endpoint="/user/getme",
                documentation_url="https://ronaldo-club.to/api/docs",
            ),
            "api2": UnifiedAPIConfiguration(
                service_name="api2",
                base_url="https://ronaldo-club.to/api/cards/vhq",
                endpoints={
                    "list_items": APIEndpointConfig(
                        "list_items",
                        "https://ronaldo-club.to/api/cards/vhq/list",
                        "POST",
                        30,
                    ),
                    "filters": APIEndpointConfig(
                        "filters",
                        "https://ronaldo-club.to/api/cards/vhq/filters",
                        "GET",
                        30,
                    ),
                    "orders": APIEndpointConfig(
                        "orders",
                        "https://ronaldo-club.to/api/cards/vhq/orders",
                        "GET",
                        30,
                    ),
                    "check_order": APIEndpointConfig(
                        "check_order",
                        "https://ronaldo-club.to/api/cards/vhq/check",
                        "POST",
                        30,
                    ),
                },
                credentials=APICredentials(),
                source=ConfigSource.DEFAULT,
                display_name="API 2 - BASE 2 Browse API",
                description="API 2: VHQ browse endpoints for BIN cards (BASE 2)",
                category="bin_cards",
                tags=["api2", "base2", "vhq", "browse", "bin"],
                environment="development",
                version="2.0",
                health_check_endpoint="/list",
                documentation_url="https://ronaldo-club.to/api/docs",
            )
        }
        return defaults.get(service_name)

    async def _create_initial_health_status(self, config_id: str):
        """Create initial health status for a new API configuration"""
        try:
            health_status = APIHealthStatus(
                api_config_id=config_id,
                is_healthy=False,
                last_check_at=now_utc(),
                next_check_at=now_utc() + timedelta(minutes=5),
            )
            await self.api_health.insert_one(health_status.to_mongo())
        except Exception as e:
            logger.warning(
                f"Failed to create initial health status for {config_id}: {e}"
            )

    def _invalidate_caches(self) -> None:
        """Invalidate all cached configuration lookups"""
        self._config_cache.clear()
        self._all_configs_cache.clear()

    async def _persist_endpoints(
        self, config_id: str, config: UnifiedAPIConfiguration
    ) -> None:
        """Upsert endpoint definitions for a configuration."""
        if not config.endpoints:
            return

        cfg_id_str = str(config_id)
        endpoint_names = set(config.endpoints.keys())

        for name, endpoint in config.endpoints.items():
            endpoint_obj = endpoint
            if isinstance(endpoint, dict):
                endpoint_obj = APIEndpointConfig(**endpoint)

            path = endpoint_obj.url or ""
            base_url = config.base_url or ""
            if base_url and path.startswith(base_url):
                path = path[len(base_url) :]
            path = path or "/"
            if not path.startswith("/"):
                path = f"/{path}"

            doc = {
                "api_config_id": cfg_id_str,
                "name": endpoint_obj.name or name,
                "path": path,
                "method": endpoint_obj.method or "GET",
                "timeout": getattr(endpoint_obj, "timeout", 30),
                "retry_count": getattr(endpoint_obj, "retry_count", 3),
                "retry_delays": getattr(endpoint_obj, "retry_delays", [1, 2, 4]),
                "updated_at": now_utc(),
            }

            try:
                await self.api_endpoints.update_one(
                    {"api_config_id": cfg_id_str, "name": doc["name"]},
                    {"$set": doc},
                    upsert=True,
                )
            except TypeError:
                existing = await self.api_endpoints.find_one(
                    {"api_config_id": cfg_id_str, "name": doc["name"]}
                )
                if existing:
                    await self.api_endpoints.update_one(
                        {"_id": existing.get("_id")}, {"$set": doc}
                    )
                else:
                    await self.api_endpoints.insert_one(doc)

        try:
            await self.api_endpoints.delete_many(
                {
                    "api_config_id": cfg_id_str,
                    "name": {"$nin": list(endpoint_names)},
                }
            )
        except Exception:
            pass

    async def _audit_log(
        self,
        operation: str,
        resource_type: str,
        resource_id: str,
        actor_id: str,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ):
        """Create an audit log entry"""
        try:
            audit_entry = APIAuditLog(
                operation=operation,
                resource_type=resource_type,
                resource_id=resource_id,
                actor_id=actor_id,
                old_values=old_values or {},
                new_values=new_values or {},
                success=success,
                error_message=error_message,
                timestamp=now_utc(),
            )
            await self.api_audit.insert_one(audit_entry.to_mongo())
        except Exception as e:
            logger.warning(f"Failed to create audit log entry: {e}")


# Singleton instance
_unified_service_instance = None


def get_unified_api_config_service() -> UnifiedAPIConfigurationService:
    """Get singleton instance of the unified API configuration service"""
    global _unified_service_instance
    if _unified_service_instance is None:
        _unified_service_instance = UnifiedAPIConfigurationService()
    return _unified_service_instance

# --- Compatibility aliases for smoother migration --------------------------------

# Allow importing these names from this module, matching legacy imports
# Old: from services.api_service import APIConfigurationService
# New: from api_v1.services.api_config import APIConfigurationService
APIConfigurationService = UnifiedAPIConfigurationService

# Old helper: from services.api_config_service import get_api_config_service
# New: expose the same name pointing to unified service getter
def get_api_config_service() -> UnifiedAPIConfigurationService:
    return get_unified_api_config_service()

# Old dataclass names sometimes imported from services.api_config_service
# Re-export equivalents for typing compatibility
APIEndpoint = APIEndpointConfig
