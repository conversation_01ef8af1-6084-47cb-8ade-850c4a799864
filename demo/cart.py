# add_to_cart
curl ^"https://ronaldo-club.to/api/cart/^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -H ^"content-type: application/json^" ^
  -b ^"__ddg1_=zPBj4MGDj0ADc38IxHFn; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTc3NjYzNzcsImV4cCI6MTc2MDM1ODM3N30.3oOgTyePjKgACc9_c4gdwLZrnXHuqcYCSGroAxnsAuE; testcookie=1; __ddg9_=*************; __ddg10_=1757772072; __ddg8_=8iWvAWqPlqpDbmYP^" ^
  -H ^"origin: https://ronaldo-club.to^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cards/hq^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" ^
  --data-raw ^"^{^\^"id^\^":1818704,^\^"product_table_name^\^":^\^"Cards^\^"^}^"

# response
{"success":true}

#view_Cart
curl ^"https://ronaldo-club.to/api/cart/^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -b ^"__ddg1_=zPBj4MGDj0ADc38IxHFn; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTc3NjYzNzcsImV4cCI6MTc2MDM1ODM3N30.3oOgTyePjKgACc9_c4gdwLZrnXHuqcYCSGroAxnsAuE; testcookie=1; __ddg9_=*************; __ddg8_=7Eg1TmOqLVCrPsBs; __ddg10_=1757772417^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cart^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^"

#response
{"success":true,"data":[{"_id":370363,"user_id":199094,"product_id":1818704,"product_table_name":"Cards","createdAt":"2025-09-13T14:06:57.000Z","brand":"MASTERCARD","bin":"521333","city":"Shepherdsville","state":"KY","zip":"40165","exp":"05/28","expyear":null,"expmonth":null,"country":"US","price":"14.9900","discount":0},{"_id":370362,"user_id":199094,"product_id":1745588,"product_table_name":"Cards","createdAt":"2025-09-13T14:06:10.000Z","brand":"DISCOVER","bin":"601101","city":"Destin Florida","state":"FL","zip":"32541","exp":"06/29","expyear":null,"expmonth":null,"country":"US","price":"3.9900","discount":0}],"totalCartPrice":18.98}

#remove_from_cart
curl ^"https://ronaldo-club.to/api/cart/370363^" ^
  -X ^"DELETE^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -b ^"__ddg1_=zPBj4MGDj0ADc38IxHFn; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTc3NjYzNzcsImV4cCI6MTc2MDM1ODM3N30.3oOgTyePjKgACc9_c4gdwLZrnXHuqcYCSGroAxnsAuE; testcookie=1; __ddg9_=*************; __ddg10_=1757772508; __ddg8_=LSKVpEBZcTRhJbQ0^" ^
  -H ^"origin: https://ronaldo-club.to^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cart^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^"

#response
{"success":true,"newPrice":3.99}