#curl
curl ^"https://ronaldo-club.to/api/cards/hq/filters?base=^&bank=^&bin=^&country=PA^&state=^&city=^&brand=^&type=^&zip=^&priceFrom=0^&priceTo=500^&zipCheck=false^&address=false^&phone=false^&email=false^&withoutcvv=false^&refundable=false^&expirethismonth=false^&dob=false^&ssn=false^&mmn=false^&ip=false^&dl=false^&ua=false^&discount=false^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.9^" ^
  -H ^"content-type: application/json^" ^
  -b ^"__ddg1_=u1UaBqLkngSC4ZTJRDQC; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.JUbSX8FCCYhK4B9yAeHs9VlErKVFqEDLGZJXHPlE-bU; testcookie=1; __ddg9_=***************; __ddg8_=TajrsYAEUKjxIqB8; __ddg10_=**********^" ^
  -H ^"origin: https://ronaldo-club.to^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cards/hq^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" ^
  --data-raw ^"^{^\^"name^\^":^\^"brand^\^",^\^"stringfilterData^\^":^\^"base=^&bank=^&bin=^&country=PA^&state=^&city=^&brand=^&type=^&zip=^&priceFrom=0^&priceTo=500^&zipCheck=false^&address=false^&phone=false^&email=false^&withoutcvv=false^&refundable=false^&expirethismonth=false^&dob=false^&ssn=false^&mmn=false^&ip=false^&dl=false^&ua=false^&discount=false^\^"^}^"

#fetch
fetch("https://ronaldo-club.to/api/cards/hq/filters?base=&bank=&bin=&country=PA&state=&city=&brand=&type=&zip=&priceFrom=0&priceTo=500&zipCheck=false&address=false&phone=false&email=false&withoutcvv=false&refundable=false&expirethismonth=false&dob=false&ssn=false&mmn=false&ip=false&dl=false&ua=false&discount=false", {
  "headers": {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9",
    "content-type": "application/json",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Brave\";v=\"140\"",
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": "\"Android\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sec-gpc": "1"
  },
  "referrer": "https://ronaldo-club.to/store/cards/hq",
  "body": "{\"name\":\"brand\",\"stringfilterData\":\"base=&bank=&bin=&country=PA&state=&city=&brand=&type=&zip=&priceFrom=0&priceTo=500&zipCheck=false&address=false&phone=false&email=false&withoutcvv=false&refundable=false&expirethismonth=false&dob=false&ssn=false&mmn=false&ip=false&dl=false&ua=false&discount=false\"}",
  "method": "POST",
  "mode": "cors",
  "credentials": "include"
});

#response
{"success":true,"data":[{"label":"All","value":""},{"label":"MASTERCARD (1)","value":"MASTERCARD"},{"label":"VISA (3)","value":"VISA"}]}