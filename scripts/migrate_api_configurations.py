#!/usr/bin/env python3
"""
Migrate legacy API configuration documents to the unified Pydantic schema.

This script fixes older records in the `api_configurations` collection by:
- Setting `name` from legacy `service_name` when missing
- Adding minimal required fields if missing: `authentication`, `created_by`
- Optionally stamping `source` if not present (kept as-is if set)

Run in dry-run mode by default. Use `--apply` to persist changes.
"""

from __future__ import annotations

import asyncio
import argparse
from typing import Any, Dict

from database.connection import init_database, close_database, get_collection
from models.base import now_utc


async def migrate(apply: bool = False) -> int:
    await init_database()
    try:
        coll = get_collection("api_configurations")
        docs = await coll.find({}).to_list(None)

        changes = 0
        for d in docs:
            update: Dict[str, Any] = {}

            # name from legacy service_name
            if not d.get("name") and d.get("service_name"):
                update["name"] = d["service_name"]

            # minimal required fields for Pydantic model
            if "authentication" not in d:
                update["authentication"] = {"type": "none"}
            if "created_by" not in d:
                update["created_by"] = "migration"
            if "created_at" not in d:
                update["created_at"] = now_utc()

            if not update:
                continue

            changes += 1
            print(f"Would update {_id_str(d)} -> {update}")
            if apply:
                await coll.update_one({"_id": d.get("_id")}, {"$set": update})

        print(f"\nSummary: {changes} document(s) {'updated' if apply else 'to update'}.")
        return changes
    finally:
        await close_database()


def _id_str(doc: Dict[str, Any]) -> str:
    try:
        return str(doc.get("_id"))
    except Exception:
        return "<unknown>"


def main() -> None:
    parser = argparse.ArgumentParser(description="Migrate legacy API configurations")
    parser.add_argument(
        "--apply",
        action="store_true",
        help="Apply changes to the database (otherwise dry-run)",
    )
    args = parser.parse_args()

    asyncio.run(migrate(apply=args.apply))


if __name__ == "__main__":
    main()

