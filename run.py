#!/usr/bin/env python3
"""
Run Demo Wallet Bot v2 with MongoDB - Standalone Version
This script runs the bot entirely from within the bot_v2 directory.
"""

import asyncio
import sys
from pathlib import Path

# Ensure current directory is on sys.path for direct imports
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))


async def main():
    """Run the bot"""
    try:
        # Import directly from main module
        from main import main as bot_main

        await bot_main()
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except asyncio.CancelledError:
        print("\nBot cancelled")
    except Exception as e:
        print(f"<PERSON><PERSON> failed to start: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
