# Shared API System Migration Guide

This guide explains how to migrate from the existing API v1 implementation to the new shared API system while maintaining full backward compatibility.

## Overview

The shared API system provides:
- **Reusable API client architecture** - One codebase for multiple APIs
- **Configuration-driven approach** - Easy to add new APIs without code changes
- **Enhanced error handling** - Comprehensive error categorization and recovery
- **Improved logging and monitoring** - Centralized logging with better context
- **Type safety** - Strong typing throughout the system
- **Backward compatibility** - Existing code continues to work unchanged

## Architecture

```
shared_api/
├── core/                   # Base classes and interfaces
│   ├── base_client.py     # Base API client class
│   ├── interfaces.py      # API client protocols
│   ├── exceptions.py      # Shared exceptions
│   └── constants.py       # Shared constants
├── config/                # Configuration management
│   ├── api_config.py      # API configuration models
│   ├── client_factory.py  # Client factory pattern
│   └── registry.py        # API registry for multiple APIs
├── http/                  # HTTP client implementation
│   ├── client.py          # Configurable HTTP client
│   ├── auth.py            # Authentication handlers
│   └── middleware.py      # Request/response middleware
├── utils/                 # Utility functions
│   ├── validation.py      # Configuration validation
│   ├── logging.py         # Shared logging utilities
│   └── helpers.py         # Common helper functions
├── examples/              # Usage examples
│   ├── api_v1_config.py   # API v1 configuration example
│   └── new_api_config.py  # Example for new APIs
└── compatibility.py       # Backward compatibility layer
```

## Migration Strategies

### Strategy 1: Immediate Compatibility (Recommended)

Use the compatibility layer to get immediate benefits with zero code changes:

```python
# OLD CODE (still works):
from services.external_api_service import get_external_api_service

api_service = get_external_api_service()
response = await api_service.list_items(params={"page": 1, "limit": 10})

# NEW CODE (enhanced, but same interface):
from shared_api.compatibility import get_external_api_service

api_service = get_external_api_service()
response = await api_service.list_items(params={"page": 1, "limit": 10})
```

### Strategy 2: Gradual Migration

Gradually migrate to the new API while keeping existing functionality:

```python
# Step 1: Use shared API client with existing patterns
from shared_api.config.client_factory import api_client_factory
from shared_api.examples.api_v1_config import create_api_v1_configuration

config = create_api_v1_configuration(login_token="your-token")
client = api_client_factory.create_client(config)

# Step 2: Use new patterns gradually
async with client:
    response = await client.post("list_items", data={"page": 1, "limit": 10})
```

### Strategy 3: Full Migration

Adopt the new system completely for new code:

```python
from shared_api import APIClientFactory, APIConfiguration, EndpointConfiguration
from shared_api.core.constants import HTTPMethod, AuthenticationType

# Define your API configuration
config = APIConfiguration(
    name="my_new_api",
    base_url="https://api.example.com/v1",
    endpoints={
        "get_data": EndpointConfiguration(
            name="get_data",
            path="/data",
            method=HTTPMethod.GET
        )
    },
    authentication=AuthenticationConfiguration(
        type=AuthenticationType.API_KEY,
        api_key="your-api-key"
    )
)

# Create and use client
factory = APIClientFactory()
async with factory.create_client(config) as client:
    data = await client.get("get_data")
```

## Benefits of Migration

### 1. Reusability
- **Before**: Each API required separate implementation
- **After**: One configuration file per API, shared client code

### 2. Maintainability
- **Before**: Changes required updating multiple files
- **After**: Changes in shared components benefit all APIs

### 3. Error Handling
- **Before**: Inconsistent error handling across APIs
- **After**: Comprehensive, categorized error handling with recovery suggestions

### 4. Configuration Management
- **Before**: Hard-coded URLs, headers, and settings
- **After**: Flexible configuration system with environment support

### 5. Testing
- **Before**: Difficult to mock and test API interactions
- **After**: Built-in testing support with easy mocking

## Adding New APIs

With the shared system, adding a new API is simple:

```python
# 1. Create configuration
new_api_config = APIConfiguration(
    name="payment_api",
    base_url="https://payments.example.com/v2",
    endpoints={
        "process_payment": EndpointConfiguration(
            name="process_payment",
            path="/payments",
            method=HTTPMethod.POST
        ),
        "get_payment": EndpointConfiguration(
            name="get_payment", 
            path="/payments/{id}",
            method=HTTPMethod.GET
        )
    },
    authentication=AuthenticationConfiguration(
        type=AuthenticationType.BEARER_TOKEN,
        bearer_token="your-token"
    )
)

# 2. Register with factory
api_client_factory.register_configuration(new_api_config)

# 3. Use immediately
client = api_client_factory.create_client("payment_api")
```

## Configuration Examples

### API v1 Configuration
```python
from shared_api.examples.api_v1_config import create_api_v1_configuration

config = create_api_v1_configuration(
    base_url="https://ronaldo-club.to/api",
    login_token="your-bearer-token",
    session_cookies={"loginToken": "token-value"}
)
```

### New API Configuration
```python
from shared_api.config.api_config import APIConfiguration, EndpointConfiguration
from shared_api.core.constants import HTTPMethod, AuthenticationType

config = APIConfiguration(
    name="inventory_api",
    base_url="https://inventory.example.com/api/v1",
    endpoints={
        "list_products": EndpointConfiguration(
            name="list_products",
            path="/products",
            method=HTTPMethod.GET
        ),
        "create_product": EndpointConfiguration(
            name="create_product",
            path="/products",
            method=HTTPMethod.POST
        )
    },
    authentication=AuthenticationConfiguration(
        type=AuthenticationType.API_KEY,
        api_key="your-api-key",
        api_key_header="X-API-Key"
    ),
    default_headers={
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
)
```

## Testing

The shared API system includes comprehensive testing support:

```python
import pytest
from shared_api.config.client_factory import api_client_factory
from shared_api.examples.api_v1_config import create_api_v1_configuration

@pytest.mark.asyncio
async def test_api_v1_list_items():
    config = create_api_v1_configuration()
    
    async with api_client_factory.create_client(config) as client:
        # Mock the HTTP client for testing
        response = await client.post("list_items", data={"page": 1})
        assert response["success"] is True
```

## Monitoring and Health Checks

The shared system provides built-in health monitoring:

```python
from shared_api.config.registry import api_registry

# Register APIs
api_registry.register_api(api_v1_config)
api_registry.register_api(new_api_config)

# Check health of all APIs
health_status = await api_registry.check_all_health()

# Get detailed API information
api_info = api_registry.get_api_info("api_v1")
```

## Best Practices

1. **Use Configuration Files**: Store API configurations in JSON/YAML files
2. **Environment-Specific Configs**: Use different configurations for dev/staging/prod
3. **Secure Credentials**: Use environment variables for sensitive data
4. **Health Monitoring**: Implement regular health checks for all APIs
5. **Error Handling**: Use the built-in error categorization for better UX
6. **Logging**: Leverage the enhanced logging for better debugging

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `shared_api` is in your Python path
2. **Configuration Validation**: Check that all required fields are provided
3. **Authentication Issues**: Verify credentials and authentication type
4. **Network Errors**: Check connectivity and firewall settings

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger("shared_api").setLevel(logging.DEBUG)
```

## Next Steps

1. **Start with Compatibility Layer**: Use `shared_api.compatibility` for immediate benefits
2. **Migrate Gradually**: Move to new patterns one service at a time
3. **Add New APIs**: Use the shared system for all new API integrations
4. **Monitor and Optimize**: Use built-in monitoring to optimize performance
5. **Contribute**: Help improve the shared system based on your use cases
