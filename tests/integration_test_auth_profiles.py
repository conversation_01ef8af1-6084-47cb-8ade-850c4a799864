"""
Integration test for Authentication Profile System
Tests the complete workflow from profile creation to API assignment
"""

import asyncio
import logging
from datetime import datetime, timezone

from models.auth_profile import AuthProfileScope, AuthProfileStatus
from models.api import AuthenticationType, APIEnvironment
from services.auth_profile_service import get_auth_profile_service
from api_v1.services.api_config import get_api_config_service

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_auth_profile_integration():
    """
    Integration test for the authentication profile system
    Tests the complete workflow from creation to assignment
    """
    
    logger.info("🧪 Starting Authentication Profile Integration Test")
    
    try:
        # Initialize services
        auth_service = get_auth_profile_service()
        api_service = get_api_config_service()
        
        test_user = "integration_test_user"
        
        # Step 1: Create authentication profiles
        logger.info("📝 Step 1: Creating authentication profiles...")
        
        # Create API Key profile
        api_key_profile = await auth_service.create_profile(
            profile_name="test_api_key_profile",
            display_name="Test API Key Profile",
            auth_type=AuthenticationType.API_KEY,
            credentials={"api_key": "test_api_key_12345"},
            created_by=test_user,
            description="Test API key authentication profile",
            scope=AuthProfileScope.GLOBAL,
            environment=APIEnvironment.DEVELOPMENT
        )
        
        if api_key_profile:
            logger.info(f"✅ Created API Key profile: {api_key_profile.id}")
        else:
            logger.error("❌ Failed to create API Key profile")
            return False
        
        # Create Bearer Token profile
        bearer_profile = await auth_service.create_profile(
            profile_name="test_bearer_profile",
            display_name="Test Bearer Token Profile",
            auth_type=AuthenticationType.BEARER_TOKEN,
            credentials={
                "bearer_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test",
                "login_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test"
            },
            created_by=test_user,
            description="Test bearer token authentication profile",
            scope=AuthProfileScope.CATEGORY,
            allowed_categories=["ecommerce"],
            environment=APIEnvironment.DEVELOPMENT
        )
        
        if bearer_profile:
            logger.info(f"✅ Created Bearer Token profile: {bearer_profile.id}")
        else:
            logger.error("❌ Failed to create Bearer Token profile")
            return False
        
        # Step 2: List and verify profiles
        logger.info("📋 Step 2: Listing authentication profiles...")
        
        profiles = await auth_service.list_profiles()
        logger.info(f"📊 Found {len(profiles)} profiles")
        
        for profile in profiles:
            if profile.profile_name.startswith("test_"):
                logger.info(f"  - {profile.display_name} ({profile.auth_type.value})")
        
        # Step 3: Test profile retrieval
        logger.info("🔍 Step 3: Testing profile retrieval...")
        
        retrieved_profile = await auth_service.get_profile_by_name("test_api_key_profile")
        if retrieved_profile:
            logger.info(f"✅ Retrieved profile: {retrieved_profile.display_name}")
            assert retrieved_profile.is_active()
            logger.info("✅ Profile is active")
        else:
            logger.error("❌ Failed to retrieve profile")
            return False
        
        # Step 4: Test credential decryption
        logger.info("🔓 Step 4: Testing credential decryption...")
        
        decrypted_creds = await auth_service.get_decrypted_credentials(api_key_profile.id)
        if decrypted_creds and "api_key" in decrypted_creds:
            logger.info("✅ Successfully decrypted credentials")
        else:
            logger.error("❌ Failed to decrypt credentials")
            return False
        
        # Step 5: Test profile updates
        logger.info("✏️ Step 5: Testing profile updates...")
        
        update_success = await auth_service.update_profile(
            profile_id=api_key_profile.id,
            updates={
                "description": "Updated test API key authentication profile",
                "tags": ["test", "integration", "api_key"]
            },
            updated_by=test_user,
            propagate_changes=False  # Skip propagation for this test
        )
        
        if update_success:
            logger.info("✅ Successfully updated profile")
        else:
            logger.error("❌ Failed to update profile")
            return False
        
        # Step 6: Test profile assignment (mock API configuration)
        logger.info("🔗 Step 6: Testing profile assignment...")
        
        # Note: This would normally assign to a real API configuration
        # For integration testing, we'll test the assignment logic
        assignment_success = await auth_service.assign_profile_to_api(
            api_config_id="test_api_config",
            auth_profile_id=api_key_profile.id,
            assigned_by=test_user,
            override_credentials={"custom_header": "test_value"}
        )
        
        if assignment_success:
            logger.info("✅ Successfully assigned profile to API")
        else:
            logger.error("❌ Failed to assign profile to API")
            return False
        
        # Step 7: Test assignment retrieval
        logger.info("📋 Step 7: Testing assignment retrieval...")
        
        assignment = await auth_service.get_api_profile_assignment("test_api_config")
        if assignment:
            logger.info(f"✅ Retrieved assignment: {assignment.auth_profile_id}")
            assert assignment.is_active
            logger.info("✅ Assignment is active")
        else:
            logger.error("❌ Failed to retrieve assignment")
            return False
        
        # Step 8: Test impact analysis
        logger.info("📊 Step 8: Testing impact analysis...")
        
        impact = await auth_service.get_profile_impact_analysis(api_key_profile.id)
        if impact and "profile_name" in impact:
            logger.info(f"✅ Impact analysis: {impact['total_assignments']} assignments")
        else:
            logger.error("❌ Failed to get impact analysis")
            return False
        
        # Step 9: Test bulk operations
        logger.info("🔄 Step 9: Testing bulk operations...")
        
        bulk_updates = {
            api_key_profile.id: {"tags": ["bulk_updated", "test"]},
            bearer_profile.id: {"tags": ["bulk_updated", "test"]}
        }
        
        bulk_results = await auth_service.bulk_update_profiles(
            profile_updates=bulk_updates,
            updated_by=test_user,
            propagate_changes=False
        )
        
        if bulk_results["successful_updates"] == 2:
            logger.info("✅ Bulk update successful")
        else:
            logger.error(f"❌ Bulk update failed: {bulk_results}")
            return False
        
        # Step 10: Test templates
        logger.info("📋 Step 10: Testing profile templates...")
        
        templates = await auth_service.get_templates()
        if templates:
            logger.info(f"✅ Found {len(templates)} profile templates")
            for template in templates:
                logger.info(f"  - {template.template_name} ({template.auth_type.value})")
        else:
            logger.error("❌ No profile templates found")
            return False
        
        # Step 11: Create profile from template
        logger.info("🎯 Step 11: Testing profile creation from template...")
        
        ecommerce_template = next(
            (t for t in templates if t.template_id == "ecommerce_standard"), 
            None
        )
        
        if ecommerce_template:
            template_profile = await auth_service.create_profile_from_template(
                template_id="ecommerce_standard",
                profile_name="test_template_profile",
                credentials={"login_token": "template_test_token"},
                created_by=test_user,
                display_name="Test Template Profile"
            )
            
            if template_profile:
                logger.info(f"✅ Created profile from template: {template_profile.id}")
            else:
                logger.error("❌ Failed to create profile from template")
                return False
        else:
            logger.warning("⚠️ E-commerce template not found, skipping template test")
        
        # Step 12: Cleanup test data
        logger.info("🧹 Step 12: Cleaning up test data...")
        
        # Delete test profiles
        test_profiles = [
            api_key_profile.id,
            bearer_profile.id
        ]
        
        if 'template_profile' in locals():
            test_profiles.append(template_profile.id)
        
        cleanup_success = True
        for profile_id in test_profiles:
            try:
                # Unassign from APIs first
                await auth_service.unassign_profile_from_api("test_api_config")
                
                # Delete profile
                delete_success = await auth_service.delete_profile(profile_id, test_user)
                if delete_success:
                    logger.info(f"✅ Deleted profile: {profile_id}")
                else:
                    logger.warning(f"⚠️ Could not delete profile: {profile_id}")
                    cleanup_success = False
            except Exception as e:
                logger.warning(f"⚠️ Error during cleanup of {profile_id}: {e}")
                cleanup_success = False
        
        if cleanup_success:
            logger.info("✅ Cleanup completed successfully")
        else:
            logger.warning("⚠️ Cleanup completed with warnings")
        
        logger.info("🎉 Authentication Profile Integration Test PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the integration test"""
    success = await test_auth_profile_integration()
    
    if success:
        print("\n✅ All tests passed! The Authentication Profile System is working correctly.")
        exit(0)
    else:
        print("\n❌ Tests failed! Please check the logs for details.")
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())
