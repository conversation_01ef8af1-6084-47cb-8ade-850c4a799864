import pytest
from unittest.mock import AsyncMock

from handlers.catalog_handlers import CatalogHandlers


@pytest.mark.asyncio
async def test_state_filter_uses_country_context_and_shows_options():
    handlers = CatalogHandlers()

    mock_response = {
        "success": True,
        "data": [
            {"label": "All", "value": ""},
            {"label": "<PERSON>ontaine-sur-Marty (1)", "value": "Fontaine-sur-Marty"},
        ],
    }

    handlers.card_service.fetch_filter_options = AsyncMock(return_value=mock_response)

    filters = {"country": "PA"}

    message, keyboard = await handlers._build_filter_selection_menu(
        "state", "location", filters, user_id=123
    )

    handlers.card_service.fetch_filter_options.assert_awaited_once()
    kwargs = handlers.card_service.fetch_filter_options.await_args.kwargs

    assert kwargs["filters"]["country"] == "PA"
    assert "state" not in kwargs["filters"]

    buttons = [btn for row in keyboard.inline_keyboard for btn in row]
    texts = [btn.text for btn in buttons]
    callbacks = [btn.callback_data for btn in buttons if btn.callback_data]

    assert any(text == "All" for text in texts)
    assert any("<PERSON>ontaine-sur-Marty" in text for text in texts)
    assert "✍️ Enter Manually" in texts
    assert "Tip: select a country" not in message
    assert "Showing results" in message
    assert any(cb.startswith("filter:set:state:opt") for cb in callbacks)
    assert all(len(cb) <= 64 for cb in callbacks)


@pytest.mark.asyncio
async def test_city_filter_uses_active_country_and_state():
    handlers = CatalogHandlers()

    mock_response = {
        "success": True,
        "data": [
            {"label": "All", "value": ""},
            {"label": "Dufour-sur-Mer (1)", "value": "Dufour-sur-Mer"},
        ],
    }

    handlers.card_service.fetch_filter_options = AsyncMock(return_value=mock_response)

    filters = {"country": "PA", "state": "PA"}

    message, keyboard = await handlers._build_filter_selection_menu(
        "city", "location", filters, user_id=456
    )

    handlers.card_service.fetch_filter_options.assert_awaited_once()
    kwargs = handlers.card_service.fetch_filter_options.await_args.kwargs

    assert kwargs["filters"]["country"] == "PA"
    assert kwargs["filters"]["state"] == "PA"
    assert "city" not in kwargs["filters"]

    buttons = [btn for row in keyboard.inline_keyboard for btn in row]
    texts = [btn.text for btn in buttons]
    callbacks = [btn.callback_data for btn in buttons if btn.callback_data]

    assert any(text == "All" for text in texts)
    assert any("Dufour-sur-Mer" in text for text in texts)
    assert "✍️ Enter Manually" in texts
    assert "Tip: select a state" not in message
    assert "Showing results" in message
    assert any(cb.startswith("filter:set:city:opt") for cb in callbacks)
    assert all(len(cb) <= 64 for cb in callbacks)


@pytest.mark.asyncio
async def test_zip_filter_uses_active_location_chain():
    handlers = CatalogHandlers()

    mock_response = {
        "success": True,
        "data": [
            {"label": "All", "value": ""},
            {"label": "12345 (2)", "value": "12345"},
        ],
    }

    handlers.card_service.fetch_filter_options = AsyncMock(return_value=mock_response)

    filters = {
        "country": "PA",
        "state": "PA",
        "city": "Fontaine-sur-Marty",
    }

    message, keyboard = await handlers._build_filter_selection_menu(
        "zip", "location", filters, user_id=789
    )

    handlers.card_service.fetch_filter_options.assert_awaited_once()
    kwargs = handlers.card_service.fetch_filter_options.await_args.kwargs

    assert kwargs["filters"]["country"] == "PA"
    assert kwargs["filters"]["state"] == "PA"
    assert kwargs["filters"]["city"] == "Fontaine-sur-Marty"
    assert "zip" not in kwargs["filters"]

    buttons = [btn for row in keyboard.inline_keyboard for btn in row]
    texts = [btn.text for btn in buttons]
    callbacks = [btn.callback_data for btn in buttons if btn.callback_data]

    assert any(text == "All" for text in texts)
    assert any("12345" in text for text in texts)
    assert "✍️ Enter Manually" in texts
    assert "Tip: select a city" not in message
    assert "Showing results" in message
    assert any(cb.startswith("filter:set:zip:opt") for cb in callbacks)
    assert all(len(cb) <= 64 for cb in callbacks)


@pytest.mark.asyncio
async def test_city_filter_prompts_for_state_when_missing():
    handlers = CatalogHandlers()

    handlers.card_service.fetch_filter_options = AsyncMock(
        return_value={"success": True, "data": []}
    )

    message, _ = await handlers._build_filter_selection_menu(
        "city", "location", {}, user_id=101
    )

    assert "Tip: select a state" in message


def test_location_category_buttons_use_dynamic_selects():
    handlers = CatalogHandlers()
    filters = {}

    _, keyboard = handlers._build_category_menu("location", filters)

    buttons = [btn for row in keyboard.inline_keyboard for btn in row]
    callback_map = {btn.text: btn.callback_data for btn in buttons}

    assert callback_map["🌍 Country"] == "filter:select:country"
    assert callback_map["🏙 State"] == "filter:select:state"
    assert callback_map["🏘 City"] == "filter:select:city"
    assert callback_map["🏷 ZIP"] == "filter:select:zip"


@pytest.mark.asyncio
async def test_brand_filter_uses_active_filters():
    handlers = CatalogHandlers()

    mock_response = {
        "success": True,
        "data": [
            {"label": "All", "value": ""},
            {"label": "VISA (20)", "value": "VISA"},
        ],
    }

    handlers.card_service.fetch_filter_options = AsyncMock(return_value=mock_response)

    filters = {"country": "PA", "state": "PA", "type": "CREDIT"}

    message, keyboard = await handlers._build_filter_selection_menu(
        "brand", "card", filters, user_id=202
    )

    handlers.card_service.fetch_filter_options.assert_awaited_once()
    kwargs = handlers.card_service.fetch_filter_options.await_args.kwargs

    assert kwargs["filters"]["country"] == "PA"
    assert kwargs["filters"]["state"] == "PA"
    assert kwargs["filters"]["type"] == "CREDIT"
    assert "brand" not in kwargs["filters"]

    buttons = [btn for row in keyboard.inline_keyboard for btn in row]
    texts = [btn.text for btn in buttons]
    callbacks = [btn.callback_data for btn in buttons if btn.callback_data]

    assert any("VISA" in text for text in texts)
    assert "Tip:" not in message
    assert "Showing results" in message
    assert any(cb.startswith("filter:set:brand:opt") for cb in callbacks)
    assert all(len(cb) <= 64 for cb in callbacks)

    brand_token = callbacks[0].split(":")[-1]
    assert handlers._resolve_dynamic_option_value(202, "brand", brand_token) == ""


@pytest.mark.asyncio
async def test_type_filter_uses_active_filters():
    handlers = CatalogHandlers()

    mock_response = {
        "success": True,
        "data": [
            {"label": "All", "value": ""},
            {"label": "CREDIT (99)", "value": "CREDIT"},
        ],
    }

    handlers.card_service.fetch_filter_options = AsyncMock(return_value=mock_response)

    filters = {"country": "PA", "brand": "VISA"}

    message, keyboard = await handlers._build_filter_selection_menu(
        "type", "card", filters, user_id=303
    )

    handlers.card_service.fetch_filter_options.assert_awaited_once()
    kwargs = handlers.card_service.fetch_filter_options.await_args.kwargs

    assert kwargs["filters"]["country"] == "PA"
    assert kwargs["filters"]["brand"] == "VISA"
    assert "type" not in kwargs["filters"]

    buttons = [btn for row in keyboard.inline_keyboard for btn in row]
    texts = [btn.text for btn in buttons]
    callbacks = [btn.callback_data for btn in buttons if btn.callback_data]

    assert "Showing results" in message
    assert any("CREDIT" in text for text in texts)
    assert any(cb.startswith("filter:set:type:opt") for cb in callbacks)
    assert all(len(cb) <= 64 for cb in callbacks)


@pytest.mark.asyncio
async def test_level_filter_uses_active_filters():
    handlers = CatalogHandlers()

    mock_response = {
        "success": True,
        "data": [
            {"label": "All", "value": ""},
            {"label": "PLATINUM (10)", "value": "PLATINUM"},
        ],
    }

    handlers.card_service.fetch_filter_options = AsyncMock(return_value=mock_response)

    filters = {"country": "PA", "brand": "VISA", "type": "CREDIT"}

    message, keyboard = await handlers._build_filter_selection_menu(
        "level", "card", filters, user_id=404
    )

    handlers.card_service.fetch_filter_options.assert_awaited_once()
    kwargs = handlers.card_service.fetch_filter_options.await_args.kwargs

    assert kwargs["filters"]["country"] == "PA"
    assert kwargs["filters"]["brand"] == "VISA"
    assert kwargs["filters"]["type"] == "CREDIT"
    assert "level" not in kwargs["filters"]

    buttons = [btn for row in keyboard.inline_keyboard for btn in row]
    texts = [btn.text for btn in buttons]
    callbacks = [btn.callback_data for btn in buttons if btn.callback_data]

    assert "Showing results" in message
    assert any("PLATINUM" in text for text in texts)
    assert any(cb.startswith("filter:set:level:opt") for cb in callbacks)
    assert all(len(cb) <= 64 for cb in callbacks)


@pytest.mark.asyncio
async def test_bank_filter_retains_context_and_manual_option():
    handlers = CatalogHandlers()

    mock_response = {
        "success": True,
        "data": [
            {"label": "All", "value": ""},
            {"label": "CITI (3)", "value": "CITI"},
        ],
    }

    handlers.card_service.fetch_filter_options = AsyncMock(return_value=mock_response)

    filters = {"country": "PA", "state": "PA", "brand": "VISA"}

    message, keyboard = await handlers._build_filter_selection_menu(
        "bank", "card", filters, user_id=505
    )

    handlers.card_service.fetch_filter_options.assert_awaited_once()
    kwargs = handlers.card_service.fetch_filter_options.await_args.kwargs

    assert kwargs["filters"]["country"] == "PA"
    assert kwargs["filters"]["state"] == "PA"
    assert kwargs["filters"]["brand"] == "VISA"
    assert "bank" not in kwargs["filters"]

    buttons = [btn for row in keyboard.inline_keyboard for btn in row]
    texts = [btn.text for btn in buttons]
    callbacks = [btn.callback_data for btn in buttons if btn.callback_data]

    assert any("CITI" in text for text in texts)
    assert "Showing results" in message
    assert "✍️ Enter Manually" in texts
    assert any(cb.startswith("filter:set:bank:opt") for cb in callbacks)
    assert all(len(cb) <= 64 for cb in callbacks)


@pytest.mark.asyncio
async def test_filter_option_pagination_navigation():
    handlers = CatalogHandlers()

    options = [
        {"label": f"Option {i + 1}", "value": f"VAL{i + 1}"}
        for i in range(25)
    ]

    handlers.card_service.fetch_filter_options = AsyncMock(
        return_value={"success": True, "data": options}
    )

    filters = {}

    message, keyboard = await handlers._build_filter_selection_menu(
        "brand", "card", filters, user_id=606
    )

    assert "Showing results 1-20 of 25" in message
    assert "Page 1 of 2" in message

    nav_rows = [
        row
        for row in keyboard.inline_keyboard
        if any(
            btn.callback_data
            and btn.callback_data.startswith("filter:page:brand")
            for btn in row
        )
    ]
    assert nav_rows
    assert len(nav_rows[0]) == 1
    assert nav_rows[0][0].text == "Next ➡️"
    assert nav_rows[0][0].callback_data.endswith(":1")

    handlers.card_service.fetch_filter_options.reset_mock()

    message_page2, keyboard_page2 = await handlers._build_filter_selection_menu(
        "brand", "card", filters, user_id=606, page=1
    )

    assert "Showing results 21-25 of 25" in message_page2
    assert "Page 2 of 2" in message_page2

    callbacks_page2 = [
        btn.callback_data
        for row in keyboard_page2.inline_keyboard
        for btn in row
        if btn.callback_data
    ]
    assert any(cb.startswith("filter:set:brand:opt1_") for cb in callbacks_page2)

    nav_rows_page2 = [
        row
        for row in keyboard_page2.inline_keyboard
        if any(
            btn.callback_data
            and btn.callback_data.startswith("filter:page:brand")
            for btn in row
        )
    ]
    assert nav_rows_page2
    assert len(nav_rows_page2[0]) == 1
    assert nav_rows_page2[0][0].text == "⬅️ Previous"
    assert nav_rows_page2[0][0].callback_data.endswith(":0")


@pytest.mark.asyncio
async def test_filter_options_sorted_alphabetically():
    handlers = CatalogHandlers()

    unordered_options = [
        {"label": "Banana (1)", "value": "Banana"},
        {"label": "ALL CAPS (2)", "value": "ALL CAPS"},
        {"label": "apple (3)", "value": "apple"},
        {"label": "All", "value": ""},
    ]

    handlers.card_service.fetch_filter_options = AsyncMock(
        return_value={"success": True, "data": unordered_options}
    )

    filters = {}

    message, keyboard = await handlers._build_filter_selection_menu(
        "brand", "card", filters, user_id=707
    )

    assert "Showing results" in message

    option_texts: list[str] = []
    for row in keyboard.inline_keyboard:
        for btn in row:
            if btn.text == "✍️ Enter Manually":
                break
            if btn.callback_data and btn.callback_data.startswith("filter:set:brand"):
                option_texts.append(btn.text)
        else:
            continue
        break

    expected_order = ["All", "ALL CAPS (2)", "apple (3)", "Banana (1)"]
    assert option_texts == expected_order


def test_card_category_buttons_place_manual_bin_first():
    handlers = CatalogHandlers()
    filters = {}

    _, keyboard = handlers._build_category_menu("card", filters)

    buttons = [btn for row in keyboard.inline_keyboard for btn in row]
    callback_map = {btn.text: btn.callback_data for btn in buttons}

    assert keyboard.inline_keyboard[0][0].text == "✍️ Enter BIN"
    assert keyboard.inline_keyboard[0][0].callback_data == "filter:input:bin"

    assert callback_map["🏷 Brand"] == "filter:select:brand"
    assert callback_map["💳 Card Type"] == "filter:select:type"
    assert callback_map["🎚 Level"] == "filter:select:level"
    assert callback_map["🏦 Bank"] == "filter:select:bank"
    assert "⌨️ Custom Bank" not in callback_map
    assert "🔢 BIN" not in callback_map
    assert "🗂 Base" not in callback_map
