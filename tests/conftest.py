import asyncio
import pytest

try:
    from database.connection import init_database, close_database
except Exception:
    init_database = None
    close_database = None


@pytest.fixture(scope="session", autouse=True)
def _init_db_session():
    """Initialize the in-memory database once for the test session.

    Ensures collections are available for services that access the DB on init.
    """
    if init_database is None:
        yield
        return

    asyncio.run(init_database())
    try:
        yield
    finally:
        asyncio.run(close_database())
