"""
Comprehensive security tests for the Demo Wallet Bot improvements
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from aiogram.types import Message, CallbackQuery, User

from utils.security import (
    check_rate_limit_security,
    hash_password,
    verify_password,
    validate_admin_session,
    sanitize_admin_input,
)
from utils.validation import (
    validate_amount,
    validate_telegram_id,
    sanitize_text_input,
    ValidationError,
)
from handlers.admin_handlers import AdminHandlers
from handlers.catalog_handlers import CatalogHandlers
from handlers.wallet_handlers import WalletHandlers


class TestSecurityUtilities:
    """Test security utility functions"""

    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "test_password_123"

        # Test hashing
        hashed, salt = hash_password(password)
        assert isinstance(hashed, str)
        assert isinstance(salt, bytes)
        assert len(salt) == 32  # 256-bit salt

        # Test verification
        assert verify_password(password, hashed, salt) is True
        assert verify_password("wrong_password", hashed, salt) is False

        # Test different passwords produce different hashes
        hashed2, salt2 = hash_password(password)
        assert hashed != hashed2  # Different salts should produce different hashes

    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        user_id = 12345
        action = "test_action"

        # Should allow initial requests
        assert (
            check_rate_limit_security(
                user_id, action, max_attempts=3, window_seconds=60
            )
            is True
        )
        assert (
            check_rate_limit_security(
                user_id, action, max_attempts=3, window_seconds=60
            )
            is True
        )
        assert (
            check_rate_limit_security(
                user_id, action, max_attempts=3, window_seconds=60
            )
            is True
        )

        # Should block after exceeding limit
        assert (
            check_rate_limit_security(
                user_id, action, max_attempts=3, window_seconds=60
            )
            is False
        )

        # Different user should not be affected
        assert (
            check_rate_limit_security(54321, action, max_attempts=3, window_seconds=60)
            is True
        )

    def test_input_sanitization(self):
        """Test input sanitization"""
        # Test normal input
        clean_input = sanitize_admin_input("normal text", "test_field")
        assert clean_input == "normal text"

        # Test malicious input
        malicious_input = "<script>alert('xss')</script>"
        try:
            clean_input = sanitize_admin_input(malicious_input, "test_field")
            assert "<script>" not in clean_input
            assert "alert" not in clean_input
        except Exception:
            # Function may raise SecurityError for dangerous content
            pass

        # Test SQL injection attempt
        sql_input = "'; DROP TABLE users; --"
        try:
            clean_input = sanitize_admin_input(sql_input, "test_field")
            assert "DROP TABLE" not in clean_input
        except Exception:
            # Function may raise SecurityError for dangerous content
            pass
        assert "DROP TABLE" not in clean_input

    def test_admin_session_validation(self):
        """Test admin session validation"""
        # Valid admin user
        with patch("config.settings.get_settings") as mock_settings:
            mock_settings.return_value.admin_ids = [12345]
            assert validate_admin_session(12345) is True

        # Invalid user ID
        assert validate_admin_session(-1) is False
        assert validate_admin_session(0) is False


class TestInputValidation:
    """Test input validation functions"""

    def test_amount_validation(self):
        """Test amount validation"""
        # Valid amounts
        assert validate_amount("10.50") == 10.50
        assert validate_amount("100") == 100.0
        assert validate_amount(50.75) == 50.75

        # Invalid amounts
        with pytest.raises(ValidationError):
            validate_amount("-10")  # Negative
        with pytest.raises(ValidationError):
            validate_amount("0")  # Zero
        with pytest.raises(ValidationError):
            validate_amount("abc")  # Non-numeric
        with pytest.raises(ValidationError):
            validate_amount("99999")  # Too large

    def test_telegram_id_validation(self):
        """Test Telegram ID validation"""
        # Valid IDs
        assert validate_telegram_id(123456789) == 123456789
        assert validate_telegram_id("987654321") == 987654321

        # Invalid IDs
        with pytest.raises(ValidationError):
            validate_telegram_id(-1)  # Negative
        with pytest.raises(ValidationError):
            validate_telegram_id(0)  # Zero
        with pytest.raises(ValidationError):
            validate_telegram_id("abc")  # Non-numeric
        with pytest.raises(ValidationError):
            validate_telegram_id(123)  # Too short

    def test_text_sanitization(self):
        """Test text input sanitization"""
        # Normal text
        assert sanitize_text_input("Hello World") == "Hello World"

        # HTML removal
        html_input = "<b>Bold</b> text"
        assert sanitize_text_input(html_input) == "Bold text"

        # Script removal
        script_input = "javascript:alert('xss')"
        sanitized = sanitize_text_input(script_input)
        assert "javascript:" not in sanitized

        # Length validation
        with pytest.raises(ValidationError):
            sanitize_text_input("x" * 300, max_length=100)


class TestHandlerSecurity:
    """Test security improvements in handlers"""

    @pytest.fixture
    def mock_user(self):
        """Create a mock Telegram user"""
        user = Mock(spec=User)
        user.id = 12345
        user.username = "testuser"
        user.first_name = "Test"
        return user

    @pytest.fixture
    def mock_message(self, mock_user):
        """Create a mock message"""
        message = Mock(spec=Message)
        message.from_user = mock_user
        message.answer = AsyncMock()
        return message

    @pytest.fixture
    def mock_callback(self, mock_user):
        """Create a mock callback query"""
        callback = Mock(spec=CallbackQuery)
        callback.from_user = mock_user
        callback.answer = AsyncMock()
        callback.message = Mock()
        callback.message.edit_text = AsyncMock()
        return callback

    @pytest.mark.asyncio
    async def test_admin_authentication_rate_limiting(self, mock_message):
        """Test admin authentication rate limiting"""
        admin_handlers = AdminHandlers()

        # Mock the rate limiting to return False (rate limited)
        with patch("utils.security.check_rate_limit_security", return_value=False):
            with patch("aiogram.fsm.context.FSMContext") as mock_state:
                mock_state.clear = AsyncMock()

                await admin_handlers.msg_admin_auth_passphrase(mock_message, mock_state)

                # Should send rate limit message
                mock_message.answer.assert_called_once()
                call_args = mock_message.answer.call_args[0][0]
                assert "Too many authentication attempts" in call_args

    @pytest.mark.asyncio
    async def test_catalog_price_validation(self, mock_callback):
        """Test catalog price range validation"""
        catalog_handlers = CatalogHandlers()

        # Test invalid price range (negative)
        mock_callback.data = "filter:set:price:-10-100"
        await catalog_handlers.cb_filter_set(mock_callback)

        # Should show error for negative prices
        mock_callback.answer.assert_called()
        call_args = mock_callback.answer.call_args
        assert "negative" in call_args[0][0].lower()

    @pytest.mark.asyncio
    async def test_wallet_amount_validation(self, mock_message):
        """Test wallet amount input validation"""
        wallet_handlers = WalletHandlers()

        # Mock dependencies
        with patch.object(wallet_handlers, "user_service") as mock_service:
            mock_service.ensure_user_and_wallet = AsyncMock()

            # Test invalid amount
            mock_message.text = "invalid_amount"

            with patch("aiogram.fsm.context.FSMContext") as mock_state:
                mock_state.clear = AsyncMock()

                await wallet_handlers.process_custom_amount(mock_message, mock_state)

                # Should send validation error
                mock_message.answer.assert_called()
                call_args = mock_message.answer.call_args[0][0]
                assert "❌" in call_args


class TestSecurityIntegration:
    """Integration tests for security features"""

    @pytest.mark.asyncio
    async def test_end_to_end_security_flow(self):
        """Test complete security flow"""
        # This would test the complete flow from input to database
        # with all security measures in place
        pass

    def test_configuration_security(self):
        """Test security configuration settings"""
        from config.settings import get_settings

        settings = get_settings()

        # Verify security settings are properly configured
        assert hasattr(settings, "MONGODB_AUTH_SOURCE")
        assert hasattr(settings, "MONGODB_AUTH_MECHANISM")
        assert settings.MONGODB_AUTH_MECHANISM == "SCRAM-SHA-256"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
