import asyncio
from typing import Dict

import pytest

from api_v2.services.browse_service import (
    APIV2BrowseParams,
    APIV2BrowseService,
    APIV2BrowseResponse,
)
from api_v2.services.adapter import APIV2ExternalBrowseAdapter
from services.external_api_service import APIResponse, APIOperation, ListItemsParams
from shared_api.core.exceptions import HTTPClientError


class StubClient:
    def __init__(self):
        self.records = []
        self._response = {"data": [], "totalCount": 0}

    def set_response(self, response):
        self._response = response

    async def post(self, endpoint, params=None, **kwargs):
        self.records.append(("POST", endpoint, params, kwargs))
        return self._response

    async def get(self, endpoint, params=None, **kwargs):
        self.records.append(("GET", endpoint, params, kwargs))
        return self._response

    async def health_check(self):
        return True

    async def close(self):
        return None


class StubRegistry:
    def __init__(self, client):
        self.client = client
        self.registered = None

    def register_api(self, config):
        self.registered = config

    def get_client(self, name):
        return self.client


@pytest.mark.asyncio
async def test_list_items_normalizes_response():
    client = StubClient()
    client.set_response({"data": [1, 2], "total": 2})
    registry = StubRegistry(client)
    service = APIV2BrowseService(registry=registry)

    params = APIV2BrowseParams(page=2, limit=5, country="US", zip_check=True)
    response = await service.list_items(params)

    assert response.success is True
    assert response.data["page"] == 2
    assert response.data["limit"] == 5
    assert response.data["totalCount"] == 2
    filters = response.data["filters"]
    assert filters["country"] == "US"
    assert filters["zipCheck"] == "true"

    method, endpoint, query, _ = client.records[0]
    assert method == "POST"
    assert endpoint == "list_items"
    assert ("page", "2") in query
    assert ("limit", "5") in query
    assert ("zipCheck", "true") in query


@pytest.mark.asyncio
async def test_get_filters_uses_filter_params_only():
    client = StubClient()
    client.set_response({"banks": ["A"]})
    registry = StubRegistry(client)
    service = APIV2BrowseService(registry=registry)

    params = APIV2BrowseParams(page=3, limit=20, bank="META")
    response = await service.get_filters("bank", params)

    assert response.success is True
    method, endpoint, query, _ = client.records[0]
    assert method == "GET"
    assert endpoint == "filters"
    assert ("page", "3") not in query  # pagination removed
    assert ("bank", "META") in query
    assert ("name", "bank") in query


@pytest.mark.asyncio
async def test_list_orders_handles_error():
    class ErrorClient(StubClient):
        async def get(self, endpoint, params=None, **kwargs):
            raise HTTPClientError("boom", status_code=500, response_text="err")

    client = ErrorClient()
    registry = StubRegistry(client)
    service = APIV2BrowseService(registry=registry)

    response = await service.list_orders(page=4, limit=7)
    assert response.success is False
    assert response.status_code == 500
    assert response.error == "boom"


@pytest.mark.asyncio
async def test_api_v1_and_v2_can_share_registry():
    from shared_api.config.registry import APIRegistry
    from shared_api.examples.api_v1_config import create_api_v1_configuration

    registry = APIRegistry()
    registry.register_api(create_api_v1_configuration())

    service_v2 = APIV2BrowseService(registry=registry)

    client_v1 = registry.get_client("api_v1")
    client_v2 = registry.get_client(service_v2._config_name)

    assert client_v1 is not client_v2
    await client_v2.close()
    await client_v1.close()


@pytest.mark.asyncio
async def test_adapter_converts_responses():
    class DummyAuth:
        bearer_token = "token"
        custom_headers: Dict[str, str] = {}

    class DummyConfig:
        base_url = "https://ronaldo-club.to/api/cards/vhq"
        default_headers: Dict[str, str] = {}
        authentication = DummyAuth()
        environment = "production"

    adapter = APIV2ExternalBrowseAdapter(DummyConfig())

    class StubService:
        def __init__(self):
            self.received = None

        async def list_items(self, params, user_id=None):
            self.received = ("list", params)
            return APIV2BrowseResponse(success=True, data={"data": []})

        async def get_filters(self, filter_name, params, user_id=None):
            self.received = ("filters", filter_name, params)
            return APIV2BrowseResponse(success=True, data={"filters": []})

        async def list_orders(self, page=1, limit=10):
            self.received = ("orders", page, limit)
            return APIV2BrowseResponse(success=True, data={"orders": []})

        async def close(self):
            return None

    stub = StubService()
    adapter._service = stub  # Replace networked service with stub

    resp = await adapter.list_items(ListItemsParams())
    assert isinstance(resp, APIResponse)
    assert resp.operation == APIOperation.LIST_ITEMS

    resp_filters = await adapter.get_filters("bank", ListItemsParams())
    assert isinstance(resp_filters, APIResponse)
    assert resp_filters.operation == APIOperation.FILTERS

    resp_orders = await adapter.list_orders(page=2, limit=3)
    assert resp_orders.operation == APIOperation.LIST_ORDERS
