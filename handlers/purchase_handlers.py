"""
Purchase confirmation and processing handlers
"""

from __future__ import annotations

import logging
from typing import Optional

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

from middleware import attach_common_middlewares
from services.user_service import UserService
from services.cart_service import CartService
from utils.texts import DEMO_WATERMARK

logger = logging.getLogger(__name__)


class PurchaseHandlers:
    """Handlers for purchase confirmation and processing"""

    def __init__(self):
        self.user_service = UserService()
        self.cart_service = CartService()

    async def cb_purchase_confirm(self, callback: CallbackQuery) -> None:
        """Handle purchase confirmation - Queue checkout for processing"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user from database
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            user_id = str(db_user.id)
            telegram_user_id = user.id

            # Get cart contents for validation
            cart_contents = await self.cart_service.get_cart_contents(user_id)

            if cart_contents.get("is_empty", True):
                await callback.message.edit_text(
                    "🛒 <b>Cart Empty</b>\n\nYour cart is empty. Add some items first!"
                    + DEMO_WATERMARK,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [InlineKeyboardButton(text="🔎 Browse Cards", callback_data="menu:browse")],
                            [InlineKeyboardButton(text="🏠 Main Menu", callback_data="menu:main")]
                        ]
                    ),
                )
                await callback.answer()
                return

            # Queue checkout for processing
            success, message, job_id = await self.cart_service.queue_checkout(user_id, telegram_user_id)

            if success:
                # Format queue confirmation message
                queue_text = f"""
⏳ <b>Order Queued for Processing</b>

{message}

🔔 <b>What happens next:</b>
• Your order is now in the processing queue
• You'll receive notifications about progress
• Payment will be processed when your turn comes
• You can cancel the order while it's queued

{DEMO_WATERMARK}
"""

                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="❌ Cancel Order",
                                callback_data=f"purchase:cancel:{job_id}"
                            ),
                            InlineKeyboardButton(
                                text="📊 Queue Status",
                                callback_data=f"purchase:status:{job_id}"
                            ),
                        ],
                        [
                            InlineKeyboardButton(
                                text="📜 Order History", callback_data="menu:history"
                            ),
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu", callback_data="menu:main"
                            )
                        ],
                    ]
                )

                await callback.message.edit_text(queue_text, reply_markup=keyboard)
                await callback.answer("✅ Order queued!")

            else:
                # Format error message
                error_text = f"""
❌ <b>Cannot Queue Order</b>

{message}

Please check your wallet balance and cart contents.

{DEMO_WATERMARK}
"""

                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="💼 Check Wallet", callback_data="menu:wallet"
                            ),
                            InlineKeyboardButton(
                                text="🛒 View Cart", callback_data="local:cart:view"
                            ),
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu", callback_data="menu:main"
                            )
                        ],
                    ]
                )

                await callback.message.edit_text(error_text, reply_markup=keyboard)
                await callback.answer("❌ Cannot queue order", show_alert=True)

        except Exception as e:
            logger.error(f"Error in purchase confirmation: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_purchase_cancel(self, callback: CallbackQuery) -> None:
        """Handle purchase cancellation"""
        try:
            # Check if this is a queue cancellation
            parts = callback.data.split(":")
            if len(parts) >= 3 and parts[1] == "cancel":
                job_id = parts[2]
                await self._handle_queue_cancellation(callback, job_id)
                return

            # Handle regular purchase cancellation
            cancel_text = f"""
❌ <b>Purchase Cancelled</b>

Your purchase has been cancelled. Your items remain in your cart.

{DEMO_WATERMARK}
"""

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🛒 View Cart",
                            callback_data="local:cart:view"
                        ),
                        InlineKeyboardButton(
                            text="🔎 Continue Shopping",
                            callback_data="menu:browse"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Main Menu",
                            callback_data="menu:main"
                        )
                    ],
                ]
            )

            await callback.message.edit_text(cancel_text, reply_markup=keyboard)
            await callback.answer("Purchase cancelled")

        except Exception as e:
            logger.error(f"Error in purchase cancellation: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def _handle_queue_cancellation(self, callback: CallbackQuery, job_id: str) -> None:
        """Handle cancellation of a queued checkout job"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user from database
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Import checkout queue service
            from services.checkout_queue_service import CheckoutQueueService

            queue_service = CheckoutQueueService()
            success, message = await queue_service.cancel_checkout(str(db_user.id), job_id)

            if success:
                cancel_text = f"""
✅ <b>Order Cancelled</b>

{message}

Your items remain in your cart and no payment was processed.

{DEMO_WATERMARK}
"""
                await callback.answer("✅ Order cancelled")
            else:
                cancel_text = f"""
❌ <b>Cannot Cancel Order</b>

{message}

The order may have already been processed or completed.

{DEMO_WATERMARK}
"""
                await callback.answer("❌ Cannot cancel", show_alert=True)

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🛒 View Cart",
                            callback_data="local:cart:view"
                        ),
                        InlineKeyboardButton(
                            text="📜 Order History",
                            callback_data="menu:history"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Main Menu",
                            callback_data="menu:main"
                        )
                    ],
                ]
            )

            await callback.message.edit_text(cancel_text, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error cancelling queue job {job_id}: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_purchase_status(self, callback: CallbackQuery) -> None:
        """Handle purchase status check"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Extract job ID from callback data
            parts = callback.data.split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid status request", show_alert=True)
                return

            job_id = parts[2]

            # Get user from database
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Import checkout queue service
            from services.checkout_queue_service import CheckoutQueueService

            queue_service = CheckoutQueueService()
            job = await queue_service.get_job_status(job_id)

            if not job:
                status_text = f"""
❌ <b>Job Not Found</b>

Job #{job_id[:8]} was not found or has expired.

{DEMO_WATERMARK}
"""
            else:
                # Format status based on job state
                status_emoji = {
                    "queued": "⏳",
                    "processing": "🔄",
                    "completed": "✅",
                    "failed": "❌",
                    "cancelled": "❌"
                }

                emoji = status_emoji.get(job.status.value, "❓")
                status_name = job.status.value.title()

                if job.status.value == "queued":
                    queue_position = await queue_service._get_queue_position(job_id)
                    estimated_wait = queue_position * 30  # 30 seconds per job
                    status_details = f"Position #{queue_position} in line\nEstimated wait: {estimated_wait // 60}m {estimated_wait % 60}s"
                elif job.status.value == "processing":
                    status_details = "Your order is being processed..."
                elif job.status.value == "completed":
                    status_details = "Order completed successfully!"
                elif job.status.value == "failed":
                    status_details = f"Order failed: {job.last_error or 'Unknown error'}"
                elif job.status.value == "cancelled":
                    status_details = "Order was cancelled"
                else:
                    status_details = "Status unknown"

                status_text = f"""
{emoji} <b>Order Status: {status_name}</b>

🆔 <b>Job ID:</b> {job_id[:8]}
📅 <b>Created:</b> {job.created_at.strftime('%H:%M:%S UTC')}
📊 <b>Status:</b> {status_details}

{DEMO_WATERMARK}
"""

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Refresh",
                            callback_data=f"purchase:status:{job_id}"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Main Menu",
                            callback_data="menu:main"
                        )
                    ],
                ]
            )

            await callback.message.edit_text(status_text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error checking purchase status: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)


def get_purchase_router() -> Router:
    """Create and return purchase router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = PurchaseHandlers()

    # Purchase callback handlers
    router.callback_query.register(
        handlers.cb_purchase_confirm, F.data.startswith("purchase:confirm")
    )
    router.callback_query.register(
        handlers.cb_purchase_cancel, F.data.startswith("purchase:cancel")
    )
    router.callback_query.register(
        handlers.cb_purchase_status, F.data.startswith("purchase:status:")
    )

    logger.info("Purchase handlers registered")
    return router
