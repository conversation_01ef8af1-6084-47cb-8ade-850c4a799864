"""
Admin handlers for bot management with user management, stats, logs, and settings.
"""

from __future__ import annotations

import logging
from math import ceil

from aiogram import Router, F
from aiogram.filters import Command
from aiogram.types import Message, CallbackQuery
from aiogram.types.input_file import BufferedInputFile
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State

from config.settings import get_settings
from utils.keyboards import admin_menu_keyboard, back_keyboard, pagination_keyboard
from utils.texts import (
    ADMIN_UNAUTHORIZED,
    ADMIN_STATS_TEMPLATE,
    DEMO_WATERMARK,
    ERROR_PERMISSION_DENIED,
)
from utils.validation import (
    validate_telegram_id,
    validate_user_role,
    sanitize_text_input,
    ValidationError,
)
from middleware import attach_common_middlewares
from middleware.admin_permissions import AdminPermissionMiddleware
from services.user_service import UserService
from services.export_service import ExportService
from database.connection import get_collection

logger = logging.getLogger(__name__)

# Simple in-memory admin sessions and selections
ADMIN_SESSIONS: dict[int, float] = {}
ADMIN_SESSION_TTL_SECONDS = 30 * 60  # 30 minutes
USER_SELECTIONS: dict[int, set[str]] = {}


class AdminHandlers:
    """Admin-only handlers"""

    def __init__(self):
        self.settings = get_settings()
        self.user_service: UserService | None = None
        self.export_service = None
        self.audit = None

    def _is_admin(self, user_id: int) -> bool:
        """Check if user is admin with additional security checks"""
        if not user_id or user_id <= 0:
            return False
        return user_id in self.settings.admin_ids

    def _has_session(self, user_id: int) -> bool:
        """Check if admin has valid session"""
        import time

        expiry = ADMIN_SESSIONS.get(user_id)
        if expiry and expiry > time.time():
            return True

        # Clean expired session
        if user_id in ADMIN_SESSIONS:
            del ADMIN_SESSIONS[user_id]
        return False

    def _touch_session(self, user_id: int):
        """Create or refresh admin session"""
        import time

        ADMIN_SESSIONS[user_id] = time.time() + ADMIN_SESSION_TTL_SECONDS
        logger.info(f"Admin session created/refreshed for user {user_id}")

    def _require_admin_auth(self, user_id: int) -> tuple[bool, str]:
        """Comprehensive admin authentication check"""
        if not user_id or user_id <= 0:
            return False, "❌ Invalid user"

        if not self._is_admin(user_id):
            logger.warning(f"Unauthorized admin access attempt from user {user_id}")
            return False, "❌ Unauthorized access"

        if not self._has_session(user_id):
            return False, "🔒 Admin session required. Send /admin to unlock."

        return True, ""

    def _cleanup_expired_sessions(self):
        """Clean up expired admin sessions"""
        import time

        current_time = time.time()
        expired_sessions = [
            user_id
            for user_id, expiry in ADMIN_SESSIONS.items()
            if expiry <= current_time
        ]

        for user_id in expired_sessions:
            del ADMIN_SESSIONS[user_id]
            logger.info(f"Cleaned expired admin session for user {user_id}")

        return len(expired_sessions)

    async def _audit(self, actor_id: int, action: str, metadata: dict | None = None):
        try:
            from models.catalog import AuditLog

            if self.audit is None:
                self.audit = get_collection("audit_logs")
            log = AuditLog(
                actor_id=str(actor_id), action=action, metadata=metadata or {}, hash=""
            )
            await self.audit.insert_one(log.to_mongo())
        except Exception as e:
            logger.warning(f"Failed to write audit log: {e}")

    async def cmd_admin(self, message: Message, state: FSMContext) -> None:
        """Enhanced admin command with comprehensive security"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ Invalid request" + DEMO_WATERMARK)
                return

            if not self._is_admin(user.id):
                logger.warning(f"Unauthorized admin access attempt from user {user.id}")
                await self._audit(
                    user.id,
                    "admin_access_denied",
                    {"reason": "not_admin", "username": user.username or "unknown"},
                )
                await message.answer(ADMIN_UNAUTHORIZED + DEMO_WATERMARK)
                return

            # Clean up expired sessions
            cleaned = self._cleanup_expired_sessions()
            if cleaned > 0:
                logger.info(f"Cleaned {cleaned} expired admin sessions")

            # Check for passphrase requirement
            from os import getenv

            passphrase = getenv("ADMIN_PASSPHRASE", "").strip()

            if not passphrase:
                logger.error("Admin passphrase not configured")
                await message.answer(
                    "❌ <b>Configuration Error</b>\n\nAdmin authentication is not properly configured."
                    + DEMO_WATERMARK
                )
                return

            # Validate passphrase strength
            if len(passphrase) < 12:
                logger.error("Admin passphrase does not meet security requirements")
                await message.answer(
                    "❌ <b>Security Error</b>\n\nAdmin authentication configuration is insecure."
                    + DEMO_WATERMARK
                )
                return

            # Check if session exists
            if not self._has_session(user.id):
                await state.set_state(AdminAuthStates.waiting_passphrase)
                await message.answer(
                    "🔒 <b>Admin Authentication Required</b>\n\n"
                    "Please enter the admin passphrase to access the admin panel.\n\n"
                    "⚠️ <b>Security Notice:</b> This session will expire in 30 minutes."
                    + DEMO_WATERMARK
                )
                return

            # Valid session exists
            self._touch_session(user.id)
            await self._audit(
                user.id, "admin_panel_accessed", {"session_refreshed": True}
            )

            await message.answer(
                "🛠️ <b>Admin Panel</b>\n\n"
                "Welcome back to the admin panel. Choose an option:\n\n"
                f"📊 Session expires in {ADMIN_SESSION_TTL_SECONDS // 60} minutes"
                + DEMO_WATERMARK,
                reply_markup=admin_menu_keyboard(),
            )
        except Exception as e:
            logger.error(f"Error in admin command: {e}")
            await message.answer("❌ System error occurred" + DEMO_WATERMARK)

    async def cmd_admin_logout(self, message: Message) -> None:
        """Admin logout command for security"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ Invalid request" + DEMO_WATERMARK)
                return

            if not self._is_admin(user.id):
                await message.answer("❌ You are not an admin" + DEMO_WATERMARK)
                return

            # Remove session if exists
            if user.id in ADMIN_SESSIONS:
                del ADMIN_SESSIONS[user.id]
                await self._audit(user.id, "admin_logout", {"manual_logout": True})
                logger.info(f"Admin {user.id} logged out manually")
                await message.answer(
                    "✅ <b>Logged Out</b>\n\n"
                    "You have been successfully logged out from the admin panel.\n\n"
                    "Send /admin to log in again." + DEMO_WATERMARK
                )
            else:
                await message.answer(
                    "ℹ️ <b>No Active Session</b>\n\n"
                    "You don't have an active admin session.\n\n"
                    "Send /admin to log in." + DEMO_WATERMARK
                )
        except Exception as e:
            logger.error(f"Error in admin logout: {e}")
            await message.answer("❌ Error during logout" + DEMO_WATERMARK)

    # --- Stats & Logs -----------------------------------------------------------
    async def cb_admin_stats(self, callback: CallbackQuery) -> None:
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return
            if not self._has_session(user.id):
                await callback.answer(
                    "🔒 Admin session required. Send /admin to unlock.", show_alert=True
                )
                return

            if self.user_service is None:
                self.user_service = UserService()
            total_users = await self.user_service.count_users()
            stats_text = (
                ADMIN_STATS_TEMPLATE.format(
                    total_users=total_users,
                    active_today=0,
                    new_week=0,
                    total_balance=0.0,
                    avg_balance=0.0,
                    total_transactions=0,
                    transactions_today=0,
                    total_volume=0.0,
                    total_purchases=0,
                    success_rate=100.0,
                    total_revenue=0.0,
                )
                + DEMO_WATERMARK
            )

            await callback.message.edit_text(
                stats_text, reply_markup=back_keyboard("admin:menu")
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in admin stats: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_dashboard(self, callback: CallbackQuery) -> None:
        """Enhanced admin dashboard with real-time statistics"""
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer(ADMIN_UNAUTHORIZED, show_alert=True)
                return

            # Generate comprehensive admin summary
            if self.export_service is None:
                self.export_service = ExportService()
            summary = await self.export_service.generate_admin_summary()

            dashboard_text = f"""
🔧 <b>Enhanced Admin Dashboard</b>

👥 <b>Users:</b>
• Total: {summary['users']['total']}
• Active (7 days): {summary['users']['active_last_7_days']}
• Activity Rate: {summary['users']['activity_rate']}%

💳 <b>Transactions:</b>
• Total: {summary['transactions']['total']}
• Last 24h: {summary['transactions']['last_24_hours']}

💰 <b>Wallets:</b>
• Total Balance: ${summary['wallets']['total_balance']:.2f}
• Average Balance: ${summary['wallets']['average_balance']:.2f}
• Locked Wallets: {summary['wallets']['locked_count']}

📊 <b>System Health:</b>
• Database: 🟢 Connected
• Last Updated: {summary['generated_at'][:19]}

{DEMO_WATERMARK}
"""

            # Enhanced admin keyboard
            from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="👥 User Management", callback_data="admin:users"
                        ),
                        InlineKeyboardButton(
                            text="📊 Statistics", callback_data="admin:stats"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔄 Refresh", callback_data="admin:dashboard"
                        ),
                        InlineKeyboardButton(
                            text="⚙️ Settings", callback_data="admin:settings"
                        ),
                    ],
                    [InlineKeyboardButton(text="🔙 Back", callback_data="menu:main")],
                ]
            )

            await callback.message.edit_text(dashboard_text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in admin dashboard: {e}")
            await callback.answer("❌ Error loading dashboard", show_alert=True)

    async def cb_admin_logs(self, callback: CallbackQuery) -> None:
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return
            if not self._has_session(user.id):
                await callback.answer(
                    "🔒 Admin session required. Send /admin to unlock.", show_alert=True
                )
                return

            # Ensure audit collection is available
            if self.audit is None:
                self.audit = get_collection("audit_logs")

            logs = await self.audit.find({}).sort("created_at", -1).limit(5).to_list(5)
            lines = [
                f"{l.get('created_at')} [{l.get('action')}] actor={l.get('actor_id')} meta={l.get('metadata')}"
                for l in logs
            ] or ["No logs yet"]
            logs_text = (
                """
📄 <b>Recent Audit Logs</b>

<code>{}</code>

<i>Showing last 5 log entries</i>
            """.format(
                    "\n".join(lines)
                )
                + DEMO_WATERMARK
            )

            await callback.message.edit_text(
                logs_text, reply_markup=back_keyboard("admin:menu")
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in admin logs: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    # --- Users -----------------------------------------------------------------
    async def cb_admin_users(self, callback: CallbackQuery) -> None:
        return await self._render_users_page(callback, page=1)

    async def cb_admin_users_page(self, callback: CallbackQuery) -> None:
        try:
            page = int(callback.data.split(":")[-1])
            return await self._render_users_page(callback, page)
        except Exception:
            await callback.answer("❌ Invalid page", show_alert=True)

    async def cb_admin_users_search(
        self, callback: CallbackQuery, state: FSMContext
    ) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        await state.set_state(AdminUserSearchStates.waiting_query)
        await callback.message.edit_text(
            "🔎 <b>Search Users</b>\n\nEnter a username or Telegram ID:"
            + DEMO_WATERMARK,
            reply_markup=back_keyboard("admin:users"),
        )
        await callback.answer()

    async def _render_users_page(self, callback: CallbackQuery, page: int = 1):
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        per_page = 5
        users = await self.user_service.list_users(page=page, per_page=per_page)
        total = await self.user_service.count_users()
        total_pages = max(1, ceil(total / per_page))
        lines = [
            f"{i+1 + (page-1)*per_page}. <b>{u.username or u.first_name or u.telegram_id}</b> | id: <code>{u.id}</code> | role: <code>{u.role}</code> | active: {'✅' if getattr(u, 'active', True) else '❌'}"
            for i, u in enumerate(users)
        ] or ["No users yet"]
        text = (
            "👥 <b>Users</b>\n\n"
            + "\n".join(lines)
            + "\n\nSelect a user to manage or export list."
        ) + DEMO_WATERMARK

        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        rows = [
            [
                InlineKeyboardButton(
                    text=f"🔧 Manage: {u.username or u.first_name or u.telegram_id}",
                    callback_data=f"admin:user:view:{u.id}",
                )
            ]
            for u in users
        ]
        rows.append(
            [
                InlineKeyboardButton(
                    text="📤 Export CSV", callback_data="admin:users:export"
                ),
                InlineKeyboardButton(
                    text="🔎 Search", callback_data="admin:users:search"
                ),
                InlineKeyboardButton(text="⬅️ Back", callback_data="admin:menu"),
            ]
        )
        pager = pagination_keyboard(
            page, total_pages, "admin:users:page", back_callback="admin:menu"
        )
        kb = InlineKeyboardMarkup(inline_keyboard=rows + pager.inline_keyboard)
        await callback.message.edit_text(text, reply_markup=kb)
        await callback.answer()

    async def cb_admin_users_export(self, callback: CallbackQuery) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        data = await self.user_service.export_users_csv()
        if not data:
            await callback.answer("No data to export", show_alert=True)
            return
        await callback.message.answer_document(
            BufferedInputFile(data, filename="users.csv"), caption="Users export"
        )
        await self._audit(callback.from_user.id, "export_users")
        await callback.answer()

    async def cb_admin_user_view(self, callback: CallbackQuery) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        _, _, _, user_id = callback.data.split(":", 3)
        target = await self.user_service.get_user_by_id(user_id)
        if not target:
            await callback.answer("User not found", show_alert=True)
            return
        text = (
            "👤 <b>User</b>\n\n"
            f"ID: <code>{target.id}</code>\n"
            f"Telegram: <code>{target.telegram_id}</code>\n"
            f"Username: {target.username or '-'}\n"
            f"Name: {target.first_name or '-'}\n"
            f"Role: <code>{target.role}</code>\n"
            f"Active: {'✅' if getattr(target, 'active', True) else '❌'}\n"
        ) + DEMO_WATERMARK

        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        kb = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="Make Admin",
                        callback_data=f"admin:user:setrole:admin:{target.id}",
                    ),
                    InlineKeyboardButton(
                        text="Make Mod",
                        callback_data=f"admin:user:setrole:moderator:{target.id}",
                    ),
                    InlineKeyboardButton(
                        text="Make User",
                        callback_data=f"admin:user:setrole:user:{target.id}",
                    ),
                ],
                [
                    InlineKeyboardButton(
                        text=(
                            "Deactivate"
                            if getattr(target, "active", True)
                            else "Activate"
                        ),
                        callback_data=f"admin:user:toggle:{target.id}",
                    )
                ],
                [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:users")],
            ]
        )
        await callback.message.edit_text(text, reply_markup=kb)
        await callback.answer()

    async def cb_admin_user_setrole(self, callback: CallbackQuery) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        _, _, _, role, user_id = callback.data.split(":", 4)
        ok = await self.user_service.set_user_role(user_id, role)
        if ok:
            await self._audit(
                callback.from_user.id, "set_role", {"target": user_id, "role": role}
            )
            await callback.answer("✅ Role updated")
            callback.data = f"admin:user:view:{user_id}"
            return await self.cb_admin_user_view(callback)
        else:
            await callback.answer("❌ Failed to update role", show_alert=True)

    async def cb_admin_user_toggle(self, callback: CallbackQuery) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        _, _, _, user_id = callback.data.split(":", 3)
        target = await self.user_service.get_user_by_id(user_id)
        if not target:
            await callback.answer("User not found", show_alert=True)
            return
        new_state = not getattr(target, "active", True)
        ok = await self.user_service.set_user_active(user_id, new_state)
        if ok:
            await self._audit(
                callback.from_user.id,
                "toggle_active",
                {"target": user_id, "active": new_state},
            )
            await callback.answer("✅ Updated")
            callback.data = f"admin:user:view:{user_id}"
            return await self.cb_admin_user_view(callback)
        else:
            await callback.answer("❌ Failed to update", show_alert=True)

    # --- Menu ------------------------------------------------------------------
    async def cb_admin_menu(self, callback: CallbackQuery) -> None:
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return
            await callback.message.edit_text(
                "🛠️ <b>Admin Panel</b>\n\nChoose an option:" + DEMO_WATERMARK,
                reply_markup=admin_menu_keyboard(),
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in admin menu: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_system_health(self, callback: CallbackQuery) -> None:
        """Handle system health monitoring"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            # Use comprehensive authentication check
            auth_ok, auth_msg = self._require_admin_auth(user.id)
            if not auth_ok:
                await callback.answer(auth_msg, show_alert=True)
                return

            # Get system health metrics
            health_data = await self._get_system_health()

            health_text = f"""
🚨 <b>System Health Monitor</b>

🔧 <b>Bot Status:</b> {'🟢 Online' if health_data['bot_online'] else '🔴 Offline'}
💾 <b>Database:</b> {'🟢 Connected' if health_data['db_connected'] else '🔴 Disconnected'}
🔧 <b>APIs:</b> {health_data['api_status']}

📊 <b>Performance Metrics:</b>
• CPU Usage: {health_data['cpu_usage']}%
• Memory Usage: {health_data['memory_usage']}%
• Active Sessions: {health_data['active_sessions']}
• Response Time: {health_data['response_time']}ms

⚠️ <b>Alerts:</b>
{health_data['alerts'] if health_data['alerts'] else '• No active alerts'}

{DEMO_WATERMARK}
"""

            from utils.keyboards import admin_system_health_keyboard

            await callback.message.edit_text(
                health_text, reply_markup=admin_system_health_keyboard()
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in system health: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_transactions(self, callback: CallbackQuery) -> None:
        """Handle transaction monitoring"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            # Use comprehensive authentication check
            auth_ok, auth_msg = self._require_admin_auth(user.id)
            if not auth_ok:
                await callback.answer(auth_msg, show_alert=True)
                return

            # Get transaction statistics
            tx_stats = await self._get_transaction_stats()

            tx_text = f"""
💳 <b>Transaction Monitoring</b>

📊 <b>Today's Summary:</b>
• Total Transactions: {tx_stats['today_total']}
• Total Volume: ${tx_stats['today_volume']:.2f}
• Average Amount: ${tx_stats['avg_amount']:.2f}
• Success Rate: {tx_stats['success_rate']:.1f}%

⚠️ <b>Flagged Transactions:</b>
• Suspicious: {tx_stats['suspicious_count']}
• High Amount: {tx_stats['high_amount_count']}
• Failed: {tx_stats['failed_count']}

📈 <b>Recent Activity:</b>
• Last Hour: {tx_stats['last_hour']} transactions
• Peak Hour: {tx_stats['peak_hour']}
• Busiest Day: {tx_stats['busiest_day']}

{DEMO_WATERMARK}
"""

            from utils.keyboards import admin_transactions_keyboard

            await callback.message.edit_text(
                tx_text, reply_markup=admin_transactions_keyboard()
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in transaction monitoring: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_database(self, callback: CallbackQuery) -> None:
        """Handle database management"""
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return

            # Get database statistics
            db_stats = await self._get_database_stats()

            db_text = f"""
🗂️ <b>Database Management</b>

📊 <b>Collections Overview:</b>
• Users: {db_stats['users_count']} documents
• Transactions: {db_stats['transactions_count']} documents
• Cards: {db_stats['cards_count']} documents
• Sessions: {db_stats['sessions_count']} documents

💾 <b>Storage Info:</b>
• Total Size: {db_stats['total_size']} MB
• Index Size: {db_stats['index_size']} MB
• Free Space: {db_stats['free_space']} MB

🔧 <b>Performance:</b>
• Avg Query Time: {db_stats['avg_query_time']}ms
• Active Connections: {db_stats['active_connections']}
• Last Backup: {db_stats['last_backup']}

{DEMO_WATERMARK}
"""

            from utils.keyboards import admin_database_keyboard

            await callback.message.edit_text(
                db_text, reply_markup=admin_database_keyboard()
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in database management: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_emergency(self, callback: CallbackQuery) -> None:
        """Handle emergency controls"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            # Use comprehensive authentication check
            auth_ok, auth_msg = self._require_admin_auth(user.id)
            if not auth_ok:
                await callback.answer(auth_msg, show_alert=True)
                return

            emergency_text = f"""
🚨 <b>Emergency Controls</b>

⚠️ <b>WARNING:</b> These controls can affect all users and system operations.
Use with extreme caution and only during emergencies.

🔧 <b>Available Actions:</b>
• Disable Bot - Temporarily disable bot for maintenance
• Emergency Broadcast - Send message to all users
• Lock Accounts - Temporarily lock all user accounts
• Stop Services - Stop non-critical background services
• Restart Bot - Restart the bot application
• Clear Cache - Clear all cached data

📋 <b>Current Status:</b>
• Bot Status: 🟢 Online
• Services: 🟢 All Running
• Emergency Mode: 🔴 Disabled

{DEMO_WATERMARK}
"""

            from utils.keyboards import admin_emergency_keyboard

            await callback.message.edit_text(
                emergency_text, reply_markup=admin_emergency_keyboard()
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in emergency controls: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_audit(self, callback: CallbackQuery) -> None:
        """Handle audit logs"""
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return

            # Get recent audit logs
            audit_stats = await self._get_audit_stats()

            audit_text = f"""
📋 <b>Audit Logs</b>

📊 <b>Recent Activity Summary:</b>
• Total Logs: {audit_stats['total_logs']}
• Today's Entries: {audit_stats['today_entries']}
• User Actions: {audit_stats['user_actions']}
• Admin Actions: {audit_stats['admin_actions']}
• Security Events: {audit_stats['security_events']}

⚠️ <b>Recent Security Events:</b>
{audit_stats['recent_security'] if audit_stats['recent_security'] else '• No recent security events'}

🔍 <b>Most Recent Actions:</b>
{audit_stats['recent_actions']}

{DEMO_WATERMARK}
"""

            from utils.keyboards import admin_audit_keyboard

            await callback.message.edit_text(
                audit_text, reply_markup=admin_audit_keyboard()
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in audit logs: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_help(self, callback: CallbackQuery) -> None:
        """Handle admin help"""
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return

            help_text = f"""
❓ <b>Admin Panel Help</b>

📖 <b>Quick Start Guide:</b>
• Use 📊 Dashboard for system overview
• Monitor 🚨 System Health regularly
• Check 💳 Transactions for suspicious activity
• Use 🗂️ Database for maintenance tasks

🔧 <b>Key Features:</b>
• User Management - View, edit, ban users
• Transaction Monitoring - Track all payments
• System Health - Monitor performance
• Database Tools - Backup, cleanup, stats
• Emergency Controls - Critical system actions
• Audit Logs - Track all admin activities

⚠️ <b>Security Best Practices:</b>
• Always log out after admin sessions
• Use emergency controls only when necessary
• Monitor audit logs for suspicious activity
• Keep admin credentials secure

{DEMO_WATERMARK}
"""

            from utils.keyboards import admin_help_keyboard

            await callback.message.edit_text(
                help_text, reply_markup=admin_help_keyboard()
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in admin help: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_catalog(self, callback: CallbackQuery) -> None:
        """Handle admin catalog management"""
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return

            catalog_text = f"""
📋 <b>Catalog Management</b>

📊 <b>Statistics:</b>
• Total Cards: Demo data available
• Active Cards: All demo cards active
• Categories: Multiple categories
• Last Updated: Real-time

🔧 <b>Management Options:</b>
• Add new cards to catalog
• Update existing card information
• Manage categories and filters
• Import/Export catalog data
• Monitor card availability

{DEMO_WATERMARK}
"""

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="➕ Add Cards", callback_data="admin:catalog:add"
                        ),
                        InlineKeyboardButton(
                            text="✏️ Edit Cards", callback_data="admin:catalog:edit"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="📊 Statistics", callback_data="admin:catalog:stats"
                        ),
                        InlineKeyboardButton(
                            text="🏷️ Categories",
                            callback_data="admin:catalog:categories",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="📤 Export", callback_data="admin:catalog:export"
                        ),
                        InlineKeyboardButton(
                            text="📥 Import", callback_data="admin:catalog:import"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔄 Refresh Data",
                            callback_data="admin:catalog:refresh",
                        ),
                    ],
                    [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:menu")],
                ]
            )

            await callback.message.edit_text(catalog_text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in admin catalog: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_catalog_stats(self, callback: CallbackQuery) -> None:
        """Show basic catalog statistics from `catalog_items` collection."""
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return

            coll = get_collection("catalog_items")

            total = await coll.count_documents({})
            active = await coll.count_documents({"active": True})
            inactive = total - active

            # Aggregate by brand and type (top 5)
            pipeline = [
                {"$group": {"_id": "$brand", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}},
                {"$limit": 5},
            ]
            brands = await coll.aggregate(pipeline).to_list(length=5)

            pipeline_type = [
                {"$group": {"_id": "$type", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}},
            ]
            types = await coll.aggregate(pipeline_type).to_list(length=None)

            # Average price
            pipeline_price = [{"$group": {"_id": None, "avgPrice": {"$avg": "$price"}}}]
            avg_doc = await coll.aggregate(pipeline_price).to_list(length=1)
            avg_price = (avg_doc[0]["avgPrice"] if avg_doc else 0.0) or 0.0

            text = (
                "📊 <b>Catalog Statistics</b>\n\n"
                f"• Total items: <b>{total}</b>\n"
                f"• Active: <b>{active}</b> | Inactive: <b>{inactive}</b>\n"
                f"• Avg price: <b>${avg_price:.2f}</b>\n\n"
                "🏷️ <b>Top Brands</b>:\n"
                + (
                    "\n".join(
                        [f"  • {b['_id'] or 'Unknown'}: {b['count']}" for b in brands]
                    )
                    or "  • No data"
                )
                + "\n\n💳 <b>Types</b>:\n"
                + (
                    "\n".join(
                        [f"  • {t['_id'] or 'Unknown'}: {t['count']}" for t in types]
                    )
                    or "  • No data"
                )
                + DEMO_WATERMARK
            )

            from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:catalog")]
                ]
            )

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in catalog stats: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_catalog_categories(self, callback: CallbackQuery) -> None:
        """List distinct brands and types as categories."""
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return

            coll = get_collection("catalog_items")
            brands = await coll.distinct("brand")
            types = await coll.distinct("type")

            def fmt_list(values: list[str | None]) -> str:
                vals = [v for v in values if v]
                if not vals:
                    return "  • None"
                vals.sort()
                # Limit to avoid overly long messages
                shown = vals[:30]
                lines = [f"  • {v}" for v in shown]
                if len(vals) > len(shown):
                    lines.append(f"  • … and {len(vals) - len(shown)} more")
                return "\n".join(lines)

            text = (
                "🏷️ <b>Catalog Categories</b>\n\n"
                "<b>Brands</b>:\n"
                + fmt_list(brands)
                + "\n\n"
                + "<b>Types</b>:\n"
                + fmt_list(types)
                + DEMO_WATERMARK
            )

            from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:catalog")]
                ]
            )
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in catalog categories: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_catalog_refresh(self, callback: CallbackQuery) -> None:
        """Refresh catalog caches (best effort)."""
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return

            # Best-effort: nothing to refresh persistently yet; respond OK
            await callback.answer("🔄 Catalog refreshed", show_alert=False)
        except Exception as e:
            logger.error(f"Error refreshing catalog: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_apis(self, callback: CallbackQuery) -> None:
        """Redirect to API configuration management"""
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return

            if not self._has_session(user.id):
                await callback.answer(
                    "🔒 Admin session required. Send /admin to unlock.", show_alert=True
                )
                return

            # Import and call the API config main handler
            from handlers.admin_api_config_handlers import callback_api_config_main

            await callback_api_config_main(callback)

        except Exception as e:
            logger.error(f"Error in API config redirect: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_admin_other(self, callback: CallbackQuery) -> None:
        try:
            user = callback.from_user
            if not user or not self._is_admin(user.id):
                await callback.answer("❌ Unauthorized", show_alert=True)
                return
            action = callback.data.split(":", 1)[1]
            action_name = action.replace(":", " ").replace("_", " ").title()
            await callback.message.edit_text(
                f"🛠️ <b>{action_name}</b>\n\nThis admin feature is not implemented in the demo."
                + DEMO_WATERMARK,
                reply_markup=back_keyboard("admin:menu"),
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in admin generic action: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    # --- Settings ---------------------------------------------------------------
    async def cb_admin_settings(self, callback: CallbackQuery) -> None:
        return await self._render_settings(callback, page=1)

    async def cb_admin_settings_page(self, callback: CallbackQuery) -> None:
        try:
            page = int(callback.data.split(":")[-1])
            return await self._render_settings(callback, page)
        except Exception:
            await callback.answer("❌ Invalid page", show_alert=True)

    async def _render_settings(self, callback: CallbackQuery, page: int = 1):
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        settings_coll = get_collection("app_settings")
        docs = await settings_coll.find({}).sort("key", 1).to_list(None)
        per_page = 10
        from math import ceil

        total_pages = max(1, ceil(len(docs) / per_page))
        start = max(0, (page - 1) * per_page)
        subset = docs[start : start + per_page]
        lines = [
            f"• <b>{d.get('key')}</b> = <code>{(d.get('value') or '')[:40]}</code>"
            for d in subset
        ] or ["No settings"]
        text = ("⚙️ <b>App Settings</b>\n\n" + "\n".join(lines)) + DEMO_WATERMARK

        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        rows = [
            [
                InlineKeyboardButton(
                    text=f"Edit: {d.get('key')}",
                    callback_data=f"admin:setting:edit:{d.get('key')}",
                )
            ]
            for d in subset
        ]
        rows.append(
            [
                InlineKeyboardButton(
                    text="➕ Add Setting", callback_data="admin:setting:add"
                ),
                InlineKeyboardButton(text="⬅️ Back", callback_data="admin:menu"),
            ]
        )
        pager = pagination_keyboard(
            page, total_pages, "admin:settings:page", back_callback="admin:menu"
        )
        kb = InlineKeyboardMarkup(inline_keyboard=rows + pager.inline_keyboard)
        await callback.message.edit_text(text, reply_markup=kb)
        await callback.answer()

    async def cb_admin_setting_edit(
        self, callback: CallbackQuery, state: FSMContext
    ) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        key = callback.data.split(":", 3)[-1]
        await state.set_state(AdminSettingStates.editing_value)
        await state.update_data(setting_key=key)
        await callback.message.edit_text(
            f"✏️ <b>Edit Setting</b>\n\nKey: <code>{key}</code>\n\nSend the new value:"
            + DEMO_WATERMARK,
            reply_markup=back_keyboard("admin:settings"),
        )
        await callback.answer()

    async def cb_admin_setting_add(
        self, callback: CallbackQuery, state: FSMContext
    ) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        await state.set_state(AdminSettingStates.waiting_key)
        await callback.message.edit_text(
            "➕ <b>Add Setting</b>\n\nSend the new key (alphanumeric, '_' or '-'):"
            + DEMO_WATERMARK,
            reply_markup=back_keyboard("admin:settings"),
        )
        await callback.answer()

    async def msg_user_search_query(self, message: Message, state: FSMContext) -> None:
        if not self._is_admin(message.from_user.id):
            await message.answer(ADMIN_UNAUTHORIZED + DEMO_WATERMARK)
            await state.clear()
            return
        query = (message.text or "").strip()
        users, total = await self.user_service.search_users(query, page=1, per_page=10)
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        rows = [
            [
                InlineKeyboardButton(
                    text=f"🔧 Manage: {u.username or u.first_name or u.telegram_id}",
                    callback_data=f"admin:user:view:{u.id}",
                )
            ]
            for u in users
        ]
        rows.append([InlineKeyboardButton(text="⬅️ Back", callback_data="admin:users")])
        kb = InlineKeyboardMarkup(inline_keyboard=rows)
        lines = [
            f"• <b>{u.username or u.first_name or u.telegram_id}</b> | id: <code>{u.id}</code> | role: <code>{u.role}</code>"
            for u in users
        ] or ["No results"]
        await message.answer(
            "🔎 <b>Search Results</b>\n\n" + "\n".join(lines) + DEMO_WATERMARK,
            reply_markup=kb,
        )
        await state.clear()

    async def msg_setting_add_key(self, message: Message, state: FSMContext) -> None:
        if not self._is_admin(message.from_user.id):
            await message.answer(ADMIN_UNAUTHORIZED + DEMO_WATERMARK)
            await state.clear()
            return
        key = (message.text or "").strip().lower()
        await state.update_data(setting_key=key)
        await state.set_state(AdminSettingStates.editing_value)
        await message.answer(
            f"✏️ Now send value for <code>{key}</code>" + DEMO_WATERMARK
        )

    async def msg_setting_set_value(self, message: Message, state: FSMContext) -> None:
        if not self._is_admin(message.from_user.id):
            await message.answer(ADMIN_UNAUTHORIZED + DEMO_WATERMARK)
            await state.clear()
            return
        data = await state.get_data()
        key = data.get("setting_key")
        val = message.text or ""
        coll = get_collection("app_settings")
        await coll.update_one({"key": key}, {"$set": {"key": key, "value": val}})
        # Ensure exists (works for both motor and in-memory)
        existing = await coll.find_one({"key": key})
        if not existing:
            await coll.insert_one({"key": key, "value": val})
        await self._audit(message.from_user.id, "setting_update", {"key": key})
        await message.answer(
            f"✅ Setting <code>{key}</code> updated" + DEMO_WATERMARK,
            reply_markup=back_keyboard("admin:settings"),
        )
        await state.clear()

    async def msg_admin_auth_passphrase(
        self, message: Message, state: FSMContext
    ) -> None:
        """Enhanced admin authentication with comprehensive security"""
        from os import getenv
        import time

        from utils.security import check_rate_limit_security

        user_id = message.from_user.id

        # Clean up expired sessions first
        self._cleanup_expired_sessions()

        # Enhanced rate limiting for admin authentication attempts
        if not check_rate_limit_security(
            user_id, "admin_auth", max_attempts=3, window_seconds=300
        ):
            await self._audit(
                user_id,
                "admin_auth_rate_limited",
                {"ip": "unknown", "user_agent": "telegram"},
            )
            logger.warning(f"Admin auth rate limited for user {user_id}")
            await message.answer(
                "🚫 <b>Security Alert</b>\n\nToo many authentication attempts detected. "
                "Please wait 5 minutes before trying again.\n\n"
                "If this wasn't you, please contact the system administrator."
                + DEMO_WATERMARK
            )
            await state.clear()
            return

        # Validate passphrase configuration
        passphrase = getenv("ADMIN_PASSPHRASE", "").strip()
        if not passphrase:
            logger.error("Admin passphrase not configured in environment")
            await message.answer(
                "❌ <b>Configuration Error</b>\n\nAdmin authentication is not properly configured. "
                "Please contact the system administrator." + DEMO_WATERMARK
            )
            await state.clear()
            return

        # Validate passphrase strength (minimum requirements)
        if len(passphrase) < 12:
            logger.error("Admin passphrase does not meet minimum security requirements")
            await message.answer(
                "❌ <b>Security Error</b>\n\nAdmin authentication configuration does not meet "
                "security requirements. Please contact the system administrator."
                + DEMO_WATERMARK
            )
            await state.clear()
            return

        # Validate user input
        user_input = (message.text or "").strip()
        if not user_input:
            await message.answer(
                "❌ <b>Invalid Input</b>\n\nPlease enter the admin passphrase."
                + DEMO_WATERMARK
            )
            await state.clear()
            return

        # Additional input validation
        if len(user_input) > 256:  # Prevent potential DoS
            await message.answer(
                "❌ <b>Invalid Input</b>\n\nPassphrase too long." + DEMO_WATERMARK
            )
            await state.clear()
            return

        # Secure passphrase comparison with timing attack protection
        auth_success = self._secure_compare_passphrase(user_input, passphrase)

        if auth_success:
            # Successful authentication
            self._touch_session(user_id)
            await self._audit(
                user_id,
                "admin_auth_success",
                {
                    "timestamp": time.time(),
                    "session_duration": ADMIN_SESSION_TTL_SECONDS,
                },
            )
            logger.info(f"Admin authentication successful for user {user_id}")

            await message.answer(
                "✅ <b>Authentication Successful</b>\n\n"
                "🛠️ <b>Admin Panel</b>\n\nWelcome to the admin panel. Choose an option:"
                + DEMO_WATERMARK,
                reply_markup=admin_menu_keyboard(),
            )
        else:
            # Failed authentication
            await self._audit(
                user_id,
                "admin_auth_failed",
                {"timestamp": time.time(), "input_length": len(user_input)},
            )
            logger.warning(f"Failed admin authentication attempt from user {user_id}")

            await message.answer(
                "❌ <b>Authentication Failed</b>\n\n"
                "Incorrect passphrase. Please try again or send /admin to restart."
                + DEMO_WATERMARK
            )

        await state.clear()

    def _secure_compare_passphrase(
        self, user_input: str, stored_passphrase: str
    ) -> bool:
        """Secure passphrase comparison to prevent timing attacks"""
        import hmac

        return hmac.compare_digest(
            user_input.encode("utf-8"), stored_passphrase.encode("utf-8")
        )

    async def _get_system_health(self) -> dict:
        """Get system health metrics"""
        try:
            # Try to import psutil, fallback to mock data if not available
            try:
                import psutil

                cpu_usage = psutil.cpu_percent(interval=0.1)
                memory = psutil.virtual_memory()
                memory_usage = memory.percent
            except ImportError:
                # Mock system metrics if psutil is not available
                cpu_usage = 25.5
                memory_usage = 45.2

            from database.connection import get_database

            # Database connectivity check
            try:
                db = get_database()
                await db.command("ping")
                db_connected = True
            except Exception:
                db_connected = False

            # Active sessions count
            active_sessions = len(ADMIN_SESSIONS)

            # Mock response time (in real implementation, measure actual response time)
            response_time = 45

            # Check for alerts
            alerts = []
            if cpu_usage > 80:
                alerts.append("• High CPU usage detected")
            if memory_usage > 85:
                alerts.append("• High memory usage detected")
            if not db_connected:
                alerts.append("• Database connection issues")

            return {
                "bot_online": True,
                "db_connected": db_connected,
                "api_status": "🟢 All APIs operational",
                "cpu_usage": round(cpu_usage, 1),
                "memory_usage": round(memory_usage, 1),
                "active_sessions": active_sessions,
                "response_time": response_time,
                "alerts": "\n".join(alerts) if alerts else None,
            }
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {
                "bot_online": True,
                "db_connected": False,
                "api_status": "⚠️ Unable to check APIs",
                "cpu_usage": 0,
                "memory_usage": 0,
                "active_sessions": 0,
                "response_time": 0,
                "alerts": "• Error retrieving system metrics",
            }

    async def _get_transaction_stats(self) -> dict:
        """Get transaction statistics"""
        try:
            from database.connection import get_collection
            from datetime import datetime, timedelta

            tx_collection = get_collection("transactions")

            # Get today's date range
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            tomorrow = today + timedelta(days=1)

            # Today's transactions
            today_cursor = tx_collection.find(
                {"created_at": {"$gte": today, "$lt": tomorrow}}
            )
            today_txs = await today_cursor.to_list(length=None)

            today_total = len(today_txs)
            today_volume = sum(tx.get("amount", 0) for tx in today_txs)
            avg_amount = today_volume / today_total if today_total > 0 else 0

            # Success rate calculation
            successful = sum(1 for tx in today_txs if tx.get("status") == "completed")
            success_rate = (successful / today_total * 100) if today_total > 0 else 100

            # Mock suspicious transaction counts
            suspicious_count = max(0, today_total // 20)  # ~5% flagged as suspicious
            high_amount_count = sum(1 for tx in today_txs if tx.get("amount", 0) > 100)
            failed_count = today_total - successful

            # Last hour activity
            last_hour = datetime.now() - timedelta(hours=1)
            last_hour_cursor = tx_collection.find({"created_at": {"$gte": last_hour}})
            last_hour_txs = await last_hour_cursor.to_list(length=None)

            return {
                "today_total": today_total,
                "today_volume": today_volume,
                "avg_amount": avg_amount,
                "success_rate": success_rate,
                "suspicious_count": suspicious_count,
                "high_amount_count": high_amount_count,
                "failed_count": failed_count,
                "last_hour": len(last_hour_txs),
                "peak_hour": "14:00-15:00",  # Mock data
                "busiest_day": "Monday",  # Mock data
            }
        except Exception as e:
            logger.error(f"Error getting transaction stats: {e}")
            return {
                "today_total": 0,
                "today_volume": 0,
                "avg_amount": 0,
                "success_rate": 0,
                "suspicious_count": 0,
                "high_amount_count": 0,
                "failed_count": 0,
                "last_hour": 0,
                "peak_hour": "N/A",
                "busiest_day": "N/A",
            }

    async def _get_database_stats(self) -> dict:
        """Get database statistics"""
        try:
            from database.connection import get_database, get_collection

            db = get_database()

            # Get collection counts
            users_count = await get_collection("users").count_documents({})
            transactions_count = await get_collection("transactions").count_documents(
                {}
            )
            cards_count = await get_collection("cards").count_documents({})
            sessions_count = len(ADMIN_SESSIONS)

            # Get database stats
            stats = await db.command("dbStats")
            total_size = round(stats.get("dataSize", 0) / (1024 * 1024), 2)  # MB
            index_size = round(stats.get("indexSize", 0) / (1024 * 1024), 2)  # MB

            # Mock additional stats
            free_space = 1024  # MB
            avg_query_time = 15  # ms
            active_connections = stats.get("connections", {}).get("current", 1)

            return {
                "users_count": users_count,
                "transactions_count": transactions_count,
                "cards_count": cards_count,
                "sessions_count": sessions_count,
                "total_size": total_size,
                "index_size": index_size,
                "free_space": free_space,
                "avg_query_time": avg_query_time,
                "active_connections": active_connections,
                "last_backup": "Never (Demo Mode)",
            }
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {
                "users_count": 0,
                "transactions_count": 0,
                "cards_count": 0,
                "sessions_count": 0,
                "total_size": 0,
                "index_size": 0,
                "free_space": 0,
                "avg_query_time": 0,
                "active_connections": 0,
                "last_backup": "Error",
            }

    async def _get_audit_stats(self) -> dict:
        """Get audit log statistics"""
        try:
            from database.connection import get_collection
            from datetime import datetime, timedelta

            audit_collection = get_collection("audit_logs")

            # Get today's date range
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

            # Total logs count
            total_logs = await audit_collection.count_documents({})

            # Today's entries
            today_entries = await audit_collection.count_documents(
                {"created_at": {"$gte": today}}
            )

            # Count by action type
            user_actions = await audit_collection.count_documents(
                {"action": {"$regex": "^user_"}}
            )
            admin_actions = await audit_collection.count_documents(
                {"action": {"$regex": "^admin_"}}
            )
            security_events = await audit_collection.count_documents(
                {
                    "action": {
                        "$in": [
                            "login_failed",
                            "admin_auth_failed",
                            "suspicious_activity",
                        ]
                    }
                }
            )

            # Recent security events
            recent_security_cursor = (
                audit_collection.find(
                    {
                        "action": {
                            "$in": [
                                "login_failed",
                                "admin_auth_failed",
                                "suspicious_activity",
                            ]
                        },
                        "created_at": {"$gte": datetime.now() - timedelta(hours=24)},
                    }
                )
                .sort("created_at", -1)
                .limit(3)
            )

            recent_security_logs = await recent_security_cursor.to_list(length=3)
            recent_security = []
            for log in recent_security_logs:
                action = log.get("action", "unknown")
                timestamp = log.get("created_at", datetime.now())
                if hasattr(timestamp, "strftime"):
                    time_str = timestamp.strftime("%H:%M")
                else:
                    time_str = "unknown"
                recent_security.append(f"• {action} at {time_str}")

            # Recent actions
            recent_cursor = audit_collection.find({}).sort("created_at", -1).limit(5)
            recent_logs = await recent_cursor.to_list(length=5)
            recent_actions = []
            for log in recent_logs:
                action = log.get("action", "unknown")
                actor = log.get("actor_id", "unknown")
                timestamp = log.get("created_at", datetime.now())
                if hasattr(timestamp, "strftime"):
                    time_str = timestamp.strftime("%H:%M")
                else:
                    time_str = "unknown"
                recent_actions.append(f"• {action} by {actor} at {time_str}")

            return {
                "total_logs": total_logs,
                "today_entries": today_entries,
                "user_actions": user_actions,
                "admin_actions": admin_actions,
                "security_events": security_events,
                "recent_security": (
                    "\n".join(recent_security) if recent_security else None
                ),
                "recent_actions": (
                    "\n".join(recent_actions)
                    if recent_actions
                    else "• No recent actions"
                ),
            }
        except Exception as e:
            logger.error(f"Error getting audit stats: {e}")
            return {
                "total_logs": 0,
                "today_entries": 0,
                "user_actions": 0,
                "admin_actions": 0,
                "security_events": 0,
                "recent_security": None,
                "recent_actions": "• Error loading recent actions",
            }

    # --- Roles -----------------------------------------------------------------
    async def cb_admin_roles(self, callback: CallbackQuery) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        coll = get_collection("roles")
        docs = await coll.find({}).to_list(None)
        if not docs:
            defaults = [
                {"name": "admin", "permissions": ["*"]},
                {
                    "name": "moderator",
                    "permissions": ["users:view", "users:edit", "settings:view"],
                },
                {"name": "user", "permissions": ["self:view"]},
            ]
            for d in defaults:
                await coll.insert_one(d)
            docs = await coll.find({}).to_list(None)
        lines = [
            f"• <b>{d.get('name')}</b> → <code>{','.join(d.get('permissions') or [])}</code>"
            for d in docs
        ]
        text = (
            "🔐 <b>Roles</b>\n\n"
            + "\n".join(lines)
            + "\n\nSelect a role to edit permissions."
        ) + DEMO_WATERMARK
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        rows = [
            [
                InlineKeyboardButton(
                    text=f"Edit: {d.get('name')}",
                    callback_data=f"admin:role:edit:{d.get('name')}",
                )
            ]
            for d in docs
        ]
        rows.append([InlineKeyboardButton(text="⬅️ Back", callback_data="admin:menu")])
        kb = InlineKeyboardMarkup(inline_keyboard=rows)
        await callback.message.edit_text(text, reply_markup=kb)
        await callback.answer()

    async def cb_admin_role_edit(
        self, callback: CallbackQuery, state: FSMContext
    ) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        role = callback.data.split(":", 3)[-1]
        await state.set_state(AdminRoleStates.editing_perms)
        await state.update_data(role_name=role)
        await callback.message.edit_text(
            f"✏️ <b>Edit Role</b>\n\nRole: <code>{role}</code>\n\nSend a comma-separated list of permissions (e.g., <code>users:view,users:edit</code>):"
            + DEMO_WATERMARK,
            reply_markup=back_keyboard("admin:roles"),
        )
        await callback.answer()

    async def msg_role_set_perms(self, message: Message, state: FSMContext) -> None:
        if not self._is_admin(message.from_user.id):
            await message.answer(ADMIN_UNAUTHORIZED + DEMO_WATERMARK)
            await state.clear()
            return
        data = await state.get_data()
        role = data.get("role_name")
        perms = [p.strip() for p in (message.text or "").split(",") if p.strip()]
        coll = get_collection("roles")
        await coll.update_one(
            {"name": role}, {"$set": {"name": role, "permissions": perms}}
        )
        await message.answer(
            f"✅ Role <code>{role}</code> updated" + DEMO_WATERMARK,
            reply_markup=back_keyboard("admin:roles"),
        )
        await state.clear()

    # --- Users: Add -------------------------------------------------------------
    async def cb_admin_users_add(
        self, callback: CallbackQuery, state: FSMContext
    ) -> None:
        if not callback.from_user or not self._is_admin(callback.from_user.id):
            await callback.answer("❌ Unauthorized", show_alert=True)
            return
        if not self._has_session(callback.from_user.id):
            await callback.answer(
                "🔒 Admin session required. Send /admin to unlock.", show_alert=True
            )
            return
        await state.set_state(AdminUserAddState.waiting_input)
        await callback.message.edit_text(
            "➕ <b>Add User</b>\n\nSend details in one line as: <code>telegram_id,username,first_name,role,active</code>\nExample: <code>123456789,john,John,user,true</code>"
            + DEMO_WATERMARK,
            reply_markup=back_keyboard("admin:users"),
        )
        await callback.answer()

    async def msg_admin_add_user(self, message: Message, state: FSMContext) -> None:
        if not self._is_admin(message.from_user.id):
            await message.answer(ADMIN_UNAUTHORIZED + DEMO_WATERMARK)
            await state.clear()
            return
        parts = [p.strip() for p in (message.text or "").split(",")]
        if len(parts) < 5:
            await message.answer(
                "❌ Invalid format. Please send: <code>telegram_id,username,first_name,role,active</code>"
                + DEMO_WATERMARK
            )
            return
        try:
            telegram_id = int(parts[0])
            username = parts[1] or None
            first_name = parts[2] or None
            role = parts[3] or "user"
            active = parts[4].lower() in ("1", "true", "yes", "y")
        except Exception:
            await message.answer(
                "❌ Could not parse input. Try again." + DEMO_WATERMARK
            )
            return
        try:
            user = await self.user_service.create_user(
                telegram_id,
                username=username,
                first_name=first_name,
                role=role,
                active=active,
            )
            await self._audit(
                message.from_user.id,
                "create_user",
                {"telegram_id": telegram_id, "role": role},
            )
            await message.answer(
                f"✅ User created: <code>{user.id}</code> (tg: {telegram_id})"
                + DEMO_WATERMARK,
                reply_markup=back_keyboard("admin:users"),
            )
            await state.clear()
        except Exception as e:
            logger.error(f"Add user failed: {e}")
            await message.answer("❌ Failed to create user" + DEMO_WATERMARK)
            await state.clear()


class AdminUserSearchStates(StatesGroup):
    waiting_query = State()


class AdminSettingStates(StatesGroup):
    waiting_key = State()
    editing_value = State()


class AdminAuthStates(StatesGroup):
    waiting_passphrase = State()


class AdminRoleStates(StatesGroup):
    editing_perms = State()


class AdminUserAddState(StatesGroup):
    waiting_input = State()


def get_admin_router() -> Router:
    router = Router()
    attach_common_middlewares(router)
    # Admin permission guard for callbacks
    router.callback_query.middleware(AdminPermissionMiddleware())
    handlers = AdminHandlers()

    # Include API configuration management router
    from handlers.admin_api_config_handlers import router as api_config_router
    from handlers.admin_auth_profile_handlers import router as auth_profile_router

    router.include_router(api_config_router)
    router.include_router(auth_profile_router)

    # Command handlers
    router.message.register(handlers.cmd_admin, Command("admin"))
    router.message.register(handlers.cmd_admin_logout, Command("admin_logout"))

    # Callback handlers
    router.callback_query.register(handlers.cb_admin_menu, F.data == "admin:menu")
    router.callback_query.register(handlers.cb_admin_stats, F.data == "admin:stats")
    router.callback_query.register(handlers.cb_admin_logs, F.data == "admin:logs")
    router.callback_query.register(handlers.cb_admin_catalog, F.data == "admin:catalog")
    router.callback_query.register(
        handlers.cb_admin_catalog_stats, F.data == "admin:catalog:stats"
    )
    router.callback_query.register(
        handlers.cb_admin_catalog_categories, F.data == "admin:catalog:categories"
    )
    router.callback_query.register(
        handlers.cb_admin_catalog_refresh, F.data == "admin:catalog:refresh"
    )

    # Users
    router.callback_query.register(handlers.cb_admin_users, F.data == "admin:users")
    router.callback_query.register(
        handlers.cb_admin_users_export, F.data == "admin:users:export"
    )
    router.callback_query.register(
        handlers.cb_admin_users_page, F.data.startswith("admin:users:page:")
    )
    router.callback_query.register(
        handlers.cb_admin_user_view, F.data.startswith("admin:user:view:")
    )
    router.callback_query.register(
        handlers.cb_admin_user_setrole, F.data.startswith("admin:user:setrole:")
    )
    router.callback_query.register(
        handlers.cb_admin_user_toggle, F.data.startswith("admin:user:toggle:")
    )

    # Users search (FSM)
    router.callback_query.register(
        handlers.cb_admin_users_search, F.data == "admin:users:search"
    )
    router.message.register(
        handlers.msg_user_search_query, AdminUserSearchStates.waiting_query
    )

    # Settings management
    router.callback_query.register(
        handlers.cb_admin_settings, F.data == "admin:settings"
    )
    router.callback_query.register(
        handlers.cb_admin_settings_page, F.data.startswith("admin:settings:page:")
    )
    router.callback_query.register(
        handlers.cb_admin_setting_edit, F.data.startswith("admin:setting:edit:")
    )
    router.callback_query.register(
        handlers.cb_admin_setting_add, F.data == "admin:setting:add"
    )
    router.message.register(
        handlers.msg_setting_add_key, AdminSettingStates.waiting_key
    )
    router.message.register(
        handlers.msg_setting_set_value, AdminSettingStates.editing_value
    )
    router.message.register(
        handlers.msg_admin_auth_passphrase, AdminAuthStates.waiting_passphrase
    )

    # Roles management
    router.callback_query.register(handlers.cb_admin_roles, F.data == "admin:roles")
    router.callback_query.register(
        handlers.cb_admin_role_edit, F.data.startswith("admin:role:edit:")
    )
    router.message.register(handlers.msg_role_set_perms, AdminRoleStates.editing_perms)

    # Users add (FSM)
    router.callback_query.register(
        handlers.cb_admin_users_add, F.data == "admin:users:add"
    )
    router.message.register(
        handlers.msg_admin_add_user, AdminUserAddState.waiting_input
    )

    # API Configuration redirect handler
    router.callback_query.register(handlers.cb_admin_apis, F.data == "admin:apis")

    # Enhanced admin features
    router.callback_query.register(
        handlers.cb_admin_dashboard, F.data == "admin:dashboard"
    )
    router.callback_query.register(
        handlers.cb_admin_system_health, F.data == "admin:health"
    )
    router.callback_query.register(
        handlers.cb_admin_transactions, F.data == "admin:transactions"
    )
    router.callback_query.register(
        handlers.cb_admin_database, F.data == "admin:database"
    )
    router.callback_query.register(
        handlers.cb_admin_emergency, F.data == "admin:emergency"
    )
    router.callback_query.register(handlers.cb_admin_audit, F.data == "admin:audit")
    router.callback_query.register(handlers.cb_admin_help, F.data == "admin:help")

    # Fallback for other admin actions not implemented
    router.callback_query.register(handlers.cb_admin_other, F.data.startswith("admin:"))

    logger.info("Admin handlers registered")
    return router
