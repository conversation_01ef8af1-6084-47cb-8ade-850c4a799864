"""
User-related Telegram bot handlers
"""

from __future__ import annotations

import logging

from aiogram import Router, F
from aiogram.filters import Command
from aiogram.types import (
    Message,
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
)

from services.user_service import UserService
from services.health_service import HealthService
from services.product_service import ProductService
from database.connection import get_database
from utils.keyboards import main_menu_keyboard, help_menu_keyboard, enhanced_main_menu_keyboard
from utils.texts import (
    WELCOME_MESSAGE,
    DEMO_WATERMARK,
    HELP_GETTING_STARTED,
    HELP_FAQ,
    HELP_COMMANDS,
)
from middleware import attach_common_middlewares

logger = logging.getLogger(__name__)


class UserHandlers:
    """User-related message and callback handlers"""

    def __init__(self):
        self.user_service = UserService()
        self.health_service = HealthService()
        self.product_service = ProductService()

    async def cmd_start(self, message: Message) -> None:
        """Handle /start command with enhanced multi-product flow"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ Unable to identify user")
                return

            # Ensure user and wallet exist
            await self.user_service.ensure_user_and_wallet(
                telegram_id=user.id,
                username=user.username,
                first_name=user.first_name,
                language_code=user.language_code,
            )

            # Initialize user product preferences
            await self.product_service.get_user_preferences(user.id)

            # Get product configuration to check if wallet should be prominent
            config = await self.product_service.get_product_config()
            show_wallet_prominent = config.show_wallet_on_start

            # Get user's current selection for personalized welcome
            current_product, current_api = await self.product_service.get_user_current_selection(user.id)

            # Enhanced welcome message
            welcome_text = WELCOME_MESSAGE.format(first_name=user.first_name or "there")

            # Add product selection guidance
            welcome_text += "\n\n🛍️ <b>Multi-Product Platform</b>\n"
            welcome_text += "Choose from our available product categories:\n"
            welcome_text += "• 💳 <b>BIN Cards</b> - Bank identification data\n"
            welcome_text += "• 🗂️ <b>DUMP Cards</b> - Complete card datasets\n\n"

            if current_product and current_api:
                api_info = await self.product_service.get_api_info(current_api)
                if api_info:
                    welcome_text += f"📍 <i>Currently using: {current_product.value.upper()} → {api_info.name}</i>\n\n"
            else:
                welcome_text += "👆 <i>Select your preferred product type to get started!</i>\n\n"

            welcome_text += DEMO_WATERMARK

            await message.answer(
                welcome_text,
                reply_markup=enhanced_main_menu_keyboard(show_wallet_prominent)
            )

            logger.info("User %s started bot with enhanced flow", user.id, extra={"user_id": user.id})

        except Exception as e:
            logger.error(
                f"Error in start command: {e}",
                extra={"user_id": user.id if user else None},
            )
            await message.answer(
                "❌ An error occurred. Please try again later." + DEMO_WATERMARK
            )

    async def cmd_help(self, message: Message) -> None:
        """Handle /help command"""
        try:
            help_text = (
                """
🧭 <b>Demo Wallet Bot Commands</b>

<b>Basic Commands:</b>
/start - Start the bot and create wallet
/balance - View wallet balance
/help - Show this help message

<b>Wallet Commands:</b>
/addfunds &lt;amount&gt; - Add funds to wallet

<b>Catalog Commands:</b>
/filter - Set search filters
/search - Search catalog items

<b>Admin Commands:</b> (Admin only)
/admin_stats - View bot statistics
/admin_logs - View recent logs

<b>Data Commands:</b>
/delete_me - Delete all your data

Use the inline keyboard buttons for easier navigation!
            """
                + DEMO_WATERMARK
            )

            await message.answer(help_text, reply_markup=main_menu_keyboard())

        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await message.answer(
                "❌ An error occurred. Please try again later." + DEMO_WATERMARK
            )

    async def cmd_version(self, message: Message) -> None:
        """Show bot version information (safe and self-contained)."""
        try:
            from aiogram import __version__ as aiogram_version
            from utils.version import __app_name__, __version__
            from config.settings import get_settings

            settings = get_settings()

            text = (
                f"ℹ️ <b>About This Bot</b>\n\n"
                f"Name: {__app_name__}\n"
                f"Version: <code>{__version__}</code>\n"
                f"Aiogram: <code>{aiogram_version}</code>\n"
                f"Environment: <code>{settings.ENVIRONMENT}</code>\n"
            ) + DEMO_WATERMARK

            await message.answer(text, reply_markup=main_menu_keyboard())
        except Exception as e:
            logger.error(f"Error in version command: {e}")
            await message.answer("❌ Unable to show version info" + DEMO_WATERMARK)

    async def cmd_health(self, message: Message) -> None:
        """Enhanced health check command"""
        try:
            # Get comprehensive health data
            health_data = await self.health_service.get_system_health()

            # Format health status
            status_emoji = {
                "healthy": "🟢",
                "degraded": "🟡",
                "unhealthy": "🔴",
                "error": "❌",
            }

            emoji = status_emoji.get(health_data.get("status", "error"), "❓")
            uptime_hours = round(health_data.get("uptime_seconds", 0) / 3600, 1)

            db_info = health_data.get("database", {})
            perf_info = health_data.get("performance", {})

            health_text = f"""
📡 <b>System Health Check</b>

🤖 <b>Bot Status:</b> {emoji} {health_data.get("status", "unknown").title()}
⏱️ <b>Uptime:</b> {uptime_hours} hours

💾 <b>Database:</b>
• Status: {'🟢 Connected' if db_info.get('connected') else '🔴 Disconnected'}
• Type: {db_info.get('type', 'Unknown')}
• Ping: {db_info.get('ping_time_ms', 0):.1f}ms
• Users: {db_info.get('user_count', 0)}

⚡ <b>Performance:</b>
• Avg Response: {perf_info.get('avg_response_time', 0):.3f}s
• Operations Tracked: {perf_info.get('operations_tracked', 0)}

{DEMO_WATERMARK}
"""

            await message.answer(health_text)

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            await message.answer("❌ Health check failed" + DEMO_WATERMARK)


    async def cb_main_menu(self, callback: CallbackQuery) -> None:
        """Handle main menu callback with enhanced product-aware interface"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get product configuration and user preferences
            config = await self.product_service.get_product_config()
            current_product, current_api = await self.product_service.get_user_current_selection(user.id)

            # Create enhanced main menu text
            menu_text = "🧭 <b>Main Menu</b>\n\n"

            # Show current selection if any
            if current_product and current_api:
                api_info = await self.product_service.get_api_info(current_api)
                if api_info:
                    menu_text += f"📍 <b>Current Selection:</b>\n"
                    menu_text += f"   {current_product.value.upper()} → {api_info.name}\n\n"

            menu_text += "Choose an option below to continue:\n\n"
            menu_text += DEMO_WATERMARK

            await callback.message.edit_text(
                menu_text,
                reply_markup=enhanced_main_menu_keyboard(config.show_wallet_on_start),
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in main menu callback: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_noop(self, callback: CallbackQuery) -> None:
        """Acknowledge no-op callbacks (e.g., pagination labels)."""
        try:
            await callback.answer()
        except Exception:
            pass

    async def cb_help_menu(self, callback: CallbackQuery) -> None:
        """Handle help menu callback"""
        try:
            help_text = f"""
❓ <b>Help & Support Center</b>

Welcome to the help section! Choose a topic below to get detailed information and guidance.

<b>Available Help Topics:</b>
• 🚀 Getting Started - New user guide
• 💼 Wallet Management - Funds and transactions
• 🔎 Browse & Search - Finding cards
• 🛒 Cart & Purchase - Buying process
• 📜 Transaction History - Tracking activity
• ⚙️ Settings - Account preferences
• 🤖 Bot Commands - Available commands
• ❓ FAQ - Common questions

{DEMO_WATERMARK}
"""

            await callback.message.edit_text(
                help_text, reply_markup=help_menu_keyboard()
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in help menu callback: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_help_getting_started(self, callback: CallbackQuery) -> None:
        """Handle getting started help"""
        try:
            from utils.keyboards import back_keyboard

            await callback.message.edit_text(
                HELP_GETTING_STARTED + DEMO_WATERMARK,
                reply_markup=back_keyboard("menu:help"),
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in getting started help: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_help_commands(self, callback: CallbackQuery) -> None:
        """Handle commands help"""
        try:
            from utils.keyboards import back_keyboard

            await callback.message.edit_text(
                HELP_COMMANDS + DEMO_WATERMARK,
                reply_markup=back_keyboard("menu:help"),
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in commands help: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_help_faq(self, callback: CallbackQuery) -> None:
        """Handle FAQ help"""
        try:
            from utils.keyboards import back_keyboard

            await callback.message.edit_text(
                HELP_FAQ + DEMO_WATERMARK,
                reply_markup=back_keyboard("menu:help"),
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in FAQ help: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_help_support(self, callback: CallbackQuery) -> None:
        """Handle support contact help"""
        try:
            support_text = f"""
📞 <b>Contact Support</b>

This is a demonstration bot with limited support options:

<b>For Technical Issues:</b>
• Check the FAQ section first
• Use /help command for basic guidance

<b>Data & Privacy:</b>
• Use /delete_me to remove your data
• All data is for demonstration only
• No real financial transactions occur

<b>Development Info:</b>
• This bot demonstrates wallet functionality
• Built with aiogram 3.x framework

{DEMO_WATERMARK}
"""
            from utils.keyboards import back_keyboard

            await callback.message.edit_text(
                support_text, reply_markup=back_keyboard("menu:help")
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error in support help: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cmd_delete_me(self, message: Message) -> None:
        """Handle /delete_me command"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ Unable to identify user")
                return

            # Create confirmation keyboard
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="✅ Yes, Delete",
                            callback_data=f"delete_confirm:{user.id}",
                        ),
                        InlineKeyboardButton(
                            text="❌ Cancel", callback_data="delete_cancel"
                        ),
                    ]
                ]
            )

            warning_text = (
                """
⚠️ <b>Data Deletion Warning</b>

This will permanently delete:
• Your user profile
• Wallet and transaction history  
• All purchase records
• Saved filters and preferences

<b>This action cannot be undone!</b>

Are you sure you want to continue?
            """
                + DEMO_WATERMARK
            )

            await message.answer(warning_text, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error in delete_me command: {e}")
            await message.answer(
                "❌ An error occurred. Please try again later." + DEMO_WATERMARK
            )

    async def cb_delete_confirm(self, callback: CallbackQuery) -> None:
        """Handle delete confirmation callback"""
        try:
            # Extract user_id from callback data
            user_id = int(callback.data.split(":")[1])

            # Verify the callback is from the same user
            if callback.from_user.id != user_id:
                await callback.answer("❌ Unauthorized", show_alert=True)
                return

            # Delete user data by telegram id
            removed = await self.user_service.delete_user_data(user_id)

            await callback.message.edit_text(
                "✅ <b>Data Deleted</b>\n\nYour demo data has been permanently deleted."
                + DEMO_WATERMARK
            )
            await callback.answer(f"Deleted {removed} records")

            logger.info(
                "User %s deleted their data", user_id, extra={"user_id": user_id}
            )

        except Exception as e:
            logger.error(f"Error in delete confirmation: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_delete_cancel(self, callback: CallbackQuery) -> None:
        """Handle delete cancellation callback"""
        try:
            await callback.message.edit_text(
                "ℹ️ <b>Deletion Cancelled</b>\n\nYour data is safe." + DEMO_WATERMARK,
                reply_markup=main_menu_keyboard(),
            )
            await callback.answer("Deletion cancelled")

        except Exception as e:
            logger.error(f"Error in delete cancellation: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_settings_menu(self, callback: CallbackQuery) -> None:
        """Handle settings menu"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user data
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Fetch user's wallet to determine currency (if any)
            wallet = await self.user_service.get_wallet_by_user_id(str(db_user.id))
            wallet_currency = wallet.currency if wallet else "USD"

            settings_text = f"""
⚙️ <b>Settings</b>

👤 <b>Profile Information:</b>
• User ID: <code>{db_user.telegram_id}</code>
• Username: {user.username or 'Not set'}
• Name: {user.first_name or 'Not set'} {user.last_name or ''}
• Language: {user.language_code or 'Not set'}
• Status: {'🟢 Active' if db_user.active else '🔴 Inactive'}

🔔 <b>Notifications:</b>
• Purchase confirmations: ✅ Enabled
• Balance alerts: ✅ Enabled
• Security alerts: ✅ Enabled

🌍 <b>Preferences:</b>
• Currency: {wallet_currency}
• Timezone: Auto-detect
• Language: {user.language_code or 'en'}

{DEMO_WATERMARK}
"""

            from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔔 Notifications",
                            callback_data="settings:notifications",
                        ),
                        InlineKeyboardButton(
                            text="🌍 Language", callback_data="settings:language"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="💱 Currency", callback_data="settings:currency"
                        ),
                        InlineKeyboardButton(
                            text="🔐 Privacy", callback_data="settings:privacy"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="📤 Export Data", callback_data="settings:export"
                        ),
                        InlineKeyboardButton(
                            text="🗑️ Delete Account", callback_data="settings:delete"
                        ),
                    ],
                    [InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main")],
                ]
            )

            await callback.message.edit_text(settings_text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in settings menu: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_settings_notifications(self, callback: CallbackQuery) -> None:
        """Handle notification settings"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            notifications_text = f"""
🔔 <b>Notification Settings</b>

📱 <b>Current Settings:</b>
• Purchase confirmations: ✅ Enabled
• Balance alerts: ✅ Enabled
• Security alerts: ✅ Enabled
• Promotional offers: ❌ Disabled

⚙️ <b>Notification Types:</b>
• 🛒 Purchase confirmations when you buy cards
• 💰 Balance alerts when funds are low
• 🔒 Security alerts for account changes
• 🎯 Promotional offers and discounts

{DEMO_WATERMARK}
"""

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🛒 Purchase Alerts",
                            callback_data="settings:notif:purchase",
                        ),
                        InlineKeyboardButton(
                            text="💰 Balance Alerts",
                            callback_data="settings:notif:balance",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔒 Security Alerts",
                            callback_data="settings:notif:security",
                        ),
                        InlineKeyboardButton(
                            text="🎯 Promotions", callback_data="settings:notif:promo"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="⬅️ Back", callback_data="menu:settings"
                        )
                    ],
                ]
            )

            await callback.message.edit_text(notifications_text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in notification settings: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_settings_export(self, callback: CallbackQuery) -> None:
        """Handle data export"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            export_text = f"""
📤 <b>Export Your Data</b>

📋 <b>Available Data:</b>
• User profile information
• Wallet transaction history
• Purchase records and receipts
• Settings and preferences

📄 <b>Export Formats:</b>
• JSON - Machine readable format
• CSV - Spreadsheet compatible
• PDF - Human readable report

🔒 <b>Privacy:</b>
Your data export will be generated securely and sent to you privately.

{DEMO_WATERMARK}
"""

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="📄 Export as JSON", callback_data="export:json"
                        ),
                        InlineKeyboardButton(
                            text="📊 Export as CSV", callback_data="export:csv"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="📋 Export as PDF", callback_data="export:pdf"
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="⬅️ Back", callback_data="menu:settings"
                        )
                    ],
                ]
            )

            await callback.message.edit_text(export_text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in data export: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)


def get_user_router() -> Router:
    """Create and return user router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = UserHandlers()

    # Command handlers
    router.message.register(handlers.cmd_start, Command("start"))
    router.message.register(handlers.cmd_help, Command("help"))
    router.message.register(handlers.cmd_version, Command("version"))
    router.message.register(handlers.cmd_health, Command("health"))
    router.message.register(handlers.cmd_delete_me, Command("delete_me"))

    # Callback handlers
    router.callback_query.register(handlers.cb_main_menu, F.data == "menu:main")
    router.callback_query.register(handlers.cb_noop, F.data == "noop")
    router.callback_query.register(handlers.cb_settings_menu, F.data == "menu:settings")
    router.callback_query.register(
        handlers.cb_settings_notifications, F.data == "settings:notifications"
    )
    router.callback_query.register(
        handlers.cb_settings_export, F.data == "settings:export"
    )
    router.callback_query.register(
        handlers.cb_delete_confirm, F.data.startswith("delete_confirm:")
    )
    router.callback_query.register(handlers.cb_delete_cancel, F.data == "delete_cancel")

    # Help system handlers
    router.callback_query.register(handlers.cb_help_menu, F.data == "menu:help")
    router.callback_query.register(
        handlers.cb_help_getting_started, F.data == "help:getting_started"
    )
    router.callback_query.register(handlers.cb_help_commands, F.data == "help:commands")
    router.callback_query.register(handlers.cb_help_faq, F.data == "help:faq")
    router.callback_query.register(handlers.cb_help_support, F.data == "help:support")

    logger.info("User handlers registered")
    return router
