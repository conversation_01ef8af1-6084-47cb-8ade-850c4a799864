"""
Orders-related Telegram handlers (view purchased card details)
"""

from __future__ import annotations

import logging
import asyncio
from datetime import datetime, timezone, timedelta

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

from middleware import attach_common_middlewares
from services.user_service import UserService
from services.card_service import CardService
from services.cart_service import CartService
from services.external_api_service import get_external_api_service
from config.settings import get_settings
from database.connection import get_collection
from utils.texts import DEMO_WATERMARK

logger = logging.getLogger(__name__)


class OrdersHandlers:
    def __init__(self):
        self.user_service = UserService()
        self.card_service = CardService()
        self.cart_service = CartService()
        self.external_api = get_external_api_service()
        self.purchases = get_collection("purchases")

    async def cb_view_purchased_card(self, callback: CallbackQuery) -> None:
        """Show details for a purchased card by ID"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            parts = (callback.data or "").split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = parts[2]

            # Fetch order info and show details (sanitized)
            order = await self._fetch_order_for_card(str(db_user.id), int(card_id))

            # Build details text
            if order:
                details_text = self._format_order_details(order)
            else:
                details_text = f"Could not load details for card #{card_id}."

            # 60s countdown expiry
            expiry = datetime.now(timezone.utc) + timedelta(seconds=60)
            expiry_ts = int(expiry.timestamp())

            header = f"🔎 <b>Card #{card_id} Details</b>\n"
            timer_line = self._format_timer(expiry_ts)
            body = details_text

            # Try to resolve external order id for check API
            order_id_for_check = None
            try:
                if isinstance(order, dict) and isinstance(order.get("_id"), int):
                    order_id_for_check = int(order.get("_id"))
                else:
                    order_id_for_check = await self._resolve_external_order_id(int(card_id))
            except Exception:
                order_id_for_check = None

            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="✅ Check Card",
                            callback_data=f"orders:check:{order_id_for_check or ''}:{card_id}:{expiry_ts}",
                        )
                    ],
                    [
                        InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                        InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                    ],
                ]
            )

            msg = await callback.message.edit_text(
                f"{header}{timer_line}\n\n{body}\n" + DEMO_WATERMARK,
                reply_markup=kb,
            )

            # Start countdown updater
            asyncio.create_task(
                self._run_countdown(
                    chat_id=msg.chat.id,
                    message_id=msg.message_id,
                    card_id=str(card_id),
                    expiry_ts=expiry_ts,
                    body_text=body,
                    order_id=str(order_id_for_check or ''),
                )
            )

            await callback.answer()
        except Exception as e:
            logger.error(f"Error showing purchased card: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_check_card(self, callback: CallbackQuery) -> None:
        """Handle Check Card button with expiry enforcement"""
        try:
            parts = (callback.data or "").split(":")
            # orders:check:<order_id_or_empty>:<card_id>:<expiry>
            if len(parts) not in (4, 5):
                await callback.answer("❌ Invalid request", show_alert=True)
                return
            if len(parts) == 4:
                order_id_str = ""
                card_id = parts[2]
                expiry_ts = int(parts[3])
            else:
                order_id_str = parts[2]
                card_id = parts[3]
                expiry_ts = int(parts[4])
            now_ts = int(datetime.now(timezone.utc).timestamp())
            if now_ts >= expiry_ts:
                # Disable button
                await self._disable_check_button(callback.message, card_id)
                await callback.answer("⛔ Time expired", show_alert=True)
                return

            # Resolve external order id
            order_id = int(order_id_str) if order_id_str.isdigit() else None
            if order_id is None:
                order_id = await self._resolve_external_order_id(int(card_id))

            if order_id is None:
                await callback.answer("❌ Could not resolve order id", show_alert=True)
                return

            # Call external check API and show status
            resp = await self.external_api.check_order(order_id)
            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                data = resp.data.get("data") or {}
                status = data.get("status") or resp.data.get("status") or "Unknown"
                await callback.answer(f"✅ Card status: {status}", show_alert=True)
            else:
                await callback.answer(
                    f"❌ Check failed: {getattr(resp, 'error', 'Unknown error')}",
                    show_alert=True,
                )
        except Exception as e:
            logger.error(f"Error checking card: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def _fetch_order_for_card(self, user_id: str | None, card_id: int) -> dict | None:
        """Fetch latest order entry for the specified product/card id.

        Priority: local DB purchases (non-sensitive) -> external orders API (sanitized).
        """
        try:
            # 1) Try local DB purchases
            if user_id:
                doc = await self.purchases.find_one(
                    {"user_id": user_id, "$or": [{"metadata.card_id": card_id}, {"sku": f"card_{card_id}"}]},
                    sort=[("created_at", -1)],
                )
                if doc:
                    meta = doc.get("metadata", {}) or {}
                    safe = {
                        "_id": str(doc.get("_id")),
                        "product_id": meta.get("card_id") or card_id,
                        "price": doc.get("price"),
                        "status": doc.get("status"),
                        "bank": (meta.get("card_data", {}) or {}).get("bank"),
                        "brand": (meta.get("card_data", {}) or {}).get("brand"),
                        "level": (meta.get("card_data", {}) or {}).get("level"),
                        "type": (meta.get("card_data", {}) or {}).get("type"),
                        "country": (meta.get("card_data", {}) or {}).get("country"),
                        "state": (meta.get("card_data", {}) or {}).get("state"),
                        "city": (meta.get("card_data", {}) or {}).get("city"),
                        "zip": (meta.get("card_data", {}) or {}).get("zip"),
                        "createdAt": doc.get("created_at"),
                    }
                    return safe

            # 2) Fallback to external API
            resp = await self.external_api.list_orders(page=1, limit=10)
            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                for od in (resp.data.get("data") or []):
                    pid = od.get("product_id") or od.get("card_id") or od.get("id")
                    if pid is not None and int(pid) == int(card_id):
                        # Sanitize sensitive fields before showing
                        return {
                            k: v
                            for k, v in od.items()
                            if k
                            not in {
                                "cc",
                                "cvv",
                                "ssn",
                                "dl",
                                "ua",
                                "dob",
                                "expmonth",
                                "expyear",
                                "email",
                                "phone",
                                "address",
                            }
                        }
            return None
        except Exception as e:
            logger.warning(f"Failed to fetch order for card {card_id}: {e}")
            return None

    async def _resolve_external_order_id(self, card_id: int) -> int | None:
        """Find the external order _id for a given product/card id from recent orders."""
        try:
            resp = await self.external_api.list_orders(page=1, limit=10)
            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                data = resp.data.get("data") or []
                for od in data:
                    pid = od.get("product_id") or od.get("card_id") or od.get("id")
                    if pid is not None and int(pid) == int(card_id):
                        oid = od.get("_id")
                        if isinstance(oid, int):
                            return oid
            return None
        except Exception:
            return None

    async def cb_orders_menu(self, callback: CallbackQuery) -> None:
        """Show user's recent orders (from local DB purchases)."""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            cursor = self.purchases.find({"user_id": str(db_user.id)}).sort("created_at", -1)
            docs = await cursor.limit(10).to_list(10)
            if not docs:
                text = "📦 <b>Your Orders</b>\n\nNo recent purchases found." + DEMO_WATERMARK
                kb = InlineKeyboardMarkup(
                    inline_keyboard=[[InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main")]]
                )
                await callback.message.edit_text(text, reply_markup=kb)
                await callback.answer()
                return

            lines = ["📦 <b>Your Orders</b>"]
            buttons_rows: list[list[InlineKeyboardButton]] = []

            for d in docs:
                meta = d.get("metadata", {}) or {}
                cd = meta.get("card_data", {}) or {}
                cid = meta.get("card_id")
                if not cid and isinstance(d.get("sku"), str) and d["sku"].startswith("card_"):
                    try:
                        cid = int(d["sku"].split("_", 1)[1])
                    except Exception:
                        cid = None
                price = float(d.get("price", 0.0))
                status = d.get("status", "")
                bank = cd.get("bank", "Unknown")
                brand = cd.get("brand", "")
                level = cd.get("level", "")
                created = d.get("created_at")
                lines.append(
                    f"• #{cid or '?'} — {bank} {brand} {level} ${price:.2f} [{status}]"
                )
                if cid:
                    buttons_rows.append(
                        [
                            InlineKeyboardButton(
                                text=f"🔎 View Card {str(cid)[:6]}…",
                                callback_data=f"orders:view_card:{cid}",
                            )
                        ]
                    )

            # Navigation buttons
            buttons_rows.append(
                [
                    InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                    InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main"),
                ]
            )

            await callback.message.edit_text(
                "\n".join(lines) + "\n" + DEMO_WATERMARK,
                reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons_rows),
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error showing orders: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    def _format_order_details(self, order: dict) -> str:
        try:
            lines = []
            keys = [
                "_id",
                "product_id",
                "status",
                "price",
                "bank",
                "brand",
                "level",
                "type",
                "country",
                "state",
                "city",
                "zip",
                "createdAt",
                "start_Date",
                "refundAt",
            ]
            for k in keys:
                if k in order and order[k] is not None:
                    lines.append(f"• <b>{k}</b>: {order[k]}")
            # Include any other remaining fields
            for k, v in order.items():
                if k not in keys and v is not None:
                    lines.append(f"• <b>{k}</b>: {v}")
            return "\n".join(lines)
        except Exception as e:
            logger.error(f"Error formatting order details: {e}")
            return "(error showing details)"

    def _format_timer(self, expiry_ts: int) -> str:
        remaining = max(0, expiry_ts - int(datetime.now(timezone.utc).timestamp()))
        return f"⏳ <b>Time left:</b> {remaining}s"

    async def _run_countdown(
        self, chat_id: int, message_id: int, card_id: str, expiry_ts: int, body_text: str, order_id: str
    ) -> None:
        """Edit the message to update the countdown and disable the button at 0."""
        try:
            settings = get_settings()
            from aiogram import Bot

            bot = Bot(token=settings.BOT_TOKEN)
            # Try to update every second
            last_remaining = None
            while True:
                now_ts = int(datetime.now(timezone.utc).timestamp())
                remaining = max(0, expiry_ts - now_ts)
                if remaining == 0:
                    # Disable the button and update timer to 0
                    try:
                        await self._disable_check_button_by_ids(bot, chat_id, message_id, card_id)
                    finally:
                        break
                # Only edit when value changes to reduce calls
                if remaining != last_remaining:
                    try:
                        await bot.edit_message_text(
                            chat_id=chat_id,
                            message_id=message_id,
                            text=(
                                f"🔎 <b>Card #{card_id} Details</b>\n{self._format_timer(expiry_ts)}\n\n"
                                f"{body_text}\n"
                                + DEMO_WATERMARK
                            ),
                            reply_markup=InlineKeyboardMarkup(
                                inline_keyboard=[
                                    [
                                        InlineKeyboardButton(
                                            text=f"✅ Check Card ({remaining}s)",
                                            callback_data=f"orders:check:{order_id}:{card_id}:{expiry_ts}",
                                        )
                                    ],
                                    [
                                        InlineKeyboardButton(
                                            text="🛒 View Cart", callback_data="local:cart:view"
                                        ),
                                        InlineKeyboardButton(
                                            text="⬅️ Back", callback_data="menu:history"
                                        ),
                                    ],
                                ]
                            ),
                            parse_mode="HTML",
                        )
                    except Exception:
                        # Ignore edit errors (message may have changed)
                        pass
                    last_remaining = remaining
                await asyncio.sleep(1)
            await bot.session.close()
        except Exception as e:
            logger.error(f"Countdown error: {e}")

    async def _disable_check_button(self, message, card_id: str) -> None:
        try:
            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="⛔ Check Disabled", callback_data="noop")],
                    [
                        InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                        InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                    ],
                ]
            )
            await message.edit_reply_markup(reply_markup=kb)
        except Exception:
            pass

    async def _disable_check_button_by_ids(
        self, bot, chat_id: int, message_id: int, card_id: str
    ) -> None:
        try:
            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="⛔ Check Disabled", callback_data="noop")],
                    [
                        InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                        InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                    ],
                ]
            )
            await bot.edit_message_reply_markup(
                chat_id=chat_id, message_id=message_id, reply_markup=kb
            )
        except Exception:
            pass


def get_orders_router() -> Router:
    router = Router()
    attach_common_middlewares(router)
    handlers = OrdersHandlers()

    router.callback_query.register(
        handlers.cb_view_purchased_card, F.data.startswith("orders:view_card:")
    )
    router.callback_query.register(
        handlers.cb_check_card, F.data.startswith("orders:check:")
    )
    router.callback_query.register(
        handlers.cb_orders_menu, F.data == "menu:orders"
    )

    logger.info("Orders handlers registered")
    return router
