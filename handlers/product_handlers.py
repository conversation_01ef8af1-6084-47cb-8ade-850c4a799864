"""
Product selection handlers for multi-product bot architecture
"""

from __future__ import annotations

import logging
from typing import Optional, Dict, Any

from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.fsm.context import FSMContext

from services.product_service import ProductService
from services.user_service import UserService
from models.product import ProductType, APIStatus
from utils.keyboards import (
    product_selection_keyboard,
    api_selection_keyboard,
    product_breadcrumb_keyboard,
    quick_access_keyboard,
    enhanced_main_menu_keyboard,
    back_keyboard
)
from utils.texts import DEMO_WATERMARK
from middleware import attach_common_middlewares

logger = logging.getLogger(__name__)


class ProductHandlers:
    """Product selection and API management handlers"""
    
    def __init__(self):
        self.product_service = ProductService()
        self.user_service = UserService()
    
    async def cb_product_menu(self, callback: CallbackQuery) -> None:
        """Handle product selection menu"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            
            # Get available products and user preferences
            products = await self.product_service.get_available_products()
            current_product, current_api = await self.product_service.get_user_current_selection(user.id)
            
            if not products:
                await callback.message.edit_text(
                    "⚠️ <b>No Products Available</b>\n\n"
                    "No products are currently available. Please try again later."
                    + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:main")
                )
                await callback.answer()
                return
            
            # Format products for keyboard
            product_data = []
            for product in products:
                active_apis = product.get_active_apis()
                product_data.append({
                    "type": product.type.value,
                    "name": product.name,
                    "icon": product.icon,
                    "active_apis": len(active_apis),
                    "total_apis": len(product.apis)
                })
            
            # Create menu text
            menu_text = "🛍️ <b>Select Product Type</b>\n\n"
            
            for product in products:
                active_apis = product.get_active_apis()
                status_text = f"({len(active_apis)}/{len(product.apis)} APIs active)"
                
                menu_text += f"{product.icon} <b>{product.name}</b> {status_text}\n"
                menu_text += f"   {product.description}\n\n"
            
            if current_product:
                menu_text += f"📍 <i>Currently using: {current_product.value.upper()}</i>\n\n"
            
            menu_text += DEMO_WATERMARK
            
            await callback.message.edit_text(
                menu_text,
                reply_markup=product_selection_keyboard(
                    product_data, 
                    current_product.value if current_product else None
                )
            )
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error in product menu: {e}")
            await callback.answer("❌ Error loading products", show_alert=True)
    
    async def cb_product_select(self, callback: CallbackQuery) -> None:
        """Handle product type selection"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            
            # Extract product type from callback data
            product_type_str = callback.data.split(":")[-1]
            try:
                product_type = ProductType(product_type_str)
            except ValueError:
                await callback.answer("❌ Invalid product type", show_alert=True)
                return
            
            # Get APIs for this product
            apis = await self.product_service.get_product_apis(product_type)
            if not apis:
                await callback.answer("❌ No APIs available for this product", show_alert=True)
                return
            
            # Get current selection
            current_product, current_api = await self.product_service.get_user_current_selection(user.id)
            
            # Format APIs for keyboard
            api_data = []
            for api in apis:
                api_data.append({
                    "id": api.id,
                    "name": api.name,
                    "status": api.status.value,
                    "is_default": api.is_default,
                    "description": api.description
                })
            
            # Create API selection text
            config = await self.product_service.get_product_config()
            product_info = config.get_product_by_type(product_type)
            
            menu_text = f"{product_info.icon} <b>{product_info.name}</b>\n\n"
            menu_text += f"{product_info.description}\n\n"
            menu_text += "<b>Available APIs:</b>\n\n"
            
            for api in apis:
                status_emoji = {
                    "active": "🟢",
                    "inactive": "🔴",
                    "maintenance": "🟡",
                    "error": "❌"
                }.get(api.status.value, "❓")
                
                menu_text += f"{status_emoji} <b>{api.name}</b>"
                if api.is_default:
                    menu_text += " (Default)"
                menu_text += f"\n   {api.description}\n\n"
            
            menu_text += DEMO_WATERMARK
            
            await callback.message.edit_text(
                menu_text,
                reply_markup=api_selection_keyboard(
                    product_type.value,
                    api_data,
                    current_api,
                    show_status=True
                )
            )
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error in product selection: {e}")
            await callback.answer("❌ Error selecting product", show_alert=True)
    
    async def cb_api_select(self, callback: CallbackQuery) -> None:
        """Handle API selection within a product"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            
            # Parse callback data: api:select:product_type:api_id
            parts = callback.data.split(":")
            if len(parts) != 4:
                await callback.answer("❌ Invalid selection", show_alert=True)
                return
            
            product_type_str = parts[2]
            api_id = parts[3]
            
            try:
                product_type = ProductType(product_type_str)
            except ValueError:
                await callback.answer("❌ Invalid product type", show_alert=True)
                return
            
            # Verify API is available
            if not await self.product_service.is_api_available(api_id):
                await callback.answer("❌ Selected API is not available", show_alert=True)
                return
            
            # Update user selection
            success = await self.product_service.update_user_selection(user.id, product_type, api_id)
            if not success:
                await callback.answer("❌ Failed to update selection", show_alert=True)
                return
            
            # Get API info for confirmation
            api_info = await self.product_service.get_api_info(api_id)
            if not api_info:
                await callback.answer("❌ API information not found", show_alert=True)
                return
            
            # Show confirmation and redirect to browse
            confirmation_text = f"""
✅ <b>Selection Updated</b>

🛍️ <b>Product:</b> {product_type.value.upper()}
🔗 <b>API:</b> {api_info.name}
📝 <b>Description:</b> {api_info.description}

You can now browse the catalog using this API configuration.

{DEMO_WATERMARK}
"""
            
            from utils.keyboards import browse_menu_keyboard
            
            await callback.message.edit_text(
                confirmation_text,
                reply_markup=browse_menu_keyboard()
            )
            await callback.answer(f"✅ Switched to {api_info.name}")
            
            logger.info(f"User {user.id} selected {product_type.value} -> {api_id}")
            
        except Exception as e:
            logger.error(f"Error in API selection: {e}")
            await callback.answer("❌ Error selecting API", show_alert=True)
    
    async def cb_api_refresh(self, callback: CallbackQuery) -> None:
        """Handle API status refresh"""
        try:
            # Extract product type from callback data
            product_type_str = callback.data.split(":")[-1]
            try:
                product_type = ProductType(product_type_str)
            except ValueError:
                await callback.answer("❌ Invalid product type", show_alert=True)
                return
            
            # Clear cache and reload
            await self.product_service.clear_cache()
            
            # Redirect back to API selection
            callback.data = f"product:select:{product_type_str}"
            await self.cb_product_select(callback)
            await callback.answer("🔄 Status refreshed")
            
        except Exception as e:
            logger.error(f"Error refreshing API status: {e}")
            await callback.answer("❌ Error refreshing status", show_alert=True)
    
    async def cb_quick_select(self, callback: CallbackQuery) -> None:
        """Handle quick API selection"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            
            # Parse callback data: quick:select:product_type:api_id
            parts = callback.data.split(":")
            if len(parts) != 4:
                await callback.answer("❌ Invalid selection", show_alert=True)
                return
            
            product_type_str = parts[2]
            api_id = parts[3]
            
            try:
                product_type = ProductType(product_type_str)
            except ValueError:
                await callback.answer("❌ Invalid product type", show_alert=True)
                return
            
            # Update selection and redirect to browse
            success = await self.product_service.update_user_selection(user.id, product_type, api_id)
            if success:
                callback.data = "menu:browse"
                from handlers.catalog_handlers import CatalogHandlers
                catalog_handlers = CatalogHandlers()
                await catalog_handlers.cb_browse_menu(callback)
                await callback.answer(f"⚡ Quick switched to {api_id}")
            else:
                await callback.answer("❌ Failed to switch", show_alert=True)
            
        except Exception as e:
            logger.error(f"Error in quick select: {e}")
            await callback.answer("❌ Error in quick selection", show_alert=True)


def get_product_router() -> Router:
    """Create and return product router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = ProductHandlers()
    
    # Product menu handlers
    router.callback_query.register(handlers.cb_product_menu, F.data == "menu:products")
    router.callback_query.register(
        handlers.cb_product_select, F.data.startswith("product:select:")
    )
    router.callback_query.register(
        handlers.cb_api_select, F.data.startswith("api:select:")
    )
    router.callback_query.register(
        handlers.cb_api_refresh, F.data.startswith("api:refresh:")
    )
    router.callback_query.register(
        handlers.cb_quick_select, F.data.startswith("quick:select:")
    )
    
    logger.info("Product handlers registered")
    return router
