"""
Cart management handlers for Telegram bot
"""

from __future__ import annotations

import logging

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from services.cart_service import CartService
from services.user_service import UserService
from utils.texts import DEMO_WATERMARK
from middleware import attach_common_middlewares

logger = logging.getLogger(__name__)


class CartStates(StatesGroup):
    """FSM states for cart operations"""

    waiting_quantity = State()


class LocalCartHandlers:
    """Local cart management handlers - handles internal cart operations"""

    def __init__(self):
        self.cart_service = CartService()
        self.user_service = UserService()

    async def local_cart_view_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart view callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            await callback.answer("🛒 Loading cart...")

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.message.edit_text(
                    "❌ <b>User not found</b>\n\n"
                    "Please start the bot with /start first." + DEMO_WATERMARK,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back", callback_data="menu:main"
                                )
                            ]
                        ]
                    ),
                )
                return

            # Get cart contents
            cart_contents = await self.cart_service.get_cart_contents(str(user_doc.id))

            if cart_contents.get("error"):
                await callback.message.edit_text(
                    f"❌ <b>Error loading cart</b>\n\n"
                    f"Error: {cart_contents['error']}" + DEMO_WATERMARK,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back", callback_data="menu:main"
                                )
                            ]
                        ]
                    ),
                )
                return

            # Format cart for display
            cart_text = self.cart_service.format_cart_for_display(cart_contents)

            # Create keyboard based on cart state
            keyboard_buttons = []

            if not cart_contents.get("is_empty", True):
                # Cart has items
                keyboard_buttons.extend(
                    [
                        [
                            InlineKeyboardButton(
                                text="💳 Checkout", callback_data="local:cart:checkout"
                            ),
                            InlineKeyboardButton(
                                text="🗑️ Clear Cart", callback_data="local:cart:clear"
                            ),
                        ],
                        [
                            InlineKeyboardButton(
                                text="✏️ Edit Items", callback_data="local:cart:edit"
                            ),
                            InlineKeyboardButton(
                                text="🔄 Refresh", callback_data="local:cart:view"
                            ),
                        ],
                    ]
                )

            # Always show browse and back buttons
            keyboard_buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text="🛒 Browse Catalog", callback_data="menu:browse"
                        )
                    ],
                    [InlineKeyboardButton(text="🔙 Back", callback_data="menu:main")],
                ]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            await callback.message.edit_text(
                cart_text
                + "\n\n<i>This is demo cart functionality</i>"
                + DEMO_WATERMARK,
                reply_markup=keyboard,
            )

        except Exception as e:
            logger.error(f"Error in view cart: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def local_cart_clear_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart clear callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Clear cart
            success, message = await self.cart_service.clear_cart(str(user_doc.id))

            if success:
                await callback.answer(f"✅ {message}")
                # Refresh cart view
                await self.local_cart_view_handler(callback)
            else:
                await callback.answer(f"❌ {message}", show_alert=True)

        except Exception as e:
            logger.error(f"Error clearing cart: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def local_cart_checkout_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart checkout callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            await callback.answer("⏳ Queuing checkout...")

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.message.edit_text(
                    "❌ <b>User not found</b>\n\n"
                    "Please start the bot with /start first." + DEMO_WATERMARK,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back", callback_data="menu:main"
                                )
                            ]
                        ]
                    ),
                )
                return

            user_id = str(user_doc.id)
            telegram_user_id = user.id

            # Get cart contents for validation
            cart_contents = await self.cart_service.get_cart_contents(user_id)

            if cart_contents.get("is_empty", True):
                await callback.message.edit_text(
                    "🛒 <b>Cart Empty</b>\n\nYour cart is empty. Add some items first!"
                    + DEMO_WATERMARK,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔎 Browse Cards", callback_data="menu:browse"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Main Menu", callback_data="menu:main"
                                )
                            ],
                        ]
                    ),
                )
                return

            # Queue checkout for processing
            success, message, job_id = await self.cart_service.queue_checkout(
                user_id, telegram_user_id
            )

            if success:
                # Show queue confirmation message
                queue_text = f"""
⏳ <b>Order Queued for Processing</b>

{message}

🔔 <b>What happens next:</b>
• Your order is now in the processing queue
• You'll receive notifications about progress
• Payment will be processed when your turn comes
• You can cancel the order while it's queued

{DEMO_WATERMARK}
"""

                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="❌ Cancel Order",
                                callback_data=f"purchase:cancel:{job_id}",
                            ),
                            InlineKeyboardButton(
                                text="📊 Queue Status",
                                callback_data=f"purchase:status:{job_id}",
                            ),
                        ],
                        [
                            InlineKeyboardButton(
                                text="📜 Order History", callback_data="menu:history"
                            ),
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔙 Main Menu", callback_data="menu:main"
                            )
                        ],
                    ]
                )

                await callback.message.edit_text(queue_text, reply_markup=keyboard)

            else:
                # Show error message
                error_text = f"""
❌ <b>Cannot Queue Order</b>

{message}

Please check your wallet balance and cart contents.

{DEMO_WATERMARK}
"""

                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="💰 View Wallet", callback_data="menu:wallet"
                            ),
                            InlineKeyboardButton(
                                text="🛒 View Cart", callback_data="local:cart:view"
                            ),
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔙 Back", callback_data="menu:main"
                            )
                        ],
                    ]
                )

                await callback.message.edit_text(error_text, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error during checkout: {e}")
            await callback.answer("❌ Checkout failed", show_alert=True)

    async def local_cart_edit_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart edit callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get cart contents
            cart_contents = await self.cart_service.get_cart_contents(str(user_doc.id))

            if cart_contents.get("is_empty", True):
                await callback.message.edit_text(
                    "🛒 <b>Cart is Empty</b>\n\n"
                    "Add items to your cart first!" + DEMO_WATERMARK,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🛒 Browse Catalog",
                                    callback_data="menu:browse",
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back", callback_data="local:cart:view"
                                )
                            ],
                        ]
                    ),
                )
                return

            # Show cart items with edit options
            items = cart_contents.get("items", [])
            edit_text = "✏️ <b>Edit Cart Items</b>\n\nSelect an item to modify:\n\n"

            keyboard_buttons = []
            for i, item in enumerate(items[:10], 1):  # Limit to 10 items for UI
                card_data = item.card_data
                bank = card_data.get("bank", "Unknown")[:20]
                card_id = card_data.get("_id")

                edit_text += f"{i}. {bank}... (#{card_id}) - Qty: {item.quantity}\n"

                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text=f"✏️ Edit #{card_id}",
                            callback_data=f"local:cart:edit_item:{card_id}",
                        )
                    ]
                )

            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Cart", callback_data="local:cart:view"
                    )
                ]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            await callback.message.edit_text(
                edit_text + "\n<i>This is demo cart functionality</i>" + DEMO_WATERMARK,
                reply_markup=keyboard,
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in edit cart: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def local_cart_edit_item_handler(self, callback: CallbackQuery) -> None:
        """Handle edit specific local cart item callback"""
        try:
            # Extract card ID from callback data (local:cart:edit_item:card_id)
            parts = callback.data.split(":")
            if len(parts) < 4:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = int(parts[3])

            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get cart item details
            cart_item_doc = await self.cart_service.cart_items_collection.find_one(
                {"user_id": str(user_doc.id), "card_id": card_id}
            )

            if not cart_item_doc:
                await callback.answer("❌ Item not found in cart", show_alert=True)
                return

            from models import CartItem

            cart_item = CartItem.from_mongo(cart_item_doc)
            card_data = cart_item.card_data

            # Show item edit options
            edit_text = (
                f"✏️ <b>Edit Cart Item</b>\n\n"
                f"💳 <b>Card #{card_data.get('_id')}</b>\n"
                f"🏦 <b>Bank:</b> {card_data.get('bank', 'Unknown')}\n"
                f"🔢 <b>BIN:</b> {card_data.get('bin', 'N/A')}\n"
                f"💰 <b>Price:</b> ${cart_item.price_at_add}\n"
                f"📦 <b>Current Quantity:</b> {cart_item.quantity}\n\n"
                "What would you like to do?"
            )

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="➕ Add 1",
                            callback_data=f"local:cart:qty_change:{card_id}:+1",
                        ),
                        InlineKeyboardButton(
                            text="➖ Remove 1",
                            callback_data=f"local:cart:qty_change:{card_id}:-1",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="🗑️ Remove Item",
                            callback_data=f"local:cart:remove_item:{card_id}",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="local:cart:edit"
                        )
                    ],
                ]
            )

            await callback.message.edit_text(
                edit_text
                + "\n\n<i>This is demo cart functionality</i>"
                + DEMO_WATERMARK,
                reply_markup=keyboard,
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in edit cart item: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def local_cart_quantity_change_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart quantity change callback"""
        try:
            # Parse callback data: local:cart:qty_change:card_id:change
            parts = callback.data.split(":")
            if len(parts) < 5:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = int(parts[3])
            change = int(parts[4])

            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get current quantity
            cart_item_doc = await self.cart_service.cart_items_collection.find_one(
                {"user_id": str(user_doc.id), "card_id": card_id}
            )

            if not cart_item_doc:
                await callback.answer("❌ Item not found", show_alert=True)
                return

            current_qty = cart_item_doc["quantity"]
            new_qty = max(0, current_qty + change)

            # Update quantity
            success, message = await self.cart_service.update_cart_item_quantity(
                str(user_doc.id), card_id, new_qty
            )

            if success:
                await callback.answer(f"✅ {message}")
                # Refresh the edit item view
                callback.data = f"local:cart:edit_item:{card_id}"
                await self.local_cart_edit_item_handler(callback)
            else:
                await callback.answer(f"❌ {message}", show_alert=True)

        except Exception as e:
            logger.error(f"Error in quantity change: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def local_cart_remove_item_handler(self, callback: CallbackQuery) -> None:
        """Handle remove local cart item callback"""
        try:
            # Extract card ID from callback data (local:cart:remove_item:card_id)
            parts = callback.data.split(":")
            if len(parts) < 4:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = int(parts[3])

            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Remove item
            success, message = await self.cart_service.remove_from_cart(
                str(user_doc.id), card_id
            )

            if success:
                await callback.answer(f"✅ {message}")
                # Go back to cart view
                callback.data = "local:cart:view"
                await self.local_cart_view_handler(callback)
            else:
                await callback.answer(f"❌ {message}", show_alert=True)

        except Exception as e:
            logger.error(f"Error removing cart item: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)


def get_cart_router() -> Router:
    """Create and return local cart router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = LocalCartHandlers()

    # Local cart callback handlers
    router.callback_query.register(
        handlers.local_cart_view_handler, F.data == "local:cart:view"
    )
    router.callback_query.register(
        handlers.local_cart_clear_handler, F.data == "local:cart:clear"
    )
    router.callback_query.register(
        handlers.local_cart_checkout_handler, F.data == "local:cart:checkout"
    )
    router.callback_query.register(
        handlers.local_cart_edit_handler, F.data == "local:cart:edit"
    )
    router.callback_query.register(
        handlers.local_cart_edit_item_handler,
        F.data.startswith("local:cart:edit_item:"),
    )
    router.callback_query.register(
        handlers.local_cart_quantity_change_handler,
        F.data.startswith("local:cart:qty_change:"),
    )
    router.callback_query.register(
        handlers.local_cart_remove_item_handler,
        F.data.startswith("local:cart:remove_item:"),
    )

    logger.info("Local cart handlers registered")
    return router
