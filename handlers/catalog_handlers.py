"""Catalog and browsing handlers."""

from __future__ import annotations

import logging
import re
from typing import Any, Dict, Iterable, Optional

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import StateFilter

from services.card_service import CardService
from services.cart_service import CartService
from services.user_service import UserService
from services.product_service import ProductService
from utils.keyboards import filter_menu_keyboard, back_keyboard, product_breadcrumb_keyboard
from utils.texts import DEMO_WATERMARK, INFO_NO_RESULTS
from utils.validation import ValidationError, sanitize_text_input
from middleware import attach_common_middlewares

logger = logging.getLogger(__name__)


class CatalogHandlers:
    """Catalog and browsing handlers"""

    CATEGORY_INFO: Dict[str, Dict[str, Any]] = {
        "location": {
            "label": "📍 Location",
            "description": "Filter by geography or require ZIP verification.",
            "keys": ["country", "state", "city", "zip", "zipCheck"],
        },
        "card": {
            "label": "💳 Card Details",
            "description": "Narrow results by brand, type, level, bank, BIN, or base.",
            "keys": ["brand", "type", "level", "bank", "bin", "base"],
        },
        "pricing": {
            "label": "💲 Pricing",
            "description": "Control minimum/maximum pricing.",
            "keys": ["priceFrom", "priceTo"],
        },
        "contact": {
            "label": "🧾 Contact Data",
            "description": "Require address, phone, or email data.",
            "keys": ["address", "phone", "email"],
        },
        "identity": {
            "label": "🛡 Identity Data",
            "description": "Require sensitive identity-related data points.",
            "keys": ["dob", "ssn", "mmn", "ip", "dl", "ua"],
        },
        "extras": {
            "label": "✨ Extras",
            "description": "Special options like no CVV, refunds, or discounts.",
            "keys": ["withoutcvv", "refundable", "expirethismonth", "discount"],
        },
    }

    FILTER_LABELS: Dict[str, str] = {
        "bank": "Bank",
        "bin": "BIN",
        "country": "Country",
        "state": "State",
        "city": "City",
        "brand": "Brand",
        "type": "Card Type",
        "level": "Level",
        "zip": "ZIP",
        "price": "Price Range",
        "priceFrom": "Price From",
        "priceTo": "Price To",
        "base": "Base",
        "zipCheck": "ZIP Check",
        "address": "Address",
        "phone": "Phone",
        "email": "Email",
        "withoutcvv": "Without CVV",
        "refundable": "Refundable",
        "expirethismonth": "Expire This Month",
        "dob": "DOB",
        "ssn": "SSN",
        "mmn": "MMN",
        "ip": "IP",
        "dl": "Driving Licence",
        "ua": "User Agent",
        "discount": "Discount",
    }

    BOOLEAN_FILTER_KEYS = {
        "zipCheck",
        "address",
        "phone",
        "email",
        "withoutcvv",
        "refundable",
        "expirethismonth",
        "dob",
        "ssn",
        "mmn",
        "ip",
        "dl",
        "ua",
        "discount",
    }

    DYNAMIC_FILTER_KEYS = {
        "country",
        "state",
        "city",
        "zip",
        "brand",
        "type",
        "level",
        "bank",
    }
    DYNAMIC_FILTER_RESET_MAP = {
        "country": {"country", "state", "city", "zip", "bank"},
        "state": {"state", "city", "zip", "bank"},
        "city": {"city", "zip", "bank"},
        "zip": {"zip", "bank"},
        "brand": {"brand"},
        "type": {"type"},
        "level": {"level"},
        "bank": {"bank"},
    }
    FILTER_OPTION_LIMIT = 20
    FILTER_OPTION_ROW_WIDTH = 2

    PRESET_COUNTRIES = ("US", "CA", "GB", "AU", "DE", "FR")
    PRESET_BRANDS = ("VISA", "MASTERCARD", "AMEX", "DISCOVER")
    PRESET_TYPES = ("CREDIT", "DEBIT", "PREPAID")
    PRESET_BANKS = (
        "CHASE",
        "CITI",
        "BANK OF AMERICA",
        "WELLS FARGO",
        "AMERICAN EXPRESS",
    )
    PRESET_BINS = ("452083", "601101", "411111", "438857", "520082", "548042")
    PRICE_PRESETS = (
        ("$0 - $2", 0, 2),
        ("$2 - $3", 2, 3),
        ("$3 - $5", 3, 5),
        ("$5 - $10", 5, 10),
    )

    FILTER_KEY_TO_CATEGORY: Dict[str, str] = {
        key: category
        for category, meta in CATEGORY_INFO.items()
        for key in meta["keys"]
    }
    FILTER_KEY_TO_CATEGORY.update({"price": "pricing"})

    MANUAL_INPUT_CONFIG: Dict[str, Dict[str, str]] = {
        "state": {
            "title": "Set State",
            "prompt": "Send the state or region to match (e.g., <code>CA</code>).",
            "mode": "text",
        },
        "city": {
            "title": "Set City",
            "prompt": "Send the city to match (e.g., <code>Los Angeles</code>).",
            "mode": "text",
        },
        "zip": {
            "title": "Set ZIP",
            "prompt": "Send the ZIP or postal code (3–10 digits).",
            "mode": "zip",
        },
        "bank": {
            "title": "Custom Bank",
            "prompt": "Send the bank name to match (e.g., <code>Barclays</code>).",
            "mode": "text",
        },
        "base": {
            "title": "Set Base",
            "prompt": "Send the base or source identifier (e.g., <code>Premium</code>).",
            "mode": "text",
        },
        "bin": {
            "title": "Enter BIN",
            "prompt": "Send 4–8 digits for the BIN (e.g., <code>452083</code>).",
            "mode": "bin",
        },
        "priceFrom": {
            "title": "Minimum Price",
            "prompt": "Send the minimum price (e.g., <code>5</code>).",
            "mode": "price",
        },
        "priceTo": {
            "title": "Maximum Price",
            "prompt": "Send the maximum price (e.g., <code>25</code>).",
            "mode": "price",
        },
    }

    MAX_PRICE = 10000.0
    MAX_PRICE_SPREAD = 5000.0

    def __init__(self):
        # Lazy init to avoid DB access on import
        self.user_card_services: dict[int, tuple[Optional[str], CardService]] = {}
        self.cart_service = None
        # Lazy to avoid DB access during simple handler init
        self.user_service = None
        self.product_service = ProductService()
        # In-memory filter state per user
        self.user_filters: dict[int, dict] = {}
        self.user_applied_filters: dict[int, dict] = {}
        # Track dynamic option tokens for compact callback data
        self.user_option_tokens: dict[int, dict[str, Dict[str, Any]]] = {}

        # Cache for current page cards to avoid re-fetching when adding to cart
        self.user_current_cards: dict[int, list[dict]] = {}

    async def _get_card_service(self, user_id: int) -> CardService:
        """Get card service configured for user's current product selection"""
        current_product, current_api = await self.product_service.get_user_current_selection(user_id)

        cached = self.user_card_services.get(user_id)
        if cached and cached[0] == current_api:
            return cached[1]

        external_api_service = None
        if current_api:
            external_api_service = await self.product_service.get_external_api_service_for_user(user_id)

        card_service = CardService(external_api_service=external_api_service)
        self.user_card_services[user_id] = (current_api, card_service)
        return card_service

    # FSM state for manual filter input
    class CatalogFilterStates(StatesGroup):
        WAITING_VALUE = State()

    def _get_user_id(self, callback: CallbackQuery) -> int:
        return callback.from_user.id if callback.from_user else 0

    def _get_temp_filters(self, user_id: int) -> dict:
        return self.user_filters.setdefault(user_id, {})

    def _get_applied_filters(self, user_id: int) -> dict:
        return self.user_applied_filters.get(user_id, {})

    @staticmethod
    def _chunk_list(items: list[Any], chunk_size: int) -> Iterable[list[Any]]:
        for idx in range(0, len(items), chunk_size):
            yield items[idx : idx + chunk_size]

    @staticmethod
    def _normalize_option_label(label: str) -> str:
        text = (label or "").strip()
        if not text:
            return "—"
        return text if len(text) <= 32 else text[:29] + "…"

    @staticmethod
    def _sort_dynamic_options(options: list[dict]) -> list[dict]:
        def cleaned_text(text: str) -> str:
            stripped = text.strip()
            stripped = re.sub(r"\s*\(\d+\)\s*$", "", stripped)
            stripped = re.sub(r"^[^A-Za-z0-9]+", "", stripped)
            return stripped

        def sort_key(option: dict) -> tuple[int, str, str]:
            label = str(option.get("label") or "")
            visible = cleaned_text(label)
            if not visible:
                visible = cleaned_text(str(option.get("value") or ""))
            return (0 if visible else 1, visible.casefold(), label.casefold())

        return sorted(options, key=sort_key)

    def _build_dynamic_option_rows(
        self,
        filter_key: str,
        options: list[dict],
        user_id: Optional[int],
        page: int,
    ) -> list[list[InlineKeyboardButton]]:
        rows: list[list[InlineKeyboardButton]] = []
        seen: set[str] = set()

        cleaned: list[tuple[str, str]] = []
        for option in options:
            value_raw = option.get("value")
            value = "" if value_raw is None else str(value_raw)
            if value and value in seen:
                continue
            seen.add(value)
            label = option.get("label") or value
            cleaned.append((self._normalize_option_label(label), value))

        tokenized = self._tokenize_dynamic_options(
            filter_key, cleaned, user_id, page
        )

        for chunk in self._chunk_list(tokenized, self.FILTER_OPTION_ROW_WIDTH):
            row: list[InlineKeyboardButton] = []
            for label, token in chunk:
                row.append(
                    InlineKeyboardButton(
                        text=label,
                        callback_data=f"filter:set:{filter_key}:{token}",
                    )
                )
            rows.append(row)

        return rows

    def _tokenize_dynamic_options(
        self,
        filter_key: str,
        options: list[tuple[str, str]],
        user_id: Optional[int],
        page: int,
    ) -> list[tuple[str, str]]:
        actual_user_id = user_id or 0
        user_tokens = self.user_option_tokens.setdefault(actual_user_id, {})
        token_map: dict[str, str] = {}
        user_tokens[filter_key] = {
            "page": page,
            "tokens": token_map,
        }

        tokenized: list[tuple[str, str]] = []
        for idx, (label, value) in enumerate(options):
            token = f"opt{page}_{idx}"
            token_map[token] = value
            tokenized.append((label, token))

        return tokenized

    def _resolve_dynamic_option_value(
        self, user_id: Optional[int], filter_key: str, token: str
    ) -> Optional[str]:
        actual_user_id = user_id or 0
        user_tokens = self.user_option_tokens.get(actual_user_id)
        if not user_tokens:
            return None
        filter_entry = user_tokens.get(filter_key)
        if not filter_entry:
            return None
        token_map = filter_entry.get("tokens", {})
        return token_map.get(token)

    def _filters_summary_text(self, filters: dict) -> str:
        """Generate a human-readable summary of active filters."""

        if not filters:
            return "No active filters"

        parts: list[str] = []

        price_from = self._parse_price_value(filters.get("priceFrom"))
        price_to = self._parse_price_value(filters.get("priceTo"))

        if price_from is not None and price_to is not None:
            parts.append(f"• Price Range: ${price_from:.2f} – ${price_to:.2f}")
        elif price_from is not None:
            parts.append(f"• Price From: ${price_from:.2f}")
        elif price_to is not None:
            parts.append(f"• Price To: ${price_to:.2f}")

        handled_keys = {"priceFrom", "priceTo"}

        for category in self.CATEGORY_INFO.values():
            for key in category["keys"]:
                if key in handled_keys:
                    continue
                if key not in filters:
                    continue

                value = filters[key]
                if key in self.BOOLEAN_FILTER_KEYS:
                    if bool(value):
                        label = self.FILTER_LABELS.get(key, key.title())
                        parts.append(f"• {label}: On")
                    continue

                if value is None or value == "":
                    continue

                label = self.FILTER_LABELS.get(key, key.title())
                parts.append(f"• {label}: {value}")

        # Include any keys not covered above (future-proofing)
        for key, value in filters.items():
            if key in handled_keys or key in self.FILTER_LABELS:
                continue
            if not value:
                continue
            parts.append(f"• {key.title()}: {value}")

        return "\n".join(parts)

    def _parse_price_value(self, value: Any) -> Optional[float]:
        try:
            return float(value)
        except (TypeError, ValueError):
            return None

    def _category_status_flags(self, filters: dict) -> Dict[str, bool]:
        return {
            category: any(key in filters for key in meta["keys"])
            for category, meta in self.CATEGORY_INFO.items()
        }

    def _format_value_label(self, key: str, label: str, filters: dict) -> str:
        value = filters.get(key)
        if value in (None, ""):
            return label
        if key in {"priceFrom", "priceTo"}:
            parsed = self._parse_price_value(value)
            if parsed is not None:
                return f"{label}: ${parsed:.2f}"
        return f"{label}: {value}"

    def _format_toggle_button_text(self, key: str, label: str, filters: dict) -> str:
        return f"{'✅' if filters.get(key) else '⚪'} {label}"

    def _build_clear_buttons(
        self, filters: dict, keys: Iterable[str]
    ) -> list[list[InlineKeyboardButton]]:
        rows: list[list[InlineKeyboardButton]] = []
        current_row: list[InlineKeyboardButton] = []

        for key in keys:
            if key not in filters:
                continue
            label = self.FILTER_LABELS.get(key, key.title())
            current_row.append(
                InlineKeyboardButton(
                    text=f"❌ {label}", callback_data=f"filter:unset:{key}"
                )
            )
            if len(current_row) == 2:
                rows.append(current_row)
                current_row = []

        if current_row:
            rows.append(current_row)

        return rows

    def _category_footer_rows(self) -> list[list[InlineKeyboardButton]]:
        return [
            [
                InlineKeyboardButton(text="⬅️ Filters", callback_data="filter:back"),
                InlineKeyboardButton(text="✅ Apply", callback_data="filter:apply"),
            ],
            [InlineKeyboardButton(text="🧹 Clear All", callback_data="filter:clear")],
        ]

    def _format_price_button_text(self, filters: dict) -> str:
        price_from = self._parse_price_value(filters.get("priceFrom"))
        price_to = self._parse_price_value(filters.get("priceTo"))

        if price_from is not None and price_to is not None:
            return f"💲 Range: ${price_from:.2f}–${price_to:.2f}"
        if price_from is not None:
            return f"💲 Min: ${price_from:.2f}"
        if price_to is not None:
            return f"💲 Max: ${price_to:.2f}"
        return "💲 Price Presets"

    def _category_label(self, category: str) -> str:
        info = self.CATEGORY_INFO.get(category)
        return info.get("label", category.title()) if info else category.title()

    def _category_return_keyboard(self, category: str) -> InlineKeyboardMarkup:
        label = self._category_label(category)
        return InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text=f"⬅️ Back to {label}",
                        callback_data=f"filter:category:{category}",
                    )
                ],
                [InlineKeyboardButton(text="✅ Apply Filters", callback_data="filter:apply")],
            ]
        )

    def _manual_input_success_hint(self, key: str, filters: dict) -> str:
        category = self._category_from_key(key) or "card"
        label = self._category_label(category)

        base_hint = (
            f"Tap \"Back to {label}\" to adjust these filters or \"Apply Filters\" to search."
        )

        if key == "priceFrom":
            if "priceTo" not in filters:
                return (
                    f"Minimum price saved. Use \"Back to {label}\" to set a maximum or tap \"Apply Filters\" when ready."
                )
            return f"Price range updated. {base_hint}"

        if key == "priceTo":
            if "priceFrom" not in filters:
                return (
                    f"Maximum price saved. Use \"Back to {label}\" to set a minimum or tap \"Apply Filters\" when ready."
                )
            return f"Price range updated. {base_hint}"

        if key == "zip":
            return (
                f"ZIP saved. Tap \"Back to {label}\" to tweak location filters or \"Apply Filters\" to search."
            )

        if key == "bin":
            return (
                f"BIN saved. Tap \"Back to {label}\" to adjust card filters or \"Apply Filters\" to search."
            )

        if key in {"state", "city", "country"}:
            return (
                f"Location updated. Tap \"Back to {label}\" to refine or \"Apply Filters\" to search."
            )

        if key in {"bank", "brand", "type", "base"}:
            return (
                f"Card details saved. Tap \"Back to {label}\" to refine or \"Apply Filters\" to search."
            )

        return base_hint

    def _build_category_menu(
        self, category: str, filters: dict
    ) -> tuple[str, InlineKeyboardMarkup]:
        meta = self.CATEGORY_INFO.get(category)
        if not meta:
            raise ValueError(f"Unknown filter category: {category}")

        buttons: list[list[InlineKeyboardButton]] = []

        if category == "location":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_value_label(
                                "country", "🌍 Country", filters
                            ),
                            callback_data="filter:select:country",
                        ),
                        InlineKeyboardButton(
                            text=self._format_value_label(
                                "state", "🏙 State", filters
                            ),
                            callback_data="filter:select:state",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_value_label(
                                "city", "🏘 City", filters
                            ),
                            callback_data="filter:select:city",
                        ),
                        InlineKeyboardButton(
                            text=self._format_value_label("zip", "🏷 ZIP", filters),
                            callback_data="filter:select:zip",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "zipCheck", "ZIP Check", filters
                            ),
                            callback_data="filter:toggle:zipCheck",
                        )
                    ],
                ]
            )
            buttons.extend(self._build_clear_buttons(filters, ["country", "state", "city", "zip"]))

        elif category == "card":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text="✍️ Enter BIN",
                            callback_data="filter:input:bin",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_value_label("brand", "🏷 Brand", filters),
                            callback_data="filter:select:brand",
                        ),
                        InlineKeyboardButton(
                            text=self._format_value_label("type", "💳 Card Type", filters),
                            callback_data="filter:select:type",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_value_label("level", "🎚 Level", filters),
                            callback_data="filter:select:level",
                        ),
                        InlineKeyboardButton(
                            text=self._format_value_label("bank", "🏦 Bank", filters),
                            callback_data="filter:select:bank",
                        ),
                    ],
                ]
            )
            buttons.extend(
                self._build_clear_buttons(
                    filters, ["brand", "type", "level", "bank", "bin", "base"]
                )
            )

        elif category == "pricing":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_price_button_text(filters),
                            callback_data="filter:select:price",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="⬆️ Set Min", callback_data="filter:input:priceFrom"
                        ),
                        InlineKeyboardButton(
                            text="⬇️ Set Max", callback_data="filter:input:priceTo"
                        ),
                    ],
                ]
            )
            buttons.extend(self._build_clear_buttons(filters, ["priceFrom", "priceTo"]))

        elif category == "contact":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "address", "Address", filters
                            ),
                            callback_data="filter:toggle:address",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "phone", "Phone", filters
                            ),
                            callback_data="filter:toggle:phone",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "email", "Email", filters
                            ),
                            callback_data="filter:toggle:email",
                        )
                    ],
                ]
            )

        elif category == "identity":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("dob", "DOB", filters),
                            callback_data="filter:toggle:dob",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("ssn", "SSN", filters),
                            callback_data="filter:toggle:ssn",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("mmn", "MMN", filters),
                            callback_data="filter:toggle:mmn",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("dl", "Driving Licence", filters),
                            callback_data="filter:toggle:dl",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("ip", "IP", filters),
                            callback_data="filter:toggle:ip",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("ua", "User Agent", filters),
                            callback_data="filter:toggle:ua",
                        ),
                    ],
                ]
            )

        elif category == "extras":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "withoutcvv", "Without CVV", filters
                            ),
                            callback_data="filter:toggle:withoutcvv",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "refundable", "Refundable", filters
                            ),
                            callback_data="filter:toggle:refundable",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "expirethismonth", "Expire This Month", filters
                            ),
                            callback_data="filter:toggle:expirethismonth",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "discount", "Discount", filters
                            ),
                            callback_data="filter:toggle:discount",
                        ),
                    ],
                ]
            )

        else:
            raise ValueError(f"Unhandled filter category: {category}")

        buttons.extend(self._category_footer_rows())

        category_filters = {k: filters[k] for k in meta["keys"] if k in filters}
        summary = self._filters_summary_text(category_filters)

        text = f"{meta['label']} <b>Filters</b>\n\n{meta['description']}"
        if summary and summary != "No active filters":
            text += f"\n\n<b>Active:</b>\n{summary}"

        return text, InlineKeyboardMarkup(inline_keyboard=buttons)

    async def _show_category_menu(
        self, callback: CallbackQuery, category: str
    ) -> None:
        user_id = self._get_user_id(callback)
        filters = self._get_temp_filters(user_id)
        text, keyboard = self._build_category_menu(category, filters)
        await callback.message.edit_text(
            text + DEMO_WATERMARK,
            reply_markup=keyboard,
        )

    def _category_from_key(self, key: str) -> Optional[str]:
        return self.FILTER_KEY_TO_CATEGORY.get(key)

    async def _prompt_for_manual_input(
        self, callback: CallbackQuery, state: FSMContext, key: str
    ) -> None:
        config = self.MANUAL_INPUT_CONFIG.get(key)
        if not config:
            await callback.answer("Unsupported filter input", show_alert=True)
            return

        category = self._category_from_key(key) or "card"
        title = config.get("title", self.FILTER_LABELS.get(key, key.title()))
        prompt = config.get("prompt", "Send a value")

        await callback.message.edit_text(
            f"✍️ <b>{title}</b>\n\n{prompt}" + DEMO_WATERMARK,
            reply_markup=back_keyboard(f"filter:category:{category}"),
        )

        await state.set_state(self.CatalogFilterStates.WAITING_VALUE)
        await state.update_data(
            filter_key=key,
            category=category,
            input_mode=config.get("mode", "text"),
        )
        await callback.answer()

    def _set_manual_filter_value(
        self, user_id: int, key: str, raw_value: str, input_mode: str
    ) -> tuple[bool, str]:
        raw_value = (raw_value or "").strip()
        if not raw_value:
            return False, "Value cannot be empty."

        temp = self._get_temp_filters(user_id)
        label = self.FILTER_LABELS.get(key, key.title())

        if input_mode == "bin":
            digits = "".join(ch for ch in raw_value if ch.isdigit())
            if len(digits) < 4 or len(digits) > 8:
                return False, "BIN must be 4–8 digits."
            temp["bin"] = digits
            return True, f"BIN set to <b>{digits}</b>."

        if input_mode == "zip":
            digits = "".join(ch for ch in raw_value if ch.isdigit())
            if len(digits) < 3 or len(digits) > 10:
                return False, "ZIP must be 3–10 digits."
            temp["zip"] = digits
            return True, f"ZIP set to <b>{digits}</b>."

        if input_mode == "price":
            cleaned = raw_value.replace("$", "").replace(",", "")
            price = self._parse_price_value(cleaned)
            if price is None:
                return False, "Invalid price format."
            if price < 0:
                return False, "Price cannot be negative."
            if price > self.MAX_PRICE:
                return False, f"Price cannot exceed ${self.MAX_PRICE:,.0f}."

            other_key = "priceTo" if key == "priceFrom" else "priceFrom"
            other_value = self._parse_price_value(temp.get(other_key))

            if key == "priceFrom" and other_value is not None and price > other_value:
                return False, "Minimum price cannot exceed maximum price."
            if key == "priceTo" and other_value is not None and price < other_value:
                return False, "Maximum price cannot be below minimum price."

            if other_value is not None and abs(other_value - price) > self.MAX_PRICE_SPREAD:
                return False, "Price range too wide (max $5,000 difference)."

            temp[key] = f"{price:.2f}"
            return True, f"{label} set to <b>${price:.2f}</b>."

        try:
            sanitized = sanitize_text_input(raw_value, max_length=50)
        except ValidationError as exc:
            return False, str(exc)

        if key == "state":
            sanitized = sanitized.upper()
            if not re.fullmatch(r"[A-Z0-9\-\s]{2,20}", sanitized):
                return False, "State should be 2–20 characters (letters, numbers, spaces, -)."
        elif key == "city":
            sanitized = sanitized.title()
            if not re.fullmatch(r"[A-Za-z0-9\-\s]{2,50}", sanitized):
                return False, "City should be 2–50 characters (letters, numbers, spaces, -)."
        elif key == "bank":
            sanitized = sanitized.upper()
            if not re.fullmatch(r"[A-Z0-9&\.\-\s]{2,50}", sanitized):
                return False, "Bank name should be 2–50 characters (letters, numbers, spaces, & . -)."
        elif key == "base":
            sanitized = sanitized.title()
            if not re.fullmatch(r"[A-Za-z0-9\-\s]{2,50}", sanitized):
                return False, "Base should be 2–50 characters (letters, numbers, spaces, -)."

        temp[key] = sanitized
        return True, f"{label} set to <b>{sanitized}</b>."

    def _clear_user_filters(self, user_id: int) -> None:
        """Clear all filters for a user"""
        self.user_filters[user_id] = {}
        self.user_applied_filters[user_id] = {}
        self.user_option_tokens.pop(user_id, None)

    def _has_active_filters(self, user_id: int) -> bool:
        """Check if user has any active filters"""
        applied = self._get_applied_filters(user_id)
        return bool(applied)

    async def _build_filter_selection_menu(
        self,
        filter_key: str,
        category: str,
        filters: dict,
        user_id: Optional[int] = None,
        page: int = 0,
    ) -> tuple[str, InlineKeyboardMarkup]:
        rows: list[list[InlineKeyboardButton]] = []
        info_lines: list[str] = []
        label = self.FILTER_LABELS.get(filter_key, filter_key.title())

        if filter_key in self.DYNAMIC_FILTER_KEYS:
            request_filters = filters.copy()
            for key_to_remove in self.DYNAMIC_FILTER_RESET_MAP.get(filter_key, {filter_key}):
                request_filters.pop(key_to_remove, None)

            if filter_key == "state" and not request_filters.get("country"):
                info_lines.append("Tip: select a country first to refine state options.")
            elif filter_key == "city" and not request_filters.get("state"):
                info_lines.append("Tip: select a state first to refine city options.")
            elif filter_key == "zip" and not request_filters.get("city"):
                info_lines.append("Tip: select a city first to refine ZIP options.")

            card_service = await self._get_card_service(user_id)
            response = await card_service.fetch_filter_options(
                filter_name=filter_key,
                filters=request_filters,
                user_id=str(user_id) if user_id else None,
            )

            if not response.get("success"):
                error_detail = response.get("error") or ""
                if error_detail:
                    logger.warning(
                        f"Filter options request failed for {filter_key}: {error_detail}"
                    )
                info_lines.append(
                    "⚠️ Live options unavailable. Try manual input or retry later."
                )
                options_data: list[dict] = []
            else:
                options_data = self._sort_dynamic_options(response.get("data") or [])

            actual_user_id = user_id or 0
            limit = self.FILTER_OPTION_LIMIT
            total_options = len(options_data)
            total_pages = max(1, (total_options + limit - 1) // limit) if options_data else 0
            current_page = page if total_pages else 0
            if total_pages:
                current_page = max(0, min(current_page, total_pages - 1))

            if options_data:
                start_index = current_page * limit
                end_index = min(start_index + limit, total_options)
                page_options = options_data[start_index:end_index]

                rows.extend(
                    self._build_dynamic_option_rows(
                        filter_key, page_options, user_id, current_page
                    )
                )

                info_lines.append(
                    f"Showing results {start_index + 1}-{end_index} of {total_options}."
                )
                if total_pages > 1:
                    info_lines.append(
                        f"Page {current_page + 1} of {total_pages}."
                    )
            else:
                self.user_option_tokens.setdefault(actual_user_id, {}).pop(
                    filter_key, None
                )
                if not info_lines:
                    info_lines.append("No results returned. Try manual input.")

            if filter_key in self.MANUAL_INPUT_CONFIG:
                rows.append(
                    [
                        InlineKeyboardButton(
                            text="✍️ Enter Manually",
                            callback_data=f"filter:input:{filter_key}",
                        )
                    ]
                )

            if total_pages > 1:
                nav_row: list[InlineKeyboardButton] = []
                if current_page > 0:
                    nav_row.append(
                        InlineKeyboardButton(
                            text="⬅️ Previous",
                            callback_data=f"filter:page:{filter_key}:{current_page - 1}",
                        )
                    )
                if current_page < total_pages - 1:
                    nav_row.append(
                        InlineKeyboardButton(
                            text="Next ➡️",
                            callback_data=f"filter:page:{filter_key}:{current_page + 1}",
                        )
                    )
                if nav_row:
                    rows.append(nav_row)
        else:
            if filter_key == "brand":
                options = list(self.PRESET_BRANDS)
                for chunk in self._chunk_list(options, 2):
                    rows.append(
                        [
                            InlineKeyboardButton(
                                text=brand,
                                callback_data=f"filter:set:brand:{brand}",
                            )
                            for brand in chunk
                        ]
                    )
            elif filter_key == "type":
                rows.append(
                    [
                        InlineKeyboardButton(
                            text=card_type,
                            callback_data=f"filter:set:type:{card_type}",
                        )
                        for card_type in self.PRESET_TYPES
                    ]
                )
            elif filter_key == "price":
                for preset_label, price_from, price_to in self.PRICE_PRESETS:
                    rows.append(
                        [
                            InlineKeyboardButton(
                                text=preset_label,
                                callback_data=f"filter:set:price:{price_from}-{price_to}",
                            )
                        ]
                    )
            elif filter_key == "bin":
                options = list(self.PRESET_BINS)
                for chunk in self._chunk_list(options, 3):
                    rows.append(
                        [
                            InlineKeyboardButton(
                                text=bn, callback_data=f"filter:set:bin:{bn}"
                            )
                            for bn in chunk
                        ]
                    )

        if filter_key == "price":
            if "priceFrom" in filters or "priceTo" in filters:
                rows.append(
                    [
                        InlineKeyboardButton(
                            text="❌ Clear Range", callback_data="filter:unset:price"
                        )
                    ]
                )
        elif filter_key in filters:
            rows.append(
                [
                    InlineKeyboardButton(
                        text=f"❌ Clear {label}",
                        callback_data=f"filter:unset:{filter_key}",
                    )
                ]
            )

        rows.append(
            [
                InlineKeyboardButton(
                    text="⬅️ Back", callback_data=f"filter:category:{category}"
                ),
                InlineKeyboardButton(text="✅ Apply", callback_data="filter:apply"),
            ]
        )

        message_lines = [f"🎯 <b>{label}</b>", "", "Choose an option:"]
        if info_lines:
            message_lines.append("")
            message_lines.extend(info_lines)

        return "\n".join(message_lines), InlineKeyboardMarkup(inline_keyboard=rows)

    async def _render_cards_page(self, callback: CallbackQuery, page: int) -> None:
        """Render cards list for a given page using applied filters."""
        try:
            await callback.answer("⏳ Loading cards...")

            user_id = self._get_user_id(callback)
            applied = self._get_applied_filters(user_id)
            card_service = await self._get_card_service(user_id)
            cards_data = await card_service.fetch_cards(
                page=page, limit=5, filters=applied
            )

            if not cards_data.get("success", False):
                error_msg = cards_data.get("error", "Unknown error")
                await callback.message.edit_text(
                    f"❌ <b>Error fetching cards</b>\n\n"
                    f"Error: {error_msg}" + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            cards = cards_data.get("data", [])
            total_count = cards_data.get("totalCount", 0)

            # Cache the current cards for this user
            self.user_current_cards[user_id] = cards

            if not cards:
                await callback.message.edit_text(
                    "📭 <b>No cards found</b>\n\nNo cards available on this page."
                    + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            # Format cards for display with filter status
            user_id = self._get_user_id(callback)
            applied_filters = self._get_applied_filters(user_id)

            cards_text = f"🛒 <b>Cards (Page {page})</b>\n\n"

            # Show active filters if any
            if applied_filters:
                filter_summary = self._filters_summary_text(applied_filters)
                cards_text += f"🔍 <b>Active Filters:</b>\n{filter_summary}\n\n"
                cards_text += "─" * 40 + "\n\n"

            for i, card in enumerate(cards, 1):
                cards_text += (
                    f"<b>{i}.</b> "
                    + card_service.format_card_for_display(card)
                    + "\n"
                )
                if i < len(cards):
                    cards_text += "—" * 30 + "\n\n"

            # Create pagination keyboard
            keyboard_buttons = []

            nav_buttons = []
            if page > 1:
                nav_buttons.append(
                    InlineKeyboardButton(
                        text="⬅️ Previous",
                        callback_data=f"catalog:view_cards:{page-1}",
                    )
                )

            max_page = (total_count + 4) // 5
            if page < max_page:
                nav_buttons.append(
                    InlineKeyboardButton(
                        text="➡️ Next", callback_data=f"catalog:view_cards:{page+1}"
                    )
                )

            if nav_buttons:
                keyboard_buttons.append(nav_buttons)

            if cards:
                cart_buttons = []
                for card in cards[:5]:
                    card_id = card.get("_id")
                    bin_number = card.get("bin", "N/A")
                    cart_buttons.append(
                        InlineKeyboardButton(
                            text=f"🛒 Add {bin_number}",
                            callback_data=f"catalog:add_to_cart:{card_id}",
                        )
                    )

                for i in range(0, len(cart_buttons), 2):
                    keyboard_buttons.append(cart_buttons[i : i + 2])

            # Action buttons with filter-aware options
            if applied_filters:
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="🔍 New Search", callback_data="catalog:search"
                        ),
                        InlineKeyboardButton(
                            text="🧰 Edit Filters", callback_data="catalog:filters"
                        ),
                    ]
                )
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="🧹 Clear Filters", callback_data="filter:clear"
                        ),
                        InlineKeyboardButton(
                            text="🛒 View Cart", callback_data="local:cart:view"
                        ),
                    ]
                )
            else:
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="🔍 New Search", callback_data="catalog:search"
                        ),
                        InlineKeyboardButton(
                            text="🛒 View Cart", callback_data="local:cart:view"
                        ),
                    ]
                )

            keyboard_buttons.append(
                [InlineKeyboardButton(text="◀️ Back", callback_data="menu:browse")]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            footer_text = (
                f"\n\n📊 <b>Page {page} of {max_page}</b> | "
                f"Total: {total_count:,} cards\n"
                "<i>This is demo data from an external API</i>" + DEMO_WATERMARK
            )

            await callback.message.edit_text(
                cards_text + footer_text,
                reply_markup=keyboard,
            )
        except Exception as e:
            logger.error(f"Error rendering cards page: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_browse_menu(self, callback: CallbackQuery) -> None:
        """Handle browse menu callback with product-aware interface"""
        try:
            user_id = self._get_user_id(callback)
            applied_filters = self._get_applied_filters(user_id)

            # Get current product selection
            user = callback.from_user
            current_product, current_api = None, None
            if user:
                current_product, current_api = await self.product_service.get_user_current_selection(user.id)

            # Build message with product and filter status
            message = "🛒 <b>Browse Catalog</b>\n\n"

            # Show current product/API selection
            if current_product and current_api:
                api_info = await self.product_service.get_api_info(current_api)
                if api_info:
                    status_emoji = "🟢" if api_info.status.value == "active" else "🔴"
                    message += f"📍 <b>Current Source:</b>\n"
                    message += f"   {current_product.value.upper()} → {api_info.name} {status_emoji}\n\n"
                else:
                    message += f"⚠️ <b>No API Selected</b>\n"
                    message += f"Please select a product and API first.\n\n"
            else:
                message += f"⚠️ <b>No Product Selected</b>\n"
                message += f"Please select a product and API first.\n\n"

            if applied_filters:
                filter_summary = self._filters_summary_text(applied_filters)
                message += f"🔍 <b>Active Filters:</b>\n{filter_summary}\n\n"
                message += "Choose an option to explore available items:"
            else:
                message += "Choose an option to explore available items:"

            # Create enhanced browse menu with filter-aware options
            keyboard_buttons = [
                [
                    InlineKeyboardButton(
                        text="🔎 Search", callback_data="catalog:search"
                    ),
                    InlineKeyboardButton(
                        text="🧰 Filters", callback_data="catalog:filters"
                    ),
                ],
            ]

            # Add browse all option
            if applied_filters:
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="📄 Browse with Filters",
                            callback_data="catalog:browse_filtered",
                        ),
                        InlineKeyboardButton(
                            text="📋 Browse All", callback_data="catalog:browse_all"
                        ),
                    ]
                )
            else:
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text="📄 Browse All", callback_data="catalog:browse_all"
                        ),
                    ]
                )

            # Add product selection option if no product selected
            if not current_product or not current_api:
                keyboard_buttons.insert(0, [
                    InlineKeyboardButton(text="🛍️ Select Product", callback_data="menu:products")
                ])

            # Add navigation with product context
            nav_buttons = []
            if current_product and current_api:
                nav_buttons.append(
                    InlineKeyboardButton(text="🔄 Switch API", callback_data=f"product:apis:{current_product.value}")
                )
            nav_buttons.append(
                InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main")
            )
            keyboard_buttons.append(nav_buttons)

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            await callback.message.edit_text(
                message + DEMO_WATERMARK,
                reply_markup=keyboard,
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in browse menu: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_search(self, callback: CallbackQuery) -> None:
        """Handle search callback"""
        try:
            await callback.answer("🔍 Fetching cards...")

            # Fetch cards from the API
            user_id = self._get_user_id(callback)
            card_service = await self._get_card_service(user_id)
            cards_data = await card_service.fetch_cards(
                page=1,
                limit=5,
                filters=self._get_applied_filters(user_id),
            )

            if not cards_data.get("success", False):
                error_msg = cards_data.get("error", "Unknown error")
                await callback.message.edit_text(
                    f"❌ <b>Error fetching cards</b>\n\n"
                    f"Error: {error_msg}\n\n"
                    "<i>This is demo data from an external API</i>" + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            cards = cards_data.get("data", [])
            if not cards:
                await callback.message.edit_text(
                    INFO_NO_RESULTS + "\n\n"
                    "<i>No cards found with current filters</i>" + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            # Generate summary and show first few cards
            summary = await card_service.get_card_summary(cards_data)

            # Create pagination keyboard
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="📄 View Cards", callback_data="catalog:view_cards:1"
                        ),
                        InlineKeyboardButton(
                            text="🔍 New Search", callback_data="catalog:search"
                        ),
                    ],
                    [InlineKeyboardButton(text="🔙 Back", callback_data="menu:browse")],
                ]
            )

            await callback.message.edit_text(
                summary
                + "\n\n<i>This is demo data from an external API</i>"
                + DEMO_WATERMARK,
                reply_markup=keyboard,
            )

        except Exception as e:
            logger.error(f"Error in search: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    

    async def cb_filters(self, callback: CallbackQuery) -> None:
        """Handle filters callback"""
        try:
            user_id = self._get_user_id(callback)
            temp_filters = self._get_temp_filters(user_id)
            applied_filters = self._get_applied_filters(user_id)

            # Build message with current filter status
            message = "🗂️ <b>Search Filters</b>\n\n"

            if temp_filters:
                temp_summary = self._filters_summary_text(temp_filters)
                message += f"⚙️ <b>Pending Filters:</b>\n{temp_summary}\n\n"

            if applied_filters:
                applied_summary = self._filters_summary_text(applied_filters)
                message += f"✅ <b>Applied Filters:</b>\n{applied_summary}\n\n"

            message += "Choose a category to adjust available filters."

            category_status = self._category_status_flags(temp_filters)

            await callback.message.edit_text(
                message + DEMO_WATERMARK,
                reply_markup=filter_menu_keyboard(category_status),
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in filters: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_filter_set(self, callback: CallbackQuery) -> None:
        """Compatibility wrapper for tests expecting cb_filter_set.

        Forwards to cb_filter_actions which handles filter:set:* callbacks.
        """
        return await self.cb_filter_actions(callback)

    async def cb_browse_filtered(self, callback: CallbackQuery) -> None:
        """Handle browse with filters callback"""
        try:
            user_id = self._get_user_id(callback)
            applied_filters = self._get_applied_filters(user_id)

            if not applied_filters:
                await callback.answer(
                    "No filters applied. Use 'Browse All' instead.", show_alert=True
                )
                return

            await callback.answer("🔍 Loading filtered results...")
            await self._render_cards_page(callback, page=1)

        except Exception as e:
            logger.error(f"Error in browse filtered: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_browse_all(self, callback: CallbackQuery) -> None:
        """Handle browse all callback (no filters)"""
        try:
            user_id = self._get_user_id(callback)
            # Temporarily clear applied filters for this browse session
            original_filters = self._get_applied_filters(user_id).copy()
            self.user_applied_filters[user_id] = {}

            await callback.answer("📄 Loading all cards...")
            await self._render_cards_page(callback, page=1)

            # Restore original filters after rendering
            self.user_applied_filters[user_id] = original_filters

        except Exception as e:
            logger.error(f"Error in browse all: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_filter_actions(
        self, callback: CallbackQuery, state: Optional[FSMContext] = None
    ) -> None:
        """Handle filter action callbacks."""

        try:
            parts = callback.data.split(":")
            action = parts[1] if len(parts) > 1 else ""

            user_id = self._get_user_id(callback)
            temp_filters = self._get_temp_filters(user_id)

            if action == "clear":
                self._clear_user_filters(user_id)
                await callback.answer("Filters cleared")
                await self.cb_filters(callback)
                return

            if action == "apply":
                self.user_applied_filters[user_id] = temp_filters.copy()
                await callback.answer("Filters applied")
                await self._render_cards_page(callback, page=1)
                return

            if action == "back":
                await self.cb_filters(callback)
                return

            if action == "category" and len(parts) >= 3:
                category = parts[2]
                await self._show_category_menu(callback, category)
                await callback.answer()
                return

            if action == "select" and len(parts) >= 3:
                filter_key = parts[2]
                category = self._category_from_key(filter_key) or "card"
                menu_text, keyboard = await self._build_filter_selection_menu(
                    filter_key,
                    category,
                    temp_filters,
                    user_id=user_id,
                    page=0,
                )
                await callback.message.edit_text(
                    menu_text + "\n" + DEMO_WATERMARK,
                    reply_markup=keyboard,
                )
                await callback.answer()
                return

            if action == "page" and len(parts) >= 4:
                filter_key = parts[2]
                try:
                    page_number = int(parts[3])
                except ValueError:
                    await callback.answer("Invalid page", show_alert=True)
                    return

                category = self._category_from_key(filter_key) or "card"
                menu_text, keyboard = await self._build_filter_selection_menu(
                    filter_key,
                    category,
                    temp_filters,
                    user_id=user_id,
                    page=page_number,
                )
                await callback.message.edit_text(
                    menu_text + "\n" + DEMO_WATERMARK,
                    reply_markup=keyboard,
                )
                await callback.answer()
                return

            if action == "input" and len(parts) >= 3:
                if state is None:
                    await callback.answer("State unavailable", show_alert=True)
                    return
                key = parts[2]
                await self._prompt_for_manual_input(callback, state, key)
                return

            if action == "bin_manual":  # Backward compatibility
                if state is None:
                    await callback.answer("State unavailable", show_alert=True)
                    return
                await self._prompt_for_manual_input(callback, state, "bin")
                return

            if action == "toggle" and len(parts) >= 3:
                key = parts[2]
                label = self.FILTER_LABELS.get(key, key.title())
                if temp_filters.get(key):
                    temp_filters.pop(key, None)
                    await callback.answer(f"{label} disabled")
                else:
                    temp_filters[key] = True
                    await callback.answer(f"{label} enabled")

                category = self._category_from_key(key)
                if category:
                    await self._show_category_menu(callback, category)
                else:
                    await self.cb_filters(callback)
                return

            if action == "unset" and len(parts) >= 3:
                key = parts[2]
                if key == "price":
                    temp_filters.pop("priceFrom", None)
                    temp_filters.pop("priceTo", None)
                else:
                    temp_filters.pop(key, None)

                label = self.FILTER_LABELS.get(key, key.title())
                await callback.answer(f"{label} cleared")

                category = self._category_from_key(key)
                if key == "price":
                    category = "pricing"
                if category:
                    await self._show_category_menu(callback, category)
                else:
                    await self.cb_filters(callback)
                return

            if action == "set" and len(parts) >= 4:
                key = parts[2]
                value = ":".join(parts[3:])
                label = self.FILTER_LABELS.get(key, key.title())

                if key in self.DYNAMIC_FILTER_KEYS:
                    resolved_value = self._resolve_dynamic_option_value(
                        user_id, key, value
                    )
                    if resolved_value is not None:
                        value = resolved_value
                    else:
                        token_entry = (
                            self.user_option_tokens.get(user_id, {}).get(key, {})
                        )
                        if token_entry.get("tokens"):
                            await callback.answer(
                                "Option expired. Open the filter again to refresh.",
                                show_alert=True,
                            )
                            return

                if key == "price" and "-" in value:
                    try:
                        p_from_str, p_to_str = value.rsplit("-", 1)
                        price_from = float(p_from_str.strip())
                        price_to = float(p_to_str.strip())
                    except ValueError:
                        await callback.answer(
                            "Invalid price range", show_alert=True
                        )
                        return

                    if price_from < 0 or price_to < 0:
                        await callback.answer(
                            "Prices cannot be negative", show_alert=True
                        )
                        return
                    if price_from > price_to:
                        await callback.answer(
                            "Minimum price cannot exceed maximum price",
                            show_alert=True,
                        )
                        return
                    if price_from > self.MAX_PRICE or price_to > self.MAX_PRICE:
                        await callback.answer(
                            f"Price cannot exceed ${self.MAX_PRICE:,.0f}",
                            show_alert=True,
                        )
                        return
                    if price_to - price_from > self.MAX_PRICE_SPREAD:
                        await callback.answer(
                            "Price range too wide (max $5,000 difference)",
                            show_alert=True,
                        )
                        return

                    temp_filters["priceFrom"] = f"{price_from:.2f}"
                    temp_filters["priceTo"] = f"{price_to:.2f}"
                    await callback.answer(
                        f"Price range set: ${price_from:.2f}–${price_to:.2f}"
                    )
                    await self._show_category_menu(callback, "pricing")
                    return

                trimmed_value = value.strip()
                if key != "price" and trimmed_value == "":
                    temp_filters.pop(key, None)
                    await callback.answer(f"{label} cleared")
                    category = self._category_from_key(key)
                    if category:
                        await self._show_category_menu(callback, category)
                    else:
                        await self.cb_filters(callback)
                    return

                temp_filters[key] = trimmed_value
                await callback.answer(f"{label} updated")
                category = self._category_from_key(key)
                if category:
                    await self._show_category_menu(callback, category)
                else:
                    await self.cb_filters(callback)
                return

            await callback.answer("Unknown filter action", show_alert=True)

        except Exception as e:
            logger.error(f"Error in filter actions: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def message_set_filter_value(
        self, message: Message, state: FSMContext
    ) -> None:
        """Handle manual filter input values."""

        try:
            user = message.from_user
            if not user:
                await message.answer("❌ Unable to identify user")
                return

            data = await state.get_data()
            key = data.get("filter_key")
            category = data.get("category") or self._category_from_key(key or "")
            input_mode = data.get("input_mode", "text")

            if not key:
                await message.answer("❌ No filter is awaiting input.")
                await state.clear()
                return

            temp_filters = self._get_temp_filters(user.id)
            success, response = self._set_manual_filter_value(
                user.id, key, message.text or "", input_mode
            )

            if not success:
                await message.answer(f"❌ {response}")
                return

            await state.clear()
            instructions = self._manual_input_success_hint(key, temp_filters)

            reply_markup = (
                self._category_return_keyboard(category)
                if category
                else filter_menu_keyboard(self._category_status_flags(temp_filters))
            )

            message_text = f"✅ {response}"
            if instructions:
                message_text += f"\n\n{instructions}"

            await message.answer(message_text, reply_markup=reply_markup)

        except Exception as e:
            logger.error(f"Error setting filter value: {e}")
            await message.answer("❌ Error setting filter value")

    async def cb_view_cards(self, callback: CallbackQuery) -> None:
        """Handle view cards callback with pagination"""
        try:
            # Extract page number from callback data
            parts = callback.data.split(":")
            page = int(parts[2]) if len(parts) > 2 else 1

            await callback.answer("📄 Loading cards...")

            # Fetch cards for the specified page
            user_id = self._get_user_id(callback)
            applied = self._get_applied_filters(user_id)
            card_service = await self._get_card_service(user_id)
            cards_data = await card_service.fetch_cards(
                page=page, limit=5, filters=applied
            )

            if not cards_data.get("success", False):
                error_msg = cards_data.get("error", "Unknown error")
                await callback.message.edit_text(
                    f"❌ <b>Error fetching cards</b>\n\n"
                    f"Error: {error_msg}" + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            cards = cards_data.get("data", [])
            total_count = cards_data.get("totalCount", 0)

            # Cache the current cards for this user
            self.user_current_cards[user_id] = cards

            if not cards:
                await callback.message.edit_text(
                    "📭 <b>No cards found</b>\n\n"
                    "No cards available on this page." + DEMO_WATERMARK,
                    reply_markup=back_keyboard("menu:browse"),
                )
                return

            # Format cards for display
            cards_text = f"💳 <b>Cards (Page {page})</b>\n\n"

            for i, card in enumerate(cards, 1):
                cards_text += (
                    f"<b>{i}.</b> "
                    + card_service.format_card_for_display(card)
                    + "\n"
                )
                if i < len(cards):  # Add separator between cards
                    cards_text += "─" * 30 + "\n\n"

            # Create pagination keyboard
            keyboard_buttons = []

            # Navigation buttons
            nav_buttons = []
            if page > 1:
                nav_buttons.append(
                    InlineKeyboardButton(
                        text="⬅️ Previous", callback_data=f"catalog:view_cards:{page-1}"
                    )
                )

            # Calculate if there are more pages (assuming 5 cards per page)
            max_page = (total_count + 4) // 5  # Round up division
            if page < max_page:
                nav_buttons.append(
                    InlineKeyboardButton(
                        text="➡️ Next", callback_data=f"catalog:view_cards:{page+1}"
                    )
                )

            if nav_buttons:
                keyboard_buttons.append(nav_buttons)

            # Add to cart buttons for each card
            if cards:
                cart_buttons = []
                for card in cards[:5]:  # Show add to cart for first 5 cards
                    card_id = card.get("_id")
                    bin_number = card.get("bin", "N/A")
                    cart_buttons.append(
                        InlineKeyboardButton(
                            text=f"🛒 Add {bin_number}",
                            callback_data=f"catalog:add_to_cart:{card_id}",
                        )
                    )

                # Split into rows of 2 buttons each
                for i in range(0, len(cart_buttons), 2):
                    keyboard_buttons.append(cart_buttons[i : i + 2])

            # Action buttons
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="🔍 New Search", callback_data="catalog:search"
                    ),
                    InlineKeyboardButton(
                        text="🛒 View Cart", callback_data="local:cart:view"
                    ),
                ]
            )
            keyboard_buttons.append(
                [InlineKeyboardButton(text="🔙 Back", callback_data="menu:browse")]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            footer_text = (
                f"\n\n📊 <b>Page {page} of {max_page}</b> | "
                f"Total: {total_count:,} cards\n"
                "<i>This is demo data from an external API</i>" + DEMO_WATERMARK
            )

            await callback.message.edit_text(
                cards_text + footer_text,
                reply_markup=keyboard,
            )

        except Exception as e:
            logger.error(f"Error in view cards: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_add_to_cart(self, callback: CallbackQuery) -> None:
        """Handle add to cart callback"""
        try:
            if self.user_service is None:
                self.user_service = UserService()
            if self.cart_service is None:
                self.cart_service = CartService()
            # Extract card ID from callback data
            parts = callback.data.split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = int(parts[2])

            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer(
                    "❌ User not found. Please start the bot with /start",
                    show_alert=True,
                )
                return

            await callback.answer("🛒 Adding to cart...")

            # Find card data from cache
            user_id = self._get_user_id(callback)
            cached_cards = self.user_current_cards.get(user_id, [])
            card_data = None

            for card in cached_cards:
                if card.get("_id") == card_id:
                    card_data = card
                    break

            # Add to cart with card data if available
            success, message = await self.cart_service.add_to_cart(
                str(user_doc.id), card_id, 1, card_data
            )

            if success:
                # Get updated cart count
                cart_count = await self.cart_service.get_cart_item_count(
                    str(user_doc.id)
                )

                await callback.answer(f"✅ {message} (Cart: {cart_count} items)")

                # Update the message to show cart option
                current_text = callback.message.text or callback.message.caption or ""

                # Add cart notification to current message
                if "🛒 Item added to cart!" not in current_text:
                    updated_text = (
                        current_text
                        + f"\n\n🛒 <b>Item added to cart!</b> (Total: {cart_count} items)"
                    )

                    # Create updated keyboard with cart view option
                    keyboard = InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🛒 View Cart", callback_data="local:cart:view"
                                ),
                                InlineKeyboardButton(
                                    text="🔍 Continue Shopping",
                                    callback_data="catalog:search",
                                ),
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back", callback_data="menu:browse"
                                )
                            ],
                        ]
                    )

                    await callback.message.edit_text(
                        updated_text,
                        reply_markup=keyboard,
                    )
            else:
                await callback.answer(f"❌ {message}", show_alert=True)

        except Exception as e:
            logger.error(f"Error adding to cart: {e}")
            await callback.answer("❌ Error adding to cart", show_alert=True)


def get_catalog_router() -> Router:
    """Create and return catalog router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = CatalogHandlers()

    # Callback handlers
    router.callback_query.register(handlers.cb_browse_menu, F.data == "menu:browse")
    router.callback_query.register(handlers.cb_search, F.data == "catalog:search")
    
    router.callback_query.register(handlers.cb_filters, F.data == "catalog:filters")

    # Browse handlers
    router.callback_query.register(
        handlers.cb_browse_filtered, F.data == "catalog:browse_filtered"
    )
    router.callback_query.register(
        handlers.cb_browse_all, F.data == "catalog:browse_all"
    )

    router.callback_query.register(
        handlers.cb_view_cards, F.data.startswith("catalog:view_cards:")
    )
    router.callback_query.register(
        handlers.cb_add_to_cart, F.data.startswith("catalog:add_to_cart:")
    )

    # Filter callbacks
    router.callback_query.register(
        handlers.cb_filter_actions, F.data.startswith("filter:")
    )
    # FSM message handler for manual BIN entry
    router.message.register(
        handlers.message_set_filter_value,
        StateFilter(CatalogHandlers.CatalogFilterStates.WAITING_VALUE),
    )

    logger.info("Catalog handlers registered")
    return router
