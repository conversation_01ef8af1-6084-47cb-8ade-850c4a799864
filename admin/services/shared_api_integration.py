"""
Integration Service for Shared API System

This service handles the integration between the admin interface and the shared API system,
ensuring that configurations are properly synchronized and the registry is kept up-to-date.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from shared_api.config.registry import api_registry
from shared_api.config.client_factory import api_client_factory
from shared_api.config.api_config import APIConfiguration
from shared_api.examples.api_v1_config import create_api_v1_configuration
from shared_api.examples.new_api_config import create_new_api_configuration
from api_v2.config.api_config import create_api_v2_configuration
from admin.models.api_config_storage import get_admin_api_service, AdminAPIConfiguration

logger = logging.getLogger(__name__)


class SharedAPIIntegrationService:
    """
    Service for integrating admin interface with shared API system
    
    This service ensures that:
    - Admin configurations are synchronized with the shared API registry
    - Existing configurations are migrated to the new system
    - The shared API system is properly initialized with admin configurations
    """
    
    def __init__(self):
        self.admin_storage = get_admin_api_service()
        self._initialized = False
    
    async def initialize_integration(self) -> bool:
        """
        Initialize the integration between admin and shared API systems
        
        Returns:
            True if initialization was successful
        """
        try:
            if self._initialized:
                return True
            
            logger.info("Initializing shared API integration...")
            
            # Load existing admin configurations
            admin_configs = await self.admin_storage.list_api_configs(enabled_only=True)
            
            # Ensure baseline configurations exist
            admin_configs = await self._ensure_base_configs(admin_configs)

            # Register each configuration with the shared API registry
            registered_count = 0
            for admin_config in admin_configs:
                try:
                    shared_config = admin_config.to_shared_config()
                    api_registry.register_api(shared_config)
                    registered_count += 1
                    logger.debug(f"Registered API configuration: {admin_config.name}")
                except Exception as e:
                    logger.error(f"Failed to register API configuration '{admin_config.name}': {e}")
            
            self._initialized = True
            logger.info(f"Shared API integration initialized successfully. Registered {registered_count} APIs.")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize shared API integration: {e}")
            return False
    
    async def sync_configuration(self, api_name: str) -> bool:
        """
        Synchronize a specific configuration between admin and shared API systems
        
        Args:
            api_name: Name of the API to synchronize
            
        Returns:
            True if synchronization was successful
        """
        try:
            # Get admin configuration
            admin_config = await self.admin_storage.get_api_config(api_name)
            if not admin_config:
                logger.warning(f"Admin configuration not found for API: {api_name}")
                return False
            
            # Convert to shared configuration and register
            shared_config = admin_config.to_shared_config()
            api_registry.register_api(shared_config)
            
            logger.debug(f"Synchronized API configuration: {api_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to sync configuration for '{api_name}': {e}")
            return False
    
    async def sync_all_configurations(self) -> Tuple[int, int]:
        """
        Synchronize all configurations between admin and shared API systems
        
        Returns:
            Tuple of (successful_syncs, total_configs)
        """
        try:
            admin_configs = await self.admin_storage.list_api_configs(enabled_only=True)
            successful_syncs = 0
            
            for admin_config in admin_configs:
                if await self.sync_configuration(admin_config.name):
                    successful_syncs += 1
            
            logger.info(f"Synchronized {successful_syncs}/{len(admin_configs)} API configurations")
            return successful_syncs, len(admin_configs)
            
        except Exception as e:
            logger.error(f"Failed to sync all configurations: {e}")
            return 0, 0
    
    async def migrate_legacy_configuration(
        self,
        legacy_config: Dict[str, Any],
        created_by: str
    ) -> Optional[AdminAPIConfiguration]:
        """
        Migrate a legacy API configuration to the new system
        
        Args:
            legacy_config: Legacy configuration data
            created_by: User ID who initiated the migration
            
        Returns:
            Created AdminAPIConfiguration or None if migration failed
        """
        try:
            # Extract basic information
            name = legacy_config.get("service_name", legacy_config.get("name", "unknown"))
            base_url = legacy_config.get("base_url", "")
            
            if not name or not base_url:
                logger.error("Legacy configuration missing required fields")
                return None
            
            # Convert legacy endpoints to new format
            endpoints = {}
            legacy_endpoints = legacy_config.get("endpoints", {})
            
            for ep_name, ep_data in legacy_endpoints.items():
                if isinstance(ep_data, dict):
                    endpoints[ep_name] = {
                        "path": ep_data.get("path", f"/{ep_name}"),
                        "method": ep_data.get("method", "GET"),
                        "description": ep_data.get("description", "")
                    }
                else:
                    # Simple string endpoint
                    endpoints[ep_name] = {
                        "path": str(ep_data),
                        "method": "GET",
                        "description": ""
                    }
            
            # Convert legacy authentication
            auth_type = "none"
            auth_data = {}
            
            legacy_auth = legacy_config.get("authentication", {})
            legacy_creds = legacy_config.get("credentials", {})
            
            if legacy_creds.get("login_token") or legacy_auth.get("bearer_token"):
                auth_type = "bearer_token"
                auth_data = {
                    "bearer_token": legacy_creds.get("login_token") or legacy_auth.get("bearer_token")
                }
            elif legacy_auth.get("api_key"):
                auth_type = "api_key"
                auth_data = {
                    "api_key": legacy_auth["api_key"],
                    "api_key_header": legacy_auth.get("api_key_header", "X-API-Key")
                }
            
            # Create new configuration using admin service
            from admin.services.shared_api_admin_service import get_shared_api_admin_service
            admin_service = get_shared_api_admin_service()
            
            success, message, admin_config = await admin_service.create_api_configuration(
                name=name,
                base_url=base_url,
                created_by=created_by,
                display_name=legacy_config.get("display_name", name),
                description=f"Migrated from legacy configuration: {legacy_config.get('description', '')}",
                auth_type=auth_type,
                auth_data=auth_data,
                endpoints=endpoints,
                environment=legacy_config.get("environment", "development"),
                category=legacy_config.get("category", "migrated"),
                tags=legacy_config.get("tags", ["migrated"])
            )
            
            if success:
                logger.info(f"Successfully migrated legacy configuration: {name}")
                return admin_config
            else:
                logger.error(f"Failed to migrate legacy configuration '{name}': {message}")
                return None
            
        except Exception as e:
            logger.error(f"Failed to migrate legacy configuration: {e}")
            return None
    
    async def export_configuration(self, api_name: str) -> Optional[Dict[str, Any]]:
        """
        Export an API configuration for backup or transfer
        
        Args:
            api_name: Name of the API to export
            
        Returns:
            Exported configuration data or None if export failed
        """
        try:
            admin_config = await self.admin_storage.get_api_config(api_name)
            if not admin_config:
                return None
            
            shared_config = admin_config.to_shared_config()
            
            export_data = {
                "format_version": "1.0",
                "export_timestamp": datetime.utcnow().isoformat(),
                "admin_config": {
                    "name": admin_config.name,
                    "display_name": admin_config.display_name,
                    "description": admin_config.description,
                    "environment": admin_config.environment,
                    "category": admin_config.category,
                    "tags": admin_config.tags,
                    "enabled": admin_config.enabled
                },
                "shared_config": shared_config.to_dict()
            }
            
            logger.info(f"Exported configuration for API: {api_name}")
            return export_data
            
        except Exception as e:
            logger.error(f"Failed to export configuration for '{api_name}': {e}")
            return None
    
    async def import_configuration(
        self,
        import_data: Dict[str, Any],
        imported_by: str,
        overwrite: bool = False
    ) -> Tuple[bool, str]:
        """
        Import an API configuration from exported data
        
        Args:
            import_data: Exported configuration data
            imported_by: User ID who initiated the import
            overwrite: Whether to overwrite existing configurations
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Validate import data format
            if import_data.get("format_version") != "1.0":
                return False, "Unsupported import format version"
            
            admin_data = import_data.get("admin_config", {})
            shared_data = import_data.get("shared_config", {})
            
            if not admin_data or not shared_data:
                return False, "Invalid import data structure"
            
            api_name = admin_data.get("name")
            if not api_name:
                return False, "Missing API name in import data"
            
            # Check if configuration already exists
            existing = await self.admin_storage.get_api_config(api_name)
            if existing and not overwrite:
                return False, f"API configuration '{api_name}' already exists. Use overwrite option to replace."
            
            # Create shared configuration
            shared_config = APIConfiguration.from_dict(shared_data)
            
            # Create or update admin configuration
            if existing and overwrite:
                # Update existing configuration
                success = await self.admin_storage.update_api_config(
                    name=api_name,
                    shared_config=shared_config,
                    updated_by=imported_by,
                    changes_description="Imported configuration"
                )
                
                if success:
                    return True, f"Successfully updated API configuration '{api_name}' from import"
                else:
                    return False, f"Failed to update API configuration '{api_name}'"
            else:
                # Create new configuration
                admin_config = await self.admin_storage.create_api_config(
                    shared_config=shared_config,
                    created_by=imported_by,
                    display_name=admin_data.get("display_name", api_name),
                    description=f"Imported: {admin_data.get('description', '')}",
                    environment=admin_data.get("environment", "development"),
                    category=admin_data.get("category", "imported"),
                    tags=admin_data.get("tags", []) + ["imported"],
                    enabled=admin_data.get("enabled", True)
                )
                
                return True, f"Successfully imported API configuration '{api_name}'"
            
        except Exception as e:
            logger.error(f"Failed to import configuration: {e}")
            return False, f"Import failed: {str(e)}"
    
    async def _create_default_configurations(self) -> None:
        """Create default API configurations for demonstration"""
        try:
            # Create API v1 configuration
            api_v1_config = create_api_v1_configuration(
                base_url="https://ronaldo-club.to/api",
                login_token="demo-token"
            )
            
            await self.admin_storage.create_api_config(
                shared_config=api_v1_config,
                created_by="system",
                display_name="API v1 - External Cart API",
                description="Legacy API v1 configuration for cart operations and user management",
                environment="development",
                category="legacy",
                tags=["api_v1", "cart", "legacy"]
            )
            
            # Create API v2 configuration
            api_v2_config = create_api_v2_configuration()

            await self.admin_storage.create_api_config(
                shared_config=api_v2_config,
                created_by="system",
                display_name="API v2 - BASE 2 (VHQ)",
                description="Secondary BASE 2 browse API leveraging the shared infrastructure",
                environment="development",
                category="bin_cards",
                tags=["api_v2", "base2", "bin", "vhq"],
            )

            # Create example new API configuration
            new_api_config = create_new_api_configuration(
                api_name="example_api",
                base_url="https://api.example.com",
                api_key="demo-api-key"
            )
            
            await self.admin_storage.create_api_config(
                shared_config=new_api_config,
                created_by="system",
                display_name="Example API",
                description="Example API configuration demonstrating the shared API system",
                environment="development",
                category="example",
                tags=["example", "demo"]
            )
            
            logger.info("Created default API configurations")
            
        except Exception as e:
            logger.error(f"Failed to create default configurations: {e}")

    async def _ensure_base_configs(self, admin_configs):
        """Ensure that baseline API configurations (api1, api2) exist in admin storage."""
        try:
            existing_names = {cfg.name.lower() for cfg in admin_configs}
            existing_shared_names = {
                (cfg.shared_config.get("name") or "").lower()
                for cfg in admin_configs
            }
            created: list = []

            if "api1" not in existing_names and "api1" not in existing_shared_names:
                api_v1_config = create_api_v1_configuration(
                    base_url="https://ronaldo-club.to/api",
                    login_token="demo-token",
                )
                created.append(
                    await self.admin_storage.create_api_config(
                        shared_config=api_v1_config,
                        created_by="system",
                        display_name="API v1 - External Cart API",
                        description="Legacy API v1 configuration for cart operations and user management",
                        environment="development",
                        category="legacy",
                        tags=["api_v1", "cart", "legacy"],
                    )
                )

            if "api2" not in existing_names and "api2" not in existing_shared_names:
                api_v2_config = create_api_v2_configuration()
                created.append(
                    await self.admin_storage.create_api_config(
                        shared_config=api_v2_config,
                        created_by="system",
                        display_name="API v2 - BASE 2 (VHQ)",
                        description="Secondary BASE 2 browse API leveraging the shared infrastructure",
                        environment="development",
                        category="bin_cards",
                        tags=["api_v2", "base2", "bin", "vhq"],
                    )
                )

            if created:
                admin_configs = admin_configs + created

            return admin_configs

        except Exception as e:
            logger.error(f"Failed to ensure base configurations: {e}")
            return admin_configs
    
    async def get_registry_status(self) -> Dict[str, Any]:
        """
        Get status information about the shared API registry
        
        Returns:
            Dictionary with registry status information
        """
        try:
            # Get registered APIs from the registry
            registered_apis = api_registry.list_apis()
            
            # Get admin configurations
            admin_configs = await self.admin_storage.list_api_configs()
            
            # Calculate statistics
            total_admin_configs = len(admin_configs)
            enabled_admin_configs = len([c for c in admin_configs if c.enabled])
            total_registered_apis = len(registered_apis)
            
            # Check for sync issues
            admin_names = {c.name for c in admin_configs if c.enabled}
            registry_names = {api["name"] for api in registered_apis}
            
            missing_in_registry = admin_names - registry_names
            missing_in_admin = registry_names - admin_names
            
            return {
                "initialized": self._initialized,
                "total_admin_configs": total_admin_configs,
                "enabled_admin_configs": enabled_admin_configs,
                "total_registered_apis": total_registered_apis,
                "sync_issues": {
                    "missing_in_registry": list(missing_in_registry),
                    "missing_in_admin": list(missing_in_admin)
                },
                "registry_apis": registered_apis
            }
            
        except Exception as e:
            logger.error(f"Failed to get registry status: {e}")
            return {
                "initialized": False,
                "error": str(e)
            }


# Global service instance
_integration_service = None


def get_shared_api_integration_service() -> SharedAPIIntegrationService:
    """Get the global shared API integration service instance"""
    global _integration_service
    if _integration_service is None:
        _integration_service = SharedAPIIntegrationService()
    return _integration_service


async def initialize_shared_api_integration() -> bool:
    """Initialize the shared API integration service"""
    service = get_shared_api_integration_service()
    return await service.initialize_integration()
