#!/usr/bin/env python3
"""
Migration Script for Legacy API Configurations

This script migrates existing API configurations from the legacy system
to the new shared API management system.
"""

import asyncio
import logging
import json
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from admin.services.shared_api_integration import get_shared_api_integration_service
from admin.services.shared_api_admin_service import get_shared_api_admin_service
from admin.models.api_config_storage import get_admin_api_service
from api_v1.services.api_config import UnifiedAPIConfigurationService

logger = logging.getLogger(__name__)


class LegacyAPIMigrator:
    """
    Migrator for legacy API configurations
    
    This class handles the migration of existing API configurations
    from the legacy system to the new shared API management system.
    """
    
    def __init__(self):
        self.integration_service = get_shared_api_integration_service()
        self.admin_service = get_shared_api_admin_service()
        self.admin_storage = get_admin_api_service()
        self.legacy_service = UnifiedAPIConfigurationService()
        
        self.migration_log = []
        self.migrated_count = 0
        self.failed_count = 0
    
    async def migrate_all_configurations(
        self,
        dry_run: bool = False,
        backup_file: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Migrate all legacy API configurations
        
        Args:
            dry_run: If True, only analyze what would be migrated
            backup_file: Path to save backup of legacy configurations
            
        Returns:
            Migration results summary
        """
        logger.info("Starting legacy API configuration migration...")
        
        try:
            # Get legacy configurations
            legacy_configs = await self._get_legacy_configurations()
            
            if not legacy_configs:
                logger.info("No legacy configurations found to migrate")
                return {
                    "status": "completed",
                    "total_configs": 0,
                    "migrated": 0,
                    "failed": 0,
                    "message": "No legacy configurations found"
                }
            
            logger.info(f"Found {len(legacy_configs)} legacy configurations")
            
            # Create backup if requested
            if backup_file:
                await self._create_backup(legacy_configs, backup_file)
                logger.info(f"Created backup at: {backup_file}")
            
            # Migrate each configuration
            migration_results = []
            
            for config_name, config_data in legacy_configs.items():
                try:
                    result = await self._migrate_single_configuration(
                        config_name,
                        config_data,
                        dry_run=dry_run
                    )
                    migration_results.append(result)
                    
                    if result["success"]:
                        self.migrated_count += 1
                        logger.info(f"✅ Migrated: {config_name}")
                    else:
                        self.failed_count += 1
                        logger.error(f"❌ Failed: {config_name} - {result['error']}")
                    
                except Exception as e:
                    self.failed_count += 1
                    error_msg = f"Migration failed for '{config_name}': {str(e)}"
                    logger.error(error_msg)
                    migration_results.append({
                        "config_name": config_name,
                        "success": False,
                        "error": error_msg
                    })
            
            # Generate summary
            summary = {
                "status": "completed",
                "dry_run": dry_run,
                "total_configs": len(legacy_configs),
                "migrated": self.migrated_count,
                "failed": self.failed_count,
                "migration_results": migration_results,
                "migration_log": self.migration_log,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            if dry_run:
                logger.info(f"DRY RUN: Would migrate {self.migrated_count}/{len(legacy_configs)} configurations")
            else:
                logger.info(f"Migration completed: {self.migrated_count}/{len(legacy_configs)} configurations migrated")
            
            return summary
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "migrated": self.migrated_count,
                "failed": self.failed_count
            }
    
    async def _get_legacy_configurations(self) -> Dict[str, Dict[str, Any]]:
        """Get all legacy API configurations"""
        try:
            # Get configurations from legacy service
            legacy_configs = {}
            
            # Try to get external API service configuration
            try:
                external_config = self.legacy_service.get_external_api_service()
                if external_config:
                    legacy_configs["external_api"] = {
                        "service_name": "external_api",
                        "base_url": external_config.base_url,
                        "endpoints": {
                            "login": external_config.endpoints.get("login", "/login"),
                            "cart_add": external_config.endpoints.get("cart_add", "/cart/add"),
                            "cart_remove": external_config.endpoints.get("cart_remove", "/cart/remove"),
                            "cart_list": external_config.endpoints.get("cart_list", "/cart/list"),
                            "user_info": external_config.endpoints.get("user_info", "/user/info")
                        },
                        "credentials": {
                            "login_token": getattr(external_config, "login_token", None)
                        },
                        "description": "Legacy external API service for cart operations",
                        "environment": "production",
                        "category": "legacy"
                    }
            except Exception as e:
                logger.warning(f"Could not get external API configuration: {e}")
            
            # Try to get other legacy configurations from files or database
            legacy_config_files = [
                "config/api_config.json",
                "api_v1/config.json",
                "legacy_api_configs.json"
            ]
            
            for config_file in legacy_config_files:
                try:
                    config_path = Path(config_file)
                    if config_path.exists():
                        with open(config_path, 'r') as f:
                            file_configs = json.load(f)
                            
                        if isinstance(file_configs, dict):
                            for name, config in file_configs.items():
                                if isinstance(config, dict) and "base_url" in config:
                                    legacy_configs[name] = config
                                    
                except Exception as e:
                    logger.debug(f"Could not read config file {config_file}: {e}")
            
            return legacy_configs
            
        except Exception as e:
            logger.error(f"Failed to get legacy configurations: {e}")
            return {}
    
    async def _migrate_single_configuration(
        self,
        config_name: str,
        config_data: Dict[str, Any],
        dry_run: bool = False
    ) -> Dict[str, Any]:
        """Migrate a single configuration"""
        try:
            # Check if already migrated
            existing = await self.admin_storage.get_api_config(config_name)
            if existing:
                return {
                    "config_name": config_name,
                    "success": False,
                    "error": "Configuration already exists in new system",
                    "action": "skipped"
                }
            
            # Validate legacy configuration
            if not config_data.get("base_url"):
                return {
                    "config_name": config_name,
                    "success": False,
                    "error": "Missing base_url in legacy configuration",
                    "action": "validation_failed"
                }
            
            if dry_run:
                # Just validate and return what would be done
                return {
                    "config_name": config_name,
                    "success": True,
                    "action": "would_migrate",
                    "legacy_data": config_data
                }
            
            # Perform the migration
            admin_config = await self.integration_service.migrate_legacy_configuration(
                legacy_config=config_data,
                created_by="migration_script"
            )
            
            if admin_config:
                self.migration_log.append({
                    "timestamp": datetime.utcnow().isoformat(),
                    "action": "migrated",
                    "config_name": config_name,
                    "new_id": admin_config.id
                })
                
                return {
                    "config_name": config_name,
                    "success": True,
                    "action": "migrated",
                    "new_config_id": admin_config.id
                }
            else:
                return {
                    "config_name": config_name,
                    "success": False,
                    "error": "Migration service returned None",
                    "action": "migration_failed"
                }
            
        except Exception as e:
            return {
                "config_name": config_name,
                "success": False,
                "error": str(e),
                "action": "exception"
            }
    
    async def _create_backup(
        self,
        legacy_configs: Dict[str, Dict[str, Any]],
        backup_file: str
    ) -> None:
        """Create backup of legacy configurations"""
        try:
            backup_data = {
                "backup_timestamp": datetime.utcnow().isoformat(),
                "backup_version": "1.0",
                "total_configs": len(legacy_configs),
                "configurations": legacy_configs
            }
            
            backup_path = Path(backup_file)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(backup_path, 'w') as f:
                json.dump(backup_data, f, indent=2, default=str)
            
            logger.info(f"Created backup with {len(legacy_configs)} configurations")
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            raise


async def main():
    """Main migration function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrate legacy API configurations")
    parser.add_argument("--dry-run", action="store_true", help="Perform dry run without making changes")
    parser.add_argument("--backup", type=str, help="Path to save backup file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Initialize migrator
    migrator = LegacyAPIMigrator()
    
    # Set default backup file if not provided
    backup_file = args.backup
    if not backup_file and not args.dry_run:
        backup_file = f"backups/legacy_api_configs_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        # Run migration
        results = await migrator.migrate_all_configurations(
            dry_run=args.dry_run,
            backup_file=backup_file
        )
        
        # Print results
        print("\n" + "="*60)
        print("MIGRATION RESULTS")
        print("="*60)
        print(f"Status: {results['status']}")
        print(f"Total configurations: {results['total_configs']}")
        print(f"Successfully migrated: {results['migrated']}")
        print(f"Failed migrations: {results['failed']}")
        
        if args.dry_run:
            print("\n⚠️  This was a DRY RUN - no changes were made")
        
        if results.get('migration_results'):
            print("\nDetailed Results:")
            for result in results['migration_results']:
                status = "✅" if result['success'] else "❌"
                print(f"  {status} {result['config_name']}: {result.get('action', 'unknown')}")
                if not result['success']:
                    print(f"      Error: {result.get('error', 'Unknown error')}")
        
        # Save detailed results
        results_file = f"migration_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nDetailed results saved to: {results_file}")
        
        return 0 if results['status'] == 'completed' else 1
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        print(f"\n❌ Migration failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
