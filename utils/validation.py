"""
Input validation utilities for the Demo Wallet Bot
"""

from __future__ import annotations

import re
from typing import Optional, Union


class ValidationError(Exception):
    """Custom validation error"""

    pass


def validate_amount(
    amount: Union[str, float], min_amount: float = 0.01, max_amount: float = 10000.0
) -> float:
    """
    Validate and sanitize monetary amounts

    Args:
        amount: Amount to validate (string or float)
        min_amount: Minimum allowed amount
        max_amount: Maximum allowed amount

    Returns:
        Validated and rounded amount

    Raises:
        ValidationError: If amount is invalid
    """
    try:
        if isinstance(amount, str):
            # Remove currency symbols and whitespace, but preserve negative sign
            cleaned = re.sub(r"[^\d.\-]", "", amount.strip())
            if not cleaned:
                raise ValidationError("Amount cannot be empty")
            amount = float(cleaned)

        if not isinstance(amount, (int, float)):
            raise ValidationError("Amount must be a number")

        if amount <= 0:
            raise ValidationError("Amount must be positive")
        if amount < min_amount:
            raise ValidationError(f"Amount must be at least ${min_amount:.2f}")

        if amount > max_amount:
            raise ValidationError(f"Amount cannot exceed ${max_amount:.2f}")

        return round(float(amount), 2)

    except ValueError:
        raise ValidationError("Invalid amount format")


def validate_telegram_id(telegram_id: Union[str, int]) -> int:
    """
    Validate Telegram user ID

    Args:
        telegram_id: Telegram ID to validate

    Returns:
        Validated Telegram ID

    Raises:
        ValidationError: If ID is invalid
    """
    try:
        if isinstance(telegram_id, str):
            telegram_id = int(telegram_id.strip())

        if not isinstance(telegram_id, int):
            raise ValidationError("Telegram ID must be an integer")

        if telegram_id <= 0:
            raise ValidationError("Telegram ID must be positive")

        # Telegram IDs are typically 9-10 digits
        if telegram_id < 100000000 or telegram_id > 9999999999:
            raise ValidationError("Invalid Telegram ID format")

        return telegram_id

    except ValueError:
        raise ValidationError("Invalid Telegram ID format")


def validate_currency_code(currency: str) -> str:
    """
    Validate currency code

    Args:
        currency: Currency code to validate

    Returns:
        Validated currency code in uppercase

    Raises:
        ValidationError: If currency code is invalid
    """
    if not currency or not isinstance(currency, str):
        raise ValidationError("Currency code cannot be empty")

    currency = currency.strip().upper()

    if len(currency) != 3:
        raise ValidationError("Currency code must be 3 characters")

    if not currency.isalpha():
        raise ValidationError("Currency code must contain only letters")

    # List of common currency codes for validation
    valid_currencies = {
        "USD",
        "EUR",
        "GBP",
        "JPY",
        "AUD",
        "CAD",
        "CHF",
        "CNY",
        "SEK",
        "NZD",
        "MXN",
        "SGD",
        "HKD",
        "NOK",
        "TRY",
        "RUB",
        "INR",
        "BRL",
        "ZAR",
        "KRW",
    }

    if currency not in valid_currencies:
        raise ValidationError(f"Unsupported currency code: {currency}")

    return currency


def validate_user_role(role: str) -> str:
    """
    Validate user role

    Args:
        role: Role to validate

    Returns:
        Validated role

    Raises:
        ValidationError: If role is invalid
    """
    if not role or not isinstance(role, str):
        raise ValidationError("Role cannot be empty")

    role = role.strip().lower()
    valid_roles = {"user", "admin", "moderator"}

    if role not in valid_roles:
        raise ValidationError(f"Role must be one of: {', '.join(valid_roles)}")

    return role


def sanitize_text_input(text: str, max_length: int = 256) -> str:
    """
    Sanitize text input to prevent injection attacks

    Args:
        text: Text to sanitize
        max_length: Maximum allowed length

    Returns:
        Sanitized text

    Raises:
        ValidationError: If text is invalid
    """
    if not isinstance(text, str):
        raise ValidationError("Input must be text")

    # Remove potentially dangerous characters
    text = text.strip()

    if len(text) > max_length:
        raise ValidationError(f"Text too long (max {max_length} characters)")

    # Remove HTML tags and script content for security
    text = re.sub(r"<[^>]*>", "", text)
    text = re.sub(r"javascript:", "", text, flags=re.IGNORECASE)

    return text


def validate_country_code(country: Optional[str]) -> Optional[str]:
    """
    Validate ISO country code

    Args:
        country: Country code to validate

    Returns:
        Validated country code in uppercase or None

    Raises:
        ValidationError: If country code is invalid
    """
    if not country:
        return None

    if not isinstance(country, str):
        raise ValidationError("Country code must be text")

    country = country.strip().upper()

    if len(country) != 2:
        raise ValidationError("Country code must be 2 characters")

    if not country.isalpha():
        raise ValidationError("Country code must contain only letters")

    return country


def validate_pagination_params(
    page: Union[str, int], per_page: Union[str, int] = 10
) -> tuple[int, int]:
    """
    Validate pagination parameters

    Args:
        page: Page number
        per_page: Items per page

    Returns:
        Tuple of (page, per_page)

    Raises:
        ValidationError: If parameters are invalid
    """
    try:
        page = int(page) if isinstance(page, str) else page
        per_page = int(per_page) if isinstance(per_page, str) else per_page

        if page < 1:
            raise ValidationError("Page number must be at least 1")

        if per_page < 1 or per_page > 100:
            raise ValidationError("Items per page must be between 1 and 100")

        return page, per_page

    except ValueError:
        raise ValidationError("Invalid pagination parameters")


def validate_callback_data(
    callback_data: str, expected_prefix: str = None
) -> list[str]:
    """
    Validate and parse callback data

    Args:
        callback_data: Callback data to validate
        expected_prefix: Expected prefix for the callback

    Returns:
        List of callback data parts

    Raises:
        ValidationError: If callback data is invalid
    """
    if not callback_data or not isinstance(callback_data, str):
        raise ValidationError("Callback data cannot be empty")

    callback_data = callback_data.strip()

    if len(callback_data) > 64:  # Telegram limit
        raise ValidationError("Callback data too long")

    parts = callback_data.split(":")

    if expected_prefix and (not parts or parts[0] != expected_prefix):
        raise ValidationError(f"Invalid callback prefix, expected: {expected_prefix}")

    return parts


def validate_search_query(query: str) -> str:
    """
    Validate search query input

    Args:
        query: Search query to validate

    Returns:
        Sanitized search query

    Raises:
        ValidationError: If query is invalid
    """
    if not query or not isinstance(query, str):
        raise ValidationError("Search query cannot be empty")

    query = query.strip()

    if len(query) < 2:
        raise ValidationError("Search query must be at least 2 characters")

    if len(query) > 100:
        raise ValidationError("Search query too long (max 100 characters)")

    # Remove potentially dangerous characters but allow basic search terms
    query = re.sub(r"[<>\"'&]", "", query)

    return query
