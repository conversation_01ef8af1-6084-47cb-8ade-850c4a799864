"""
Enhanced logging utilities with colors, categories, and structured output.

Features:
- Colorized console logs with clear level differentiation
- Structured formatting: timestamp, level, module, category, message
- Categories auto-detected by module or provided via extra["category"]
- Environment-aware levels (development vs production)
- Optional rotating file handler for log management
- Security filter to redact sensitive values
"""

from __future__ import annotations

import logging
import logging.handlers
import re
import sys
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # Optional: better Windows ANSI color support
    from colorama import init as colorama_init
except Exception:  # pragma: no cover - optional dependency
    colorama_init = None


class SecurityFilter(logging.Filter):
    """Filter to remove sensitive information from logs"""

    SENSITIVE_PATTERNS = [
        (re.compile(r"\b\d{10}:\w{35}\b"), "[BOT_TOKEN_REDACTED]"),  # Bot tokens
        (re.compile(r"\b\d{13,19}\b"), "[PAN_REDACTED]"),  # Credit card numbers
        (re.compile(r"\b\d{3}-\d{2}-\d{4}\b"), "[SSN_REDACTED]"),  # SSN
        (
            re.compile(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"),
            "[EMAIL_REDACTED]",
        ),  # Email
        (
            re.compile(r'\bpassword["\']?\s*[:=]\s*["\']?[^\s"\']+', re.IGNORECASE),
            "password=[REDACTED]",
        ),  # Passwords
        (
            re.compile(r'\btoken["\']?\s*[:=]\s*["\']?[^\s"\']+', re.IGNORECASE),
            "token=[REDACTED]",
        ),  # Tokens
        (
            re.compile(r'\bapi_key["\']?\s*[:=]\s*["\']?[^\s"\']+', re.IGNORECASE),
            "api_key=[REDACTED]",
        ),  # API keys
    ]

    def filter(self, record: logging.LogRecord) -> bool:
        """Filter sensitive information from log records"""
        if hasattr(record, "msg") and record.msg:
            message = str(record.msg)
            for pattern, replacement in self.SENSITIVE_PATTERNS:
                message = pattern.sub(replacement, message)
            record.msg = message

        # Also filter args if present
        if hasattr(record, "args") and record.args:
            filtered_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    for pattern, replacement in self.SENSITIVE_PATTERNS:
                        arg = pattern.sub(replacement, arg)
                filtered_args.append(arg)
            record.args = tuple(filtered_args)

        return True


class StructuredFormatter(logging.Formatter):
    """Structured logging formatter with level coloring, categories, and separators."""

    # ANSI color escapes
    COLORS = {
        "RESET": "\033[0m",
        "DIM": "\033[2m",
        "BOLD": "\033[1m",
        "DEBUG": "\033[36m",  # Cyan
        "INFO": "\033[32m",  # Green
        "WARNING": "\033[33m",  # Yellow
        "ERROR": "\033[31m",  # Red
        "CRITICAL": "\033[35m",  # Magenta
        # Category accents
        "API": "\033[94m",  # Bright blue
        "DB": "\033[96m",  # Bright cyan
        "USER": "\033[92m",  # Bright green
        "ERROR_CAT": "\033[91m",
        "PERF": "\033[95m",
        "SYSTEM": "\033[90m",  # Gray
        "GENERAL": "\033[37m",
    }

    HIDE_COLOR = False  # toggled at runtime if ANSI unsupported

    def __init__(
        self,
        include_extra: bool = True,
        colored: bool = True,
        datefmt: str = "%Y-%m-%d %H:%M:%S",
        show_category: bool = False,
    ):
        super().__init__(datefmt=datefmt)
        self.include_extra = include_extra
        self.colored = colored
        self._last_level_name: str | None = None
        self.show_category = show_category
        # Initialize color support once (avoid atexit noise)
        if self.colored and colorama_init is not None:
            try:
                colorama_init(autoreset=True)
            except Exception:
                self.HIDE_COLOR = True

    def _supports_color(self) -> bool:
        if self.HIDE_COLOR:
            return False
        try:
            return sys.stdout.isatty()
        except Exception:
            return False

    @staticmethod
    def _derive_category(record: logging.LogRecord) -> str:
        # Explicit category on record takes precedence
        cat = getattr(record, "category", None)
        if isinstance(cat, str) and cat:
            return cat.upper()

        name = record.name or ""
        if name.startswith("database"):
            return "DATABASE"
        if name.startswith("handlers") or name.startswith("middleware"):
            return "USER"
        if name.startswith("services"):
            return "API"
        if name.startswith("utils.performance"):
            return "PERFORMANCE"
        if name.startswith("config") or name.startswith("main"):
            return "SYSTEM"
        if record.levelno >= logging.ERROR:
            return "ERROR"
        return "GENERAL"

    def _colorize(self, text: str, color_key: str) -> str:
        if not self.colored or not self._supports_color():
            return text
        color = self.COLORS.get(color_key)
        reset = self.COLORS["RESET"]
        return f"{color}{text}{reset}" if color else text

    def _separator_line(self, prev_level: str, new_level: str) -> str:
        # Build a subtle, non-intrusive separator line
        label = f" level change: {prev_level} -> {new_level} "
        total = 80
        try:
            import shutil

            cols = shutil.get_terminal_size().columns
            if cols and cols >= 40:
                total = min(120, max(60, cols))
        except Exception:
            pass

        dashes = max(4, (total - len(label)) // 2)
        line = f"{'-' * dashes}{label}{'-' * dashes}"
        # Ensure at least total length; if odd diff, add one more dash to the end
        if len(line) < total:
            line += "-" * (total - len(line))

        # Use dim gray for console; plain in files
        return self._colorize(line, "SYSTEM") if self.colored else line

    def format(self, record: logging.LogRecord) -> str:
        # Timestamp
        if not hasattr(record, "asctime"):
            record.asctime = self.formatTime(record)

        category = self._derive_category(record)
        level = record.levelname
        name = record.name

        # Compose header
        ts = record.asctime
        # No padding to avoid extra spaces in level display
        lvl = self._colorize(level, level)

        message = record.getMessage()

        if self.show_category:
            # Category coloring mapping
            category_color_key = {
                "API": "API",
                "DATABASE": "DB",
                "USER": "USER",
                "ERROR": "ERROR_CAT",
                "PERFORMANCE": "PERF",
                "SYSTEM": "SYSTEM",
                "GENERAL": "GENERAL",
            }.get(category, "GENERAL")
            cat = self._colorize(category, category_color_key)
            formatted = f"{ts} [{lvl}] {name} <{cat}>: {message}"
        else:
            formatted = f"{ts} [{lvl}] {name}: {message}"

        # Include selected extras
        if self.include_extra and hasattr(record, "__dict__"):
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in [
                    "name",
                    "msg",
                    "args",
                    "levelname",
                    "levelno",
                    "pathname",
                    "filename",
                    "module",
                    "lineno",
                    "funcName",
                    "created",
                    "msecs",
                    "relativeCreated",
                    "thread",
                    "threadName",
                    "processName",
                    "process",
                    "getMessage",
                    "exc_info",
                    "exc_text",
                    "stack_info",
                    "asctime",
                    "category",
                ]:
                    extra_fields[key] = value

            if extra_fields:
                extra_str = " | ".join(f"{k}={v}" for k, v in extra_fields.items())
                formatted += f" | {extra_str}"

        # Exception info
        if record.exc_info:
            formatted += f"\n{self.formatException(record.exc_info)}"

        # Insert separator if level changed since last record
        if self._last_level_name is not None and self._last_level_name != level:
            sep = self._separator_line(self._last_level_name, level)
            output = f"{sep}\n{formatted}"
        else:
            output = formatted

        self._last_level_name = level
        return output


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    enable_security_filter: bool = True,
    structured_format: bool = True,
    environment: str | None = None,
    console_colored: bool = True,
    show_category: bool = False,
    enable_audit_logging: bool = True,
    security_log_file: Optional[str] = None,
) -> None:
    """
    Setup comprehensive logging configuration

    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
        max_file_size: Maximum log file size in bytes
        backup_count: Number of backup log files to keep
        enable_security_filter: Whether to enable security filtering
        structured_format: Whether to use structured formatting
    """
    # Convert level string to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)

    # Environment-based defaults
    env = (environment or "").lower()
    if env in {"dev", "development"}:
        console_level = logging.DEBUG
    elif env in {"prod", "production"}:
        console_level = logging.INFO
    else:
        console_level = numeric_level

    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)

    # Clear existing handlers
    root_logger.handlers.clear()

    # Create formatter
    if structured_format:
        formatter = StructuredFormatter(
            colored=console_colored, show_category=show_category
        )
    else:
        formatter = logging.Formatter(
            "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(console_level)
    console_handler.setFormatter(formatter)

    if enable_security_filter:
        console_handler.addFilter(SecurityFilter())

    root_logger.addHandler(console_handler)

    # File handler (if specified)
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=max_file_size, backupCount=backup_count, encoding="utf-8"
        )
        # File handler uses non-colored structured formatter
        if structured_format:
            file_formatter = StructuredFormatter(
                colored=False, show_category=show_category
            )
        else:
            file_formatter = logging.Formatter(
                "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S",
            )

        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(file_formatter)

        if enable_security_filter:
            file_handler.addFilter(SecurityFilter())

        root_logger.addHandler(file_handler)

    # Set specific logger levels
    logging.getLogger("aiogram").setLevel(logging.WARNING)
    logging.getLogger("motor").setLevel(logging.WARNING)
    logging.getLogger("pymongo").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)

    # Log setup completion
    logger = logging.getLogger(__name__)
    logger.info(
        f"Logging setup completed - Level={level}, Env={environment or 'default'}, File={log_file or 'None'}"
    )


def get_logger(name: str, **extra_context) -> logging.LoggerAdapter:
    """
    Get a logger with optional extra context

    Args:
        name: Logger name
        **extra_context: Additional context to include in log messages

    Returns:
        LoggerAdapter with extra context
    """
    logger = logging.getLogger(name)
    if extra_context:
        return logging.LoggerAdapter(logger, extra_context)
    return logging.LoggerAdapter(logger, {})


class LogContext:
    """Context manager for adding temporary context to logs"""

    def __init__(self, logger: logging.Logger, **context):
        self.logger = logger
        self.context = context
        self.original_extra = getattr(logger, "_extra_context", {})

    def __enter__(self):
        # Add context to logger
        if not hasattr(self.logger, "_extra_context"):
            self.logger._extra_context = {}
        self.logger._extra_context.update(self.context)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore original context
        self.logger._extra_context = self.original_extra


def setup_security_logging(security_log_file: str) -> None:
    """Setup dedicated security audit logging"""
    import os
    from pathlib import Path

    # Ensure security log directory exists
    log_dir = Path(security_log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)

    # Create security logger
    security_logger = logging.getLogger("security")
    security_logger.setLevel(logging.INFO)

    # Create security log handler with rotation
    from logging.handlers import RotatingFileHandler

    security_handler = RotatingFileHandler(
        security_log_file,
        maxBytes=50 * 1024 * 1024,  # 50MB
        backupCount=10,
        encoding="utf-8",
    )

    # Security log format (structured for analysis)
    security_formatter = logging.Formatter(
        '{"timestamp": "%(asctime)s", "level": "%(levelname)s", '
        '"event_type": "%(event_type)s", "user_id": "%(user_id)s", '
        '"action": "%(action)s", "result": "%(result)s", '
        '"details": "%(message)s", "source_ip": "%(source_ip)s"}'
    )
    security_handler.setFormatter(security_formatter)
    security_logger.addHandler(security_handler)

    # Prevent propagation to root logger
    security_logger.propagate = False


def log_security_event(
    event_type: str,
    user_id: Optional[int] = None,
    action: str = "",
    result: str = "success",
    details: str = "",
    source_ip: Optional[str] = None,
    level: str = "INFO",
) -> None:
    """Log security-related events"""
    security_logger = logging.getLogger("security")

    # Create log record with security context
    extra = {
        "event_type": event_type,
        "user_id": user_id or "unknown",
        "action": action,
        "result": result,
        "source_ip": source_ip or "unknown",
    }

    # Log at appropriate level
    log_level = getattr(logging, level.upper(), logging.INFO)
    security_logger.log(log_level, details, extra=extra)
