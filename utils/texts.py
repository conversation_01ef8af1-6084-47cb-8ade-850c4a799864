"""
Text messages and templates for the bot
"""

from __future__ import annotations

# Demo watermark
DEMO_WATERMARK = "\n\n<i>🧪 This is a demo bot for testing purposes</i>"

# Welcome messages
WELCOME_MESSAGE = """
👋 <b>Welcome to Demo Wallet Bot, {first_name}!</b>

This is a demonstration bot that simulates a digital wallet and card purchase system.

<b>Features:</b>
• 💼 Virtual wallet with balance management
• 🔎 Browse and purchase demo cards
• 📜 Transaction history tracking
• 📄 Advanced filtering and search
• 🛡️ Compliance and security checks

<b>Getting Started:</b>
1. Check your wallet balance
2. Add funds if needed
3. Browse available cards
4. Make your first purchase!

Your initial balance: <b>$100.00</b>
"""

# Error messages
ERROR_GENERIC = "❌ An error occurred. Please try again later."
ERROR_INSUFFICIENT_FUNDS = "❌ Insufficient funds. Please add money to your wallet."
ERROR_INVALID_AMOUNT = "❌ Invalid amount. Please enter a positive number."
ERROR_USER_NOT_FOUND = "❌ User not found. Please start the bot with /start."
ERROR_WALLET_LOCKED = "❌ Your wallet is temporarily locked. Contact support."
ERROR_RATE_LIMITED = "⏳ Too many requests. Please wait a moment and try again."
ERROR_COMPLIANCE_BLOCKED = "🛑 Transaction blocked by compliance checks."
ERROR_ITEM_NOT_FOUND = "❌ Item not found or no longer available."
ERROR_PURCHASE_FAILED = "❌ Purchase failed. Please try again."
ERROR_DATABASE_CONNECTION = (
    "❌ Database connection issue. Please try again in a moment."
)
ERROR_VALIDATION_FAILED = "❌ Invalid input. Please check your data and try again."
ERROR_PERMISSION_DENIED = "❌ You don't have permission to perform this action."
ERROR_SERVICE_UNAVAILABLE = (
    "❌ Service temporarily unavailable. Please try again later."
)
ERROR_TIMEOUT = "⏱️ Request timed out. Please try again."

# Success messages
SUCCESS_FUNDS_ADDED = "✅ <b>${amount:.2f}</b> added to your wallet!\n\nNew balance: <b>${balance:.2f}</b>"
SUCCESS_PURCHASE = "✅ <b>Purchase Successful!</b>\n\nItem: {sku}\nPrice: ${price:.2f}\nNew balance: ${balance:.2f}"
SUCCESS_FILTERS_APPLIED = "✅ Filters applied successfully!"
SUCCESS_FILTERS_CLEARED = "✅ All filters cleared!"

# Info messages
INFO_WALLET_BALANCE = "💼 <b>Wallet Balance</b>\n\nCurrent balance: <b>${balance:.2f}</b>\nCurrency: {currency}"
INFO_NO_HISTORY = "📜 <b>Transaction History</b>\n\nNo transactions found."
INFO_NO_RESULTS = "📄 <b>Search Results</b>\n\nNo items found matching your criteria."
INFO_LOADING = "⏳ Loading..."
INFO_PROCESSING = "⚙️ Processing your request..."

# Admin messages
ADMIN_STATS_TEMPLATE = """
📊 <b>Bot Statistics</b>

<b>Users:</b>
• Total users: {total_users}
• Active today: {active_today}
• New this week: {new_week}

<b>Wallets:</b>
• Total balance: ${total_balance:.2f}
• Average balance: ${avg_balance:.2f}

<b>Transactions:</b>
• Total transactions: {total_transactions}
• Today: {transactions_today}
• Total volume: ${total_volume:.2f}

<b>Purchases:</b>
• Total purchases: {total_purchases}
• Success rate: {success_rate:.1f}%
• Revenue: ${total_revenue:.2f}
"""

ADMIN_UNAUTHORIZED = "❌ You are not authorized to use admin commands."

# Compliance messages
COMPLIANCE_AML_BLOCKED = "🛑 Transaction blocked: AML hourly limit exceeded."
COMPLIANCE_SANCTIONS_BLOCKED = "🛑 Transaction blocked: Sanctioned country detected."
COMPLIANCE_DAILY_LIMIT = "🛑 Transaction blocked: Daily spending limit exceeded."
COMPLIANCE_MONTHLY_LIMIT = "🛑 Transaction blocked: Monthly spending limit exceeded."

# Filter messages
FILTER_COUNTRY_PROMPT = "🌍 <b>Select Country</b>\n\nChoose a country to filter by:"
FILTER_BRAND_PROMPT = "🏷️ <b>Select Brand</b>\n\nChoose a card brand:"
FILTER_PRICE_PROMPT = "💲 <b>Set Price Range</b>\n\nEnter price range (e.g., 10-50):"
FILTER_BANK_PROMPT = "🏦 <b>Select Bank</b>\n\nChoose a bank to filter by:"

# Purchase messages
PURCHASE_CONFIRMATION_TEMPLATE = """
🛒 <b>Purchase Confirmation</b>

<b>Item Details:</b>
• SKU: {sku}
• Bank: {bank}
• Country: {country}
• Brand: {brand}
• Type: {type}
• Price: <b>${price:.2f}</b>

<b>Your Balance:</b> ${balance:.2f}

Proceed with purchase?
"""

PURCHASE_SUCCESS_TEMPLATE = """
✅ <b>Purchase Successful!</b>

<b>Card Details:</b>
• Masked PAN: {masked_pan}
• Scheme: {scheme}
• Country: {country}
• Expiry: {expiry}

<b>Transaction:</b>
• Amount: ${price:.2f}
• New Balance: ${balance:.2f}
• Transaction ID: {transaction_id}

<i>Card details are for demo purposes only</i>
"""

# History messages
HISTORY_TEMPLATE = """
📜 <b>Transaction History</b>

{transactions}

<i>Showing last {count} transactions</i>
"""

TRANSACTION_ITEM_TEMPLATE = """
{emoji} <b>{type}</b>
Amount: ${amount:.2f}
Date: {date}
{details}
"""

# Help messages
HELP_COMMANDS = """
🧭 <b>Available Commands</b>

<b>Basic:</b>
/start - Start the bot
/help - Show this help
/balance - Check wallet balance

<b>Wallet:</b>
/addfunds &lt;amount&gt; - Add funds
/history - Transaction history

<b>Catalog:</b>
/search - Search items
/filters - Manage filters

<b>Data:</b>
/delete_me - Delete all data

Use the menu buttons for easier navigation!
"""

# Help Content
HELP_GETTING_STARTED = """
🚀 <b>Getting Started Guide</b>

<b>Welcome to Demo Wallet Bot!</b>

<b>Step 1: Check Your Wallet</b>
• Go to 💼 Wallet to see your current funds
• You start with $100.00 demo balance

<b>Step 2: Browse Cards</b>
• Click 🔎 Browse to see available items
• Use filters to find specific card types

<b>Step 3: Make a Purchase</b>
• Add items to your 🛒 Cart
• Review and checkout when ready

<b>Step 4: Track History</b>
• View all transactions in 📜 History
• Export data for your records
"""

HELP_FAQ = """
❓ <b>Frequently Asked Questions</b>

<b>Q: Is this a real card service?</b>
A: No, this is a demo bot for testing purposes only. All cards and transactions are simulated.

<b>Q: Can I get real money back?</b>
A: No, all funds are virtual demo credits with no real value.

<b>Q: How do I delete my data?</b>
A: Use /delete_me command or Settings → Delete Account

<b>Q: Why are some features limited?</b>
A: This is a demonstration environment with simulated data and limited functionality.

<b>Q: Are my transactions secure?</b>
A: All demo transactions use encrypted connections and follow security best practices.
"""

# Settings messages
SETTINGS_MENU = """
⚙️ <b>Settings</b>

<b>Current Settings:</b>
• Language: {language}
• Currency: {currency}
• Notifications: {notifications}

Choose what to modify:
"""

# Validation messages
VALIDATION_AMOUNT_FORMAT = "Please enter a valid amount (e.g., 25.50)"
VALIDATION_AMOUNT_RANGE = "Amount must be between $1.00 and $1000.00"
VALIDATION_PRICE_RANGE = "Please enter a valid price range (e.g., 10-50)"


def format_currency(amount: float, currency: str = "USD") -> str:
    """Format currency amount"""
    return f"${amount:.2f}"


def format_transaction_type(tx_type: str) -> str:
    """Format transaction type for display"""
    type_map = {
        "ADD_FUNDS": "💸 Funds Added",
        "PURCHASE": "🛒 Purchase",
        "REFUND": "↩️ Refund",
    }
    return type_map.get(tx_type, tx_type)


def format_purchase_status(status: str) -> str:
    """Format purchase status for display"""
    status_map = {
        "SUCCESS": "✅ Success",
        "FAILED": "❌ Failed",
        "BLOCKED": "⛔ Blocked",
        "REFUNDED": "↩️ Refunded",
    }
    return status_map.get(status, status)
