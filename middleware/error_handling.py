"""
Error handling middleware
"""

from __future__ import annotations

import logging
from typing import Any, Awaitable, Callable, Dict

from aiogram.dispatcher.middlewares.base import BaseMiddleware
from aiogram.types import TelegramObject, Message, CallbackQuery

from utils.texts import (
    ERROR_GENERIC,
    ERROR_DATABASE_CONNECTION,
    ERROR_VALIDATION_FAILED,
    ERROR_PERMISSION_DENIED,
    ERROR_SERVICE_UNAVAILABLE,
    ERROR_TIMEOUT,
    DEMO_WATERMARK,
)

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(BaseMiddleware):
    """Middleware for handling errors gracefully"""

    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any],
    ) -> Any:
        """Handle errors in bot handlers"""
        try:
            return await handler(event, data)
        except Exception as e:
            # Determine error type and appropriate message
            error_message = self._get_error_message(e)

            # Safely extract user id from event (works for dicts and aiogram User objects)
            user_id = None
            if hasattr(event, "from_user"):
                fu = getattr(event, "from_user")
                if fu is not None:
                    user_id = getattr(fu, "id", None)
                    if user_id is None and isinstance(fu, dict):
                        user_id = fu.get("id")

            logger.error(
                f"Handler error: {e}",
                extra={
                    "event_type": type(event).__name__,
                    "error_type": type(e).__name__,
                    "user_id": user_id,
                },
            )

            # Send error message to user
            if isinstance(event, (Message, CallbackQuery)):
                try:
                    error_text = error_message + DEMO_WATERMARK

                    if isinstance(event, Message):
                        await event.answer(error_text)
                    elif isinstance(event, CallbackQuery):
                        await event.answer("❌ Error occurred", show_alert=True)
                        if event.message:
                            await event.message.answer(error_text)
                except Exception as send_error:
                    logger.error(f"Failed to send error message: {send_error}")

            # Don't re-raise to prevent bot from crashing
            return None

    def _get_error_message(self, error: Exception) -> str:
        """Get appropriate error message based on exception type"""
        error_type = type(error).__name__
        error_str = str(error).lower()

        # Database-related errors
        if "connection" in error_str or "timeout" in error_str:
            return ERROR_DATABASE_CONNECTION

        # Validation errors
        if error_type in ("ValueError", "ValidationError") or "validation" in error_str:
            return ERROR_VALIDATION_FAILED

        # Security-related errors
        if "rate limit" in error_str or "too many" in error_str:
            return "⏳ Too many requests. Please wait a moment before trying again."

        if "security" in error_str or "authentication" in error_str:
            return "🔒 Security validation failed. Please try again."

        if "suspicious" in error_str or "blocked" in error_str:
            return "🚫 Request blocked for security reasons."

        # Permission errors
        if "permission" in error_str or "unauthorized" in error_str:
            return ERROR_PERMISSION_DENIED

        # Service unavailable
        if "service" in error_str or "unavailable" in error_str:
            return ERROR_SERVICE_UNAVAILABLE

        # Timeout errors
        if "timeout" in error_str:
            return ERROR_TIMEOUT

        # Default generic error
        return ERROR_GENERIC
