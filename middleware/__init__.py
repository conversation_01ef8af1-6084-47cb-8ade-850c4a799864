"""
Middleware setup for the bot
"""

from __future__ import annotations

import logging
from aiogram import Dispatcher, Router

from middleware.rate_limiting import RateLimitMiddleware
from middleware.user_context import UserContextMiddleware
from middleware.error_handling import ErrorHandlingMiddleware

logger = logging.getLogger(__name__)


def setup_middleware(dp: Di<PERSON>atcher) -> None:
    """Setup all middleware"""
    try:
        # Attach only global error handling at dispatcher level
        dp.message.middleware(ErrorHandlingMiddleware())
        dp.callback_query.middleware(ErrorHandlingMiddleware())

        logger.info("All middleware setup completed")

    except Exception as e:
        logger.error(f"Failed to setup middleware: {e}")
        raise


def attach_common_middlewares(router: Router) -> None:
    """Attach commonly used middlewares to router (per-router scope)."""
    # Rate limiting and user context per-router to avoid duplicate runs
    router.message.middleware(RateLimitMiddleware())
    router.callback_query.middleware(RateLimitMiddleware())

    router.message.middleware(UserContextMiddleware())
    router.callback_query.middleware(UserContextMiddleware())
