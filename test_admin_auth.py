#!/usr/bin/env python3
"""
Test script to verify admin authentication is working correctly
"""

import os
import sys
from dotenv import load_dotenv

def test_admin_configuration():
    """Test admin configuration and security requirements"""
    print("🔐 Testing Admin Authentication Configuration")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Test 1: Check if admin passphrase is configured
    passphrase = os.getenv("ADMIN_PASSPHRASE", "").strip()
    print(f"✅ Admin passphrase configured: {'Yes' if passphrase else 'No'}")
    
    if not passphrase:
        print("❌ ADMIN_PASSPHRASE environment variable is not set!")
        return False
    
    # Test 2: Check passphrase length (security requirement)
    min_length = 12
    passphrase_length = len(passphrase)
    meets_security = passphrase_length >= min_length
    
    print(f"✅ Passphrase length: {passphrase_length} characters")
    print(f"✅ Meets security requirements (>={min_length}): {'Yes' if meets_security else 'No'}")
    
    if not meets_security:
        print(f"❌ Admin passphrase is too short! Must be at least {min_length} characters.")
        return False
    
    # Test 3: Check admin user IDs
    admin_ids = os.getenv("ADMIN_USER_IDS", "").strip()
    print(f"✅ Admin user IDs configured: {'Yes' if admin_ids else 'No'}")
    
    if admin_ids:
        try:
            # Parse admin IDs
            ids = [int(id.strip()) for id in admin_ids.split(",") if id.strip()]
            print(f"✅ Number of admin users: {len(ids)}")
            print(f"✅ Admin user IDs: {ids}")
        except ValueError as e:
            print(f"❌ Invalid admin user IDs format: {e}")
            return False
    else:
        print("⚠️  No admin user IDs configured - admin access may be limited")
    
    # Test 4: Check other security settings
    encryption_key = os.getenv("API_CONFIG_ENCRYPTION_KEY", "").strip()
    print(f"✅ API encryption key configured: {'Yes' if encryption_key else 'No'}")
    
    environment = os.getenv("ENVIRONMENT", "development")
    print(f"✅ Environment: {environment}")
    
    print("\n🎉 Admin authentication configuration is valid!")
    print("=" * 50)
    
    # Test 5: Simulate the security check from handlers/admin_handlers.py
    print("\n🧪 Simulating Admin Handler Security Check")
    print("-" * 40)
    
    # This mimics the logic in handlers/admin_handlers.py
    if not passphrase:
        print("❌ Admin passphrase not configured")
        return False
    
    if len(passphrase) < 12:
        print("❌ Admin passphrase does not meet security requirements")
        return False
    
    print("✅ All security checks passed!")
    print("✅ Admin authentication should work correctly")
    
    return True

def main():
    """Main test function"""
    try:
        success = test_admin_configuration()
        if success:
            print("\n🎉 SUCCESS: Admin authentication is properly configured!")
            sys.exit(0)
        else:
            print("\n❌ FAILURE: Admin authentication configuration has issues!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 ERROR: Test failed with exception: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
