"""
MongoDB models for the Demo Wallet Bot
"""

from .base import BaseDocument, TimestampMixin, SoftDeleteMixin
from .user import User, Wallet
from .transaction import (
    Transaction,
    Purchase,
    CardDemo,
    TransactionType,
    PurchaseStatus,
)
from .catalog import (
    CatalogItem,
    SavedFilter,
    AuditLog,
    AppSetting,
    Cart,
    CartItem,
    CartStatus,
)
from .product import (
    ProductType,
    APIStatus,
    APIInfo,
    ProductInfo,
    UserProductPreference,
    ProductConfiguration,
    DEFAULT_PRODUCT_CONFIG,
)

__all__ = [
    "BaseDocument",
    "TimestampMixin",
    "SoftDeleteMixin",
    "User",
    "Wallet",
    "Transaction",
    "Purchase",
    "CardDemo",
    "TransactionType",
    "PurchaseStatus",
    "CatalogItem",
    "SavedFilter",
    "AuditLog",
    "AppSetting",
    "Cart",
    "CartItem",
    "CartStatus",
    "ProductType",
    "APIStatus",
    "APIInfo",
    "ProductInfo",
    "UserProductPreference",
    "ProductConfiguration",
    "DEFAULT_PRODUCT_CONFIG",
]
