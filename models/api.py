"""
API Management models for comprehensive API configuration and monitoring
"""

from __future__ import annotations

import datetime as dt
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from pydantic import BaseModel, Field, field_validator, model_validator
import json

from models.base import (
    BaseDocument,
    TimestampMixin,
    SoftDeleteMixin,
    generate_id,
    now_utc,
)


class AuthenticationType(str, Enum):
    """Supported authentication types for APIs"""

    NONE = "none"
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer_token"
    BASIC_AUTH = "basic_auth"
    OAUTH2 = "oauth2"
    CUSTOM_HEADER = "custom_header"


class APIEnvironment(str, Enum):
    """API environment types"""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class APIStatus(str, Enum):
    """API status indicators"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"


class HTTPMethod(str, Enum):
    """Supported HTTP methods"""

    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    PATCH = "PATCH"
    DELETE = "DELETE"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class RateLimitConfig(BaseModel):
    """Rate limiting configuration for APIs"""

    requests_per_minute: Optional[int] = Field(
        default=None, ge=1, description="Requests per minute limit"
    )
    requests_per_hour: Optional[int] = Field(
        default=None, ge=1, description="Requests per hour limit"
    )
    requests_per_day: Optional[int] = Field(
        default=None, ge=1, description="Requests per day limit"
    )
    burst_limit: Optional[int] = Field(
        default=None, ge=1, description="Burst request limit"
    )
    enabled: bool = Field(default=True, description="Whether rate limiting is enabled")


class TimeoutConfig(BaseModel):
    """Timeout configuration for API requests"""

    connect_timeout: int = Field(
        default=10, ge=1, le=300, description="Connection timeout in seconds"
    )
    read_timeout: int = Field(
        default=30, ge=1, le=600, description="Read timeout in seconds"
    )
    total_timeout: int = Field(
        default=60, ge=1, le=900, description="Total request timeout in seconds"
    )


class RetryConfig(BaseModel):
    """Retry configuration for failed API requests"""

    max_retries: int = Field(
        default=3, ge=0, le=10, description="Maximum number of retries"
    )
    backoff_factor: float = Field(
        default=1.0, ge=0.1, le=10.0, description="Backoff factor for retries"
    )
    retry_on_status: List[int] = Field(
        default_factory=lambda: [500, 502, 503, 504],
        description="HTTP status codes to retry on",
    )
    enabled: bool = Field(default=True, description="Whether retries are enabled")


class AuthenticationConfig(BaseModel):
    """Authentication configuration for APIs"""

    type: AuthenticationType = Field(description="Authentication type")
    api_key: Optional[str] = Field(default=None, description="API key (encrypted)")
    api_key_header: Optional[str] = Field(
        default="X-API-Key", description="Header name for API key"
    )
    bearer_token: Optional[str] = Field(
        default=None, description="Bearer token (encrypted)"
    )
    username: Optional[str] = Field(default=None, description="Username for basic auth")
    password: Optional[str] = Field(
        default=None, description="Password for basic auth (encrypted)"
    )
    oauth2_client_id: Optional[str] = Field(
        default=None, description="OAuth2 client ID"
    )
    oauth2_client_secret: Optional[str] = Field(
        default=None, description="OAuth2 client secret (encrypted)"
    )
    oauth2_token_url: Optional[str] = Field(
        default=None, description="OAuth2 token endpoint URL"
    )
    oauth2_scope: Optional[str] = Field(default=None, description="OAuth2 scope")
    custom_headers: Dict[str, str] = Field(
        default_factory=dict, description="Custom authentication headers"
    )

    @field_validator("type")
    @classmethod
    def validate_auth_type(cls, v):
        if v not in AuthenticationType:
            raise ValueError(f"Invalid authentication type: {v}")
        return v


class HealthCheckConfig(BaseModel):
    """Health check configuration for APIs"""

    enabled: bool = Field(default=True, description="Whether health checks are enabled")
    endpoint: Optional[str] = Field(
        default=None, description="Health check endpoint path"
    )
    method: HTTPMethod = Field(
        default=HTTPMethod.GET, description="HTTP method for health checks"
    )
    interval_minutes: int = Field(
        default=5, ge=1, le=1440, description="Health check interval in minutes"
    )
    timeout_seconds: int = Field(
        default=10, ge=1, le=60, description="Health check timeout in seconds"
    )
    expected_status_codes: List[int] = Field(
        default_factory=lambda: [200], description="Expected HTTP status codes"
    )
    expected_response_contains: Optional[str] = Field(
        default=None, description="Expected response content"
    )
    failure_threshold: int = Field(
        default=3,
        ge=1,
        le=10,
        description="Consecutive failures before marking as unhealthy",
    )
    success_threshold: int = Field(
        default=1, ge=1, le=5, description="Consecutive successes to mark as healthy"
    )


class APIConfiguration(BaseDocument, SoftDeleteMixin):
    """Main API configuration model"""

    # Basic Information
    name: str = Field(description="Human-readable API name")
    description: Optional[str] = Field(default=None, description="API description")
    base_url: str = Field(description="Base URL for the API")
    version: Optional[str] = Field(default="1.0", description="API version")

    # Environment and Status
    environment: APIEnvironment = Field(
        default=APIEnvironment.DEVELOPMENT, description="API environment"
    )
    status: APIStatus = Field(
        default=APIStatus.ACTIVE, description="Current API status"
    )

    # Authentication
    authentication: AuthenticationConfig = Field(
        description="Authentication configuration"
    )

    # Request Configuration
    default_headers: Dict[str, str] = Field(
        default_factory=dict, description="Default headers for all requests"
    )
    default_params: Dict[str, str] = Field(
        default_factory=dict, description="Default query parameters"
    )

    # Limits and Timeouts
    rate_limit: RateLimitConfig = Field(
        default_factory=RateLimitConfig, description="Rate limiting configuration"
    )
    timeout: TimeoutConfig = Field(
        default_factory=TimeoutConfig, description="Timeout configuration"
    )
    retry: RetryConfig = Field(
        default_factory=RetryConfig, description="Retry configuration"
    )

    # Health Monitoring
    health_check: HealthCheckConfig = Field(
        default_factory=HealthCheckConfig, description="Health check configuration"
    )

    # Metadata
    tags: List[str] = Field(default_factory=list, description="Tags for categorization")
    owner: Optional[str] = Field(default=None, description="API owner/maintainer")
    documentation_url: Optional[str] = Field(
        default=None, description="Link to API documentation"
    )

    # System fields
    created_by: str = Field(description="User ID who created this configuration")
    last_modified_by: Optional[str] = Field(
        default=None, description="User ID who last modified this configuration"
    )

    @field_validator("base_url")
    @classmethod
    def validate_base_url(cls, v):
        if not v.startswith(("http://", "https://")):
            raise ValueError("Base URL must start with http:// or https://")
        return v.rstrip("/")

    @field_validator("name")
    @classmethod
    def validate_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError("API name must be at least 2 characters long")
        return v.strip()

    def update_modified_by(self, user_id: str) -> None:
        """Update the last modified timestamp and user"""
        self.last_modified_by = user_id
        self.update_timestamp()


class APIEndpoint(BaseDocument):
    """Individual API endpoint configuration"""

    api_config_id: str = Field(description="Reference to parent API configuration")

    # Endpoint details
    path: str = Field(description="Endpoint path (relative to base URL)")
    method: HTTPMethod = Field(description="HTTP method")
    name: str = Field(description="Human-readable endpoint name")
    description: Optional[str] = Field(default=None, description="Endpoint description")

    # Override configurations (optional)
    custom_headers: Dict[str, str] = Field(
        default_factory=dict, description="Endpoint-specific headers"
    )
    custom_params: Dict[str, str] = Field(
        default_factory=dict, description="Endpoint-specific parameters"
    )
    custom_timeout: Optional[TimeoutConfig] = Field(
        default=None, description="Endpoint-specific timeout"
    )

    # Request/Response schema (optional)
    request_schema: Optional[Dict[str, Any]] = Field(
        default=None, description="JSON schema for request body"
    )
    response_schema: Optional[Dict[str, Any]] = Field(
        default=None, description="JSON schema for response body"
    )

    # Metadata
    tags: List[str] = Field(default_factory=list, description="Endpoint tags")
    is_deprecated: bool = Field(
        default=False, description="Whether endpoint is deprecated"
    )

    @field_validator("path")
    @classmethod
    def validate_path(cls, v):
        if not v.startswith("/"):
            v = "/" + v
        return v


class APIHealthStatus(BaseDocument):
    """API health monitoring status and metrics"""

    api_config_id: str = Field(description="Reference to API configuration")

    # Health status
    is_healthy: bool = Field(description="Current health status")
    last_check_at: dt.datetime = Field(
        default_factory=now_utc, description="Last health check timestamp"
    )
    next_check_at: dt.datetime = Field(description="Next scheduled health check")

    # Metrics
    response_time_ms: Optional[int] = Field(
        default=None, description="Last response time in milliseconds"
    )
    status_code: Optional[int] = Field(
        default=None, description="Last HTTP status code"
    )
    error_message: Optional[str] = Field(
        default=None, description="Last error message if any"
    )

    # Counters
    consecutive_failures: int = Field(
        default=0, description="Consecutive failure count"
    )
    consecutive_successes: int = Field(
        default=0, description="Consecutive success count"
    )
    total_checks: int = Field(
        default=0, description="Total number of health checks performed"
    )
    total_failures: int = Field(
        default=0, description="Total number of failed health checks"
    )

    # Availability metrics
    uptime_percentage: float = Field(default=100.0, description="Uptime percentage")
    last_downtime_at: Optional[dt.datetime] = Field(
        default=None, description="Last downtime timestamp"
    )
    downtime_duration_minutes: int = Field(
        default=0, description="Total downtime in minutes"
    )


class APIUsageMetrics(BaseDocument):
    """API usage analytics and metrics"""

    api_config_id: str = Field(description="Reference to API configuration")
    endpoint_id: Optional[str] = Field(
        default=None, description="Reference to specific endpoint (optional)"
    )

    # Time period
    period_start: dt.datetime = Field(description="Start of measurement period")
    period_end: dt.datetime = Field(description="End of measurement period")
    period_type: str = Field(description="Period type: hourly, daily, weekly, monthly")

    # Request metrics
    total_requests: int = Field(default=0, description="Total number of requests")
    successful_requests: int = Field(
        default=0, description="Number of successful requests (2xx)"
    )
    failed_requests: int = Field(
        default=0, description="Number of failed requests (4xx, 5xx)"
    )

    # Response time metrics
    avg_response_time_ms: float = Field(
        default=0.0, description="Average response time in milliseconds"
    )
    min_response_time_ms: int = Field(
        default=0, description="Minimum response time in milliseconds"
    )
    max_response_time_ms: int = Field(
        default=0, description="Maximum response time in milliseconds"
    )
    p95_response_time_ms: float = Field(
        default=0.0, description="95th percentile response time"
    )
    p99_response_time_ms: float = Field(
        default=0.0, description="99th percentile response time"
    )

    # Error metrics
    error_rate: float = Field(default=0.0, description="Error rate percentage")
    timeout_count: int = Field(default=0, description="Number of timeout errors")
    connection_error_count: int = Field(
        default=0, description="Number of connection errors"
    )

    # Status code breakdown
    status_2xx_count: int = Field(default=0, description="2xx status code count")
    status_3xx_count: int = Field(default=0, description="3xx status code count")
    status_4xx_count: int = Field(default=0, description="4xx status code count")
    status_5xx_count: int = Field(default=0, description="5xx status code count")

    # Data transfer
    total_bytes_sent: int = Field(default=0, description="Total bytes sent")
    total_bytes_received: int = Field(default=0, description="Total bytes received")


class APIRequestLog(BaseDocument):
    """Individual API request log entry"""

    api_config_id: str = Field(description="Reference to API configuration")
    endpoint_id: Optional[str] = Field(
        default=None, description="Reference to specific endpoint"
    )

    # Request details
    method: HTTPMethod = Field(description="HTTP method used")
    url: str = Field(description="Full request URL")
    headers: Dict[str, str] = Field(
        default_factory=dict, description="Request headers (sensitive data removed)"
    )
    query_params: Dict[str, str] = Field(
        default_factory=dict, description="Query parameters"
    )

    # Response details
    status_code: Optional[int] = Field(
        default=None, description="HTTP response status code"
    )
    response_time_ms: Optional[int] = Field(
        default=None, description="Response time in milliseconds"
    )
    response_size_bytes: Optional[int] = Field(
        default=None, description="Response size in bytes"
    )

    # Error information
    error_type: Optional[str] = Field(
        default=None, description="Error type if request failed"
    )
    error_message: Optional[str] = Field(
        default=None, description="Error message if request failed"
    )

    # Metadata
    user_agent: Optional[str] = Field(
        default=None, description="User agent used for request"
    )
    initiated_by: Optional[str] = Field(
        default=None, description="User or system that initiated the request"
    )
    request_id: Optional[str] = Field(
        default=None, description="Unique request identifier"
    )


class APICredential(BaseDocument, SoftDeleteMixin):
    """Encrypted storage for API credentials"""

    api_config_id: str = Field(description="Reference to API configuration")

    # Credential details
    credential_type: AuthenticationType = Field(description="Type of credential")
    name: str = Field(description="Human-readable credential name")
    description: Optional[str] = Field(
        default=None, description="Credential description"
    )

    # Encrypted data
    encrypted_data: str = Field(description="Encrypted credential data")
    encryption_key_id: str = Field(description="ID of encryption key used")

    # Metadata
    environment: APIEnvironment = Field(
        description="Environment this credential is for"
    )
    is_active: bool = Field(default=True, description="Whether credential is active")
    expires_at: Optional[dt.datetime] = Field(
        default=None, description="Credential expiration date"
    )
    last_used_at: Optional[dt.datetime] = Field(
        default=None, description="Last time credential was used"
    )

    # Audit
    created_by: str = Field(description="User who created this credential")
    last_modified_by: Optional[str] = Field(
        default=None, description="User who last modified this credential"
    )

    def is_expired(self) -> bool:
        """Check if credential is expired"""
        if self.expires_at is None:
            return False
        return now_utc() > self.expires_at

    def mark_used(self) -> None:
        """Mark credential as recently used"""
        self.last_used_at = now_utc()


class APIAuditLog(BaseDocument):
    """Audit log for API management operations"""

    # Operation details
    operation: str = Field(
        description="Operation performed (create, update, delete, test, etc.)"
    )
    resource_type: str = Field(
        description="Type of resource (api_config, endpoint, credential, etc.)"
    )
    resource_id: str = Field(description="ID of the affected resource")

    # Actor information
    actor_id: str = Field(description="ID of user who performed the operation")
    actor_username: Optional[str] = Field(default=None, description="Username of actor")
    actor_ip: Optional[str] = Field(default=None, description="IP address of actor")

    # Change details
    changes: Dict[str, Any] = Field(
        default_factory=dict, description="Details of changes made"
    )
    old_values: Dict[str, Any] = Field(
        default_factory=dict, description="Previous values (for updates)"
    )
    new_values: Dict[str, Any] = Field(
        default_factory=dict, description="New values (for updates)"
    )

    # Context
    session_id: Optional[str] = Field(default=None, description="Session ID")
    user_agent: Optional[str] = Field(default=None, description="User agent")

    # Result
    success: bool = Field(description="Whether operation was successful")
    error_message: Optional[str] = Field(
        default=None, description="Error message if operation failed"
    )

    # Metadata
    timestamp: dt.datetime = Field(
        default_factory=now_utc, description="Operation timestamp"
    )
    duration_ms: Optional[int] = Field(
        default=None, description="Operation duration in milliseconds"
    )
