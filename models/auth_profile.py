"""
Authentication Profile Models for Centralized Authentication Management
Provides reusable authentication profiles that can be shared across multiple API configurations
"""

from __future__ import annotations

import datetime as dt
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from pydantic import BaseModel, Field, field_validator, model_validator

from models.base import (
    BaseDocument,
    TimestampMixin,
    SoftDeleteMixin,
    generate_id,
    now_utc,
)
from models.api import AuthenticationType, APIEnvironment


class AuthProfileScope(str, Enum):
    """Scope of authentication profile usage"""

    GLOBAL = "global"  # Can be used by any API configuration
    CATEGORY = "category"  # Limited to specific API categories
    PROVIDER = "provider"  # Limited to specific API provider
    CUSTOM = "custom"  # Custom scope definition


class AuthProfileStatus(str, Enum):
    """Authentication profile status"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"
    EXPIRED = "expired"


@dataclass
class AuthProfileCredentials:
    """Centralized authentication credentials"""

    # Primary authentication
    login_token: str = ""
    api_key: str = ""
    bearer_token: str = ""

    # Basic authentication
    username: str = ""
    password: str = ""

    # OAuth2 credentials
    oauth2_client_id: str = ""
    oauth2_client_secret: str = ""
    oauth2_token_url: str = ""
    oauth2_scope: str = ""
    oauth2_refresh_token: str = ""

    # Session management
    session_cookies: Dict[str, str] = None

    # Headers and custom authentication
    headers: Dict[str, str] = None
    custom_auth_data: Dict[str, Any] = None

    def __post_init__(self):
        if self.session_cookies is None:
            self.session_cookies = {}
        if self.headers is None:
            self.headers = {}
        if self.custom_auth_data is None:
            self.custom_auth_data = {}


class AuthenticationProfile(BaseDocument, TimestampMixin, SoftDeleteMixin):
    """Centralized authentication profile for reuse across API configurations"""

    # Profile identification
    profile_name: str = Field(description="Unique profile name")
    display_name: str = Field(description="Human-readable profile name")
    description: Optional[str] = Field(default=None, description="Profile description")

    # Authentication configuration
    auth_type: AuthenticationType = Field(description="Primary authentication type")
    credentials: Dict[str, Any] = Field(
        default_factory=dict, description="Encrypted credentials"
    )

    # Profile scope and usage
    scope: AuthProfileScope = Field(
        default=AuthProfileScope.GLOBAL, description="Profile usage scope"
    )
    scope_filter: Optional[str] = Field(
        default=None, description="Scope filter (category, provider, etc.)"
    )
    allowed_categories: List[str] = Field(
        default_factory=list, description="Allowed API categories"
    )
    allowed_providers: List[str] = Field(
        default_factory=list, description="Allowed API providers"
    )

    # Environment and lifecycle
    environment: APIEnvironment = Field(
        default=APIEnvironment.DEVELOPMENT, description="Target environment"
    )
    status: AuthProfileStatus = Field(
        default=AuthProfileStatus.ACTIVE, description="Profile status"
    )
    expires_at: Optional[dt.datetime] = Field(
        default=None, description="Profile expiration date"
    )

    # Usage tracking
    usage_count: int = Field(default=0, description="Number of APIs using this profile")
    last_used_at: Optional[dt.datetime] = Field(
        default=None, description="Last usage timestamp"
    )

    # Configuration metadata
    tags: List[str] = Field(
        default_factory=list, description="Profile tags for organization"
    )
    provider_name: Optional[str] = Field(default=None, description="API provider name")
    provider_documentation: Optional[str] = Field(
        default=None, description="Provider documentation URL"
    )

    # Security and audit
    encryption_key_id: str = Field(description="ID of encryption key used")
    created_by: str = Field(description="User who created this profile")
    last_modified_by: Optional[str] = Field(
        default=None, description="User who last modified"
    )

    # Validation settings
    auto_refresh: bool = Field(
        default=False, description="Auto-refresh credentials if supported"
    )
    validation_endpoint: Optional[str] = Field(
        default=None, description="Endpoint to validate credentials"
    )
    validation_interval: int = Field(
        default=3600, description="Validation interval in seconds"
    )
    last_validated_at: Optional[dt.datetime] = Field(
        default=None, description="Last validation timestamp"
    )

    @field_validator("profile_name")
    @classmethod
    def validate_profile_name(cls, v):
        if not v or not v.strip():
            raise ValueError("Profile name cannot be empty")
        # Ensure profile name is URL-safe and unique-friendly
        import re

        if not re.match(r"^[a-z0-9_-]+$", v.lower()):
            raise ValueError(
                "Profile name must contain only lowercase letters, numbers, underscores, and hyphens"
            )
        return v.lower()

    @field_validator("auth_type")
    @classmethod
    def validate_auth_type(cls, v):
        if v not in AuthenticationType:
            raise ValueError(f"Invalid authentication type: {v}")
        return v

    def is_expired(self) -> bool:
        """Check if profile is expired"""
        if self.expires_at is None:
            return False
        return now_utc() > self.expires_at

    def is_active(self) -> bool:
        """Check if profile is active and usable"""
        return (
            self.status == AuthProfileStatus.ACTIVE
            and not self.is_expired()
            and not self.is_deleted
        )

    def can_be_used_by_category(self, category: str) -> bool:
        """Check if profile can be used by given API category"""
        if self.scope == AuthProfileScope.GLOBAL:
            return True
        elif self.scope == AuthProfileScope.CATEGORY:
            return category in self.allowed_categories
        elif self.scope == AuthProfileScope.PROVIDER:
            # For provider scope, we check if the category is allowed
            return not self.allowed_categories or category in self.allowed_categories
        return True  # CUSTOM scope allows all by default

    def can_be_used_by_provider(self, provider: str) -> bool:
        """Check if profile can be used by given API provider"""
        if self.scope == AuthProfileScope.GLOBAL:
            return True
        elif self.scope == AuthProfileScope.PROVIDER:
            return provider in self.allowed_providers
        return True  # Other scopes allow all providers

    def increment_usage(self) -> None:
        """Increment usage counter and update last used timestamp"""
        self.usage_count += 1
        self.last_used_at = now_utc()

    def mark_validated(self) -> None:
        """Mark profile as recently validated"""
        self.last_validated_at = now_utc()


class AuthProfileAssignment(BaseDocument, TimestampMixin):
    """Assignment of authentication profiles to API configurations"""

    # Assignment identification
    api_config_id: str = Field(description="API configuration ID")
    auth_profile_id: str = Field(description="Authentication profile ID")

    # Assignment configuration
    is_active: bool = Field(default=True, description="Whether assignment is active")
    override_credentials: Dict[str, Any] = Field(
        default_factory=dict, description="Credential overrides for this specific API"
    )
    override_headers: Dict[str, str] = Field(
        default_factory=dict, description="Header overrides for this specific API"
    )

    # Assignment metadata
    assigned_by: str = Field(description="User who created this assignment")
    assignment_reason: Optional[str] = Field(
        default=None, description="Reason for assignment"
    )

    # Usage tracking
    last_used_at: Optional[dt.datetime] = Field(
        default=None, description="Last time this assignment was used"
    )
    usage_count: int = Field(
        default=0, description="Number of times this assignment was used"
    )

    def mark_used(self) -> None:
        """Mark assignment as recently used"""
        self.last_used_at = now_utc()
        self.usage_count += 1


class AuthProfileUsageLog(BaseDocument, TimestampMixin):
    """Log of authentication profile usage for audit and analytics"""

    # Usage identification
    auth_profile_id: str = Field(description="Authentication profile ID")
    api_config_id: str = Field(description="API configuration ID that used the profile")

    # Usage details
    operation: str = Field(
        description="Operation performed (authenticate, validate, etc.)"
    )
    success: bool = Field(description="Whether the operation was successful")
    error_message: Optional[str] = Field(
        default=None, description="Error message if operation failed"
    )

    # Context information
    user_id: Optional[str] = Field(
        default=None, description="User who triggered the operation"
    )
    endpoint_used: Optional[str] = Field(
        default=None, description="API endpoint that was accessed"
    )
    response_time_ms: Optional[int] = Field(
        default=None, description="Response time in milliseconds"
    )

    # Metadata
    environment: APIEnvironment = Field(
        description="Environment where operation occurred"
    )
    additional_data: Dict[str, Any] = Field(
        default_factory=dict, description="Additional context data"
    )


class AuthProfileTemplate(BaseModel):
    """Template for creating authentication profiles for common providers"""

    # Template identification
    template_id: str = Field(description="Unique template identifier")
    template_name: str = Field(description="Human-readable template name")
    description: str = Field(description="Template description")

    # Provider information
    provider_name: str = Field(description="API provider name")
    provider_category: str = Field(
        description="Provider category (payment, ecommerce, etc.)"
    )
    provider_documentation: Optional[str] = Field(
        default=None, description="Provider documentation URL"
    )

    # Authentication configuration
    auth_type: AuthenticationType = Field(
        description="Authentication type for this provider"
    )
    required_fields: List[str] = Field(description="Required credential fields")
    optional_fields: List[str] = Field(
        default_factory=list, description="Optional credential fields"
    )

    # Default configuration
    default_headers: Dict[str, str] = Field(
        default_factory=dict, description="Default headers for this provider"
    )
    default_scope: AuthProfileScope = Field(
        default=AuthProfileScope.PROVIDER, description="Default profile scope"
    )

    # Setup guidance
    setup_instructions: str = Field(
        description="Instructions for setting up authentication"
    )
    credential_sources: Dict[str, str] = Field(
        default_factory=dict,
        description="Where to find each credential (e.g., 'api_key': 'Developer Dashboard > API Keys')",
    )

    # Validation configuration
    validation_endpoint: Optional[str] = Field(
        default=None, description="Endpoint to test credentials"
    )
    validation_method: str = Field(
        default="GET", description="HTTP method for validation"
    )

    def create_profile(
        self, profile_name: str, credentials: Dict[str, Any], created_by: str, **kwargs
    ) -> AuthenticationProfile:
        """Create an authentication profile from this template"""
        return AuthenticationProfile(
            profile_name=profile_name,
            display_name=kwargs.get(
                "display_name", f"{self.provider_name} - {profile_name}"
            ),
            description=kwargs.get(
                "description", f"Authentication profile for {self.provider_name}"
            ),
            auth_type=self.auth_type,
            credentials=credentials,
            scope=kwargs.get("scope", self.default_scope),
            provider_name=self.provider_name,
            provider_documentation=self.provider_documentation,
            tags=kwargs.get(
                "tags", [self.provider_category, self.provider_name.lower()]
            ),
            validation_endpoint=self.validation_endpoint,
            created_by=created_by,
            encryption_key_id=kwargs.get("encryption_key_id", "default"),
            environment=kwargs.get("environment", APIEnvironment.DEVELOPMENT),
        )
