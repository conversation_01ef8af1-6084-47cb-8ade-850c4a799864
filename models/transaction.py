"""
Transaction and Purchase models for MongoDB
"""

from __future__ import annotations

import hashlib
import time
from enum import Enum
from typing import Optional, Dict, Any

from pydantic import Field, field_validator

from models.base import BaseDocument, TimestampMixin


class TransactionType(str, Enum):
    """Transaction type enumeration"""

    ADD_FUNDS = "ADD_FUNDS"
    PURCHASE = "PURCHASE"
    REFUND = "REFUND"


class PurchaseStatus(str, Enum):
    """Purchase status enumeration"""

    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    BLOCKED = "BLOCKED"
    REFUNDED = "REFUNDED"


class Transaction(BaseDocument, TimestampMixin):
    """Transaction document model"""

    user_id: str = Field(..., description="Reference to User document ID")
    type: TransactionType = Field(..., description="Transaction type")
    amount: float = Field(..., ge=0, description="Transaction amount")
    currency: str = Field(
        default="USD", max_length=8, description="Transaction currency"
    )
    reference: Optional[str] = Field(
        default=None, max_length=128, description="Transaction reference"
    )
    hash: str = Field(default="", description="Transaction hash for integrity")
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional transaction metadata"
    )

    @field_validator("amount")
    @classmethod
    def validate_amount(cls, v):
        return round(v, 2)

    @field_validator("currency")
    @classmethod
    def validate_currency(cls, v):
        if len(v) != 3 or not v.isalpha():
            raise ValueError("Currency must be a 3-letter code")
        return v.upper()

    @field_validator("hash")
    @classmethod
    def generate_hash(cls, v, info):
        """Generate integrity hash for transaction if not provided or blank."""
        try:
            if isinstance(v, str) and v.strip():
                return v
            # Generate hash from transaction data
            values = getattr(info, "data", {}) or {}
            user_id = values.get("user_id", "")
            tx_type = values.get("type", "")
            amount = values.get("amount", 0)
            currency = values.get("currency", "USD")
            reference = values.get("reference", "")
            data = f"{tx_type}:{user_id}:{amount}:{currency}:{reference}:{time.time()}"
            return hashlib.sha256(data.encode()).hexdigest()
        except Exception:
            # Fallback: ensure non-empty value to avoid unique index nulls
            return hashlib.sha256(f"fallback:{time.time()}".encode()).hexdigest()

    model_config = {"collection_name": "transactions"}


class Purchase(BaseDocument, TimestampMixin):
    """Purchase document model"""

    user_id: str = Field(..., description="Reference to User document ID")
    sku: str = Field(..., max_length=64, description="Product SKU")
    price: float = Field(..., ge=0, description="Purchase price")
    currency: str = Field(default="USD", max_length=8, description="Purchase currency")
    status: PurchaseStatus = Field(
        default=PurchaseStatus.SUCCESS, description="Purchase status"
    )
    api_req_id: Optional[str] = Field(
        default=None, max_length=64, description="External API request ID"
    )
    idempotency_key: Optional[str] = Field(
        default=None, max_length=128, description="Idempotency key"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional purchase metadata"
    )

    @field_validator("price")
    @classmethod
    def validate_price(cls, v):
        return round(v, 2)

    @field_validator("currency")
    @classmethod
    def validate_currency(cls, v):
        if len(v) != 3 or not v.isalpha():
            raise ValueError("Currency must be a 3-letter code")
        return v.upper()

    model_config = {"collection_name": "purchases"}


class CardDemo(BaseDocument, TimestampMixin):
    """Demo card document model"""

    purchase_id: str = Field(..., description="Reference to Purchase document ID")
    masked_pan: str = Field(..., max_length=32, description="Masked PAN")
    scheme: Optional[str] = Field(
        default=None, max_length=16, description="Card scheme (VISA, MASTERCARD, etc.)"
    )
    country: Optional[str] = Field(
        default=None, max_length=8, description="Issuing country"
    )
    expiry: Optional[str] = Field(
        default=None, max_length=8, description="Card expiry (MM/YY)"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional card metadata"
    )

    @field_validator("masked_pan")
    @classmethod
    def validate_masked_pan(cls, v):
        # Basic validation for masked PAN format
        if not v or len(v) < 8:
            raise ValueError("Masked PAN too short")
        return v

    @field_validator("country")
    @classmethod
    def validate_country(cls, v):
        if v and (len(v) != 2 or not v.isalpha()):
            raise ValueError("Country must be a 2-letter code")
        return v.upper() if v else v

    @field_validator("scheme")
    @classmethod
    def validate_scheme(cls, v):
        if v:
            valid_schemes = {"VISA", "MASTERCARD", "AMEX", "DISCOVER"}
            if v.upper() not in valid_schemes:
                raise ValueError(f"Scheme must be one of: {valid_schemes}")
            return v.upper()
        return v

    model_config = {"collection_name": "cards"}
