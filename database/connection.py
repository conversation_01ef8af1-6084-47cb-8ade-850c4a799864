"""
MongoDB connection and database management
"""

from __future__ import annotations

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from motor.motor_asyncio import (
    AsyncIOMotorClient,
    AsyncIOMotorDatabase,
    AsyncIOMotorCollection,
)
from pymongo.errors import (
    ConnectionFailure,
    ServerSelectionTimeoutError,
    OperationFailure,
)

from config.settings import get_settings

logger = logging.getLogger(__name__)


class SQLiteSimulation:
    """Simple in-memory simulation of MongoDB collections for demo purposes"""

    def __init__(self, db_name: str):
        self.db_name = db_name
        self.collections = {}
        logger.info(f"Initialized SQLite simulation for database: {db_name}")

    def __getitem__(self, collection_name: str):
        """Get or create a collection"""
        if collection_name not in self.collections:
            self.collections[collection_name] = InMemoryCollection(collection_name)
        return self.collections[collection_name]

    async def command(self, cmd: str):
        """Simulate database commands"""
        if cmd == "ping":
            return {"ok": 1}
        return {"ok": 1}


class InMemoryCollection:
    """Simple in-memory collection simulation"""

    def __init__(self, name: str):
        self.name = name
        self.documents = []
        self.next_id = 1

    # --- Query helpers ---------------------------------------------------------
    def _apply_operators(self, doc_value, condition: dict) -> bool:
        """Support minimal Mongo-like operators for demo purposes"""
        for op, val in condition.items():
            if op == "$lt":
                if not (doc_value < val):
                    return False
            elif op == "$lte":
                if not (doc_value <= val):
                    return False
            elif op == "$gt":
                if not (doc_value > val):
                    return False
            elif op == "$gte":
                if not (doc_value >= val):
                    return False
            elif op == "$eq":
                if not (doc_value == val):
                    return False
            elif op == "$ne":
                if not (doc_value != val):
                    return False
            elif op == "$in":
                try:
                    if doc_value not in val:
                        return False
                except Exception:
                    return False
            else:
                return False
        return True

    async def find_one(self, filter_dict: dict):
        """Find one document"""
        for doc in self.documents:
            if self._matches_filter(doc, filter_dict):
                return doc
        return None

    async def insert_one(self, document: dict):
        """Insert one document"""
        doc = document.copy()
        if "_id" not in doc:
            try:
                from bson import ObjectId

                doc["_id"] = ObjectId()
            except Exception:
                # Fallback to string id if bson isn't available
                doc["_id"] = str(self.next_id)
            self.next_id += 1
        self.documents.append(doc)

        class InsertResult:
            def __init__(self, inserted_id):
                self.inserted_id = inserted_id

        return InsertResult(doc["_id"])

    async def update_one(self, filter_dict: dict, update_dict: dict):
        """Update one document"""
        for i, doc in enumerate(self.documents):
            if self._matches_filter(doc, filter_dict):
                if "$set" in update_dict:
                    doc.update(update_dict["$set"])
                else:
                    doc.update(update_dict)
                return True
        return False

    async def delete_one(self, filter_dict: dict):
        """Delete first matching document"""
        for i, doc in enumerate(self.documents):
            if self._matches_filter(doc, filter_dict):
                del self.documents[i]

                class DeleteResult:
                    def __init__(self, deleted_count):
                        self.deleted_count = deleted_count

                return DeleteResult(1)

        class DeleteResult:
            def __init__(self, deleted_count):
                self.deleted_count = deleted_count

        return DeleteResult(0)

    async def count_documents(self, filter_dict: dict):
        """Count documents matching filter"""
        count = 0
        for doc in self.documents:
            if self._matches_filter(doc, filter_dict):
                count += 1
        return count

    async def distinct(self, key: str):
        """Return distinct values for a top-level key (subset of Mongo behavior)."""
        values = []
        seen = set()
        for doc in self.documents:
            if key in doc:
                val = doc[key]
                # Use hashable representation
                try:
                    marker = (key, val)
                except Exception:
                    marker = (key, str(val))
                if marker not in seen:
                    seen.add(marker)
                    values.append(val)
        return values

    async def delete_many(self, filter_dict: dict):
        """Delete all matching documents"""
        remaining = []
        deleted = 0
        for doc in self.documents:
            if self._matches_filter(doc, filter_dict):
                deleted += 1
            else:
                remaining.append(doc)
        self.documents = remaining

        class DeleteResult:
            def __init__(self, deleted_count):
                self.deleted_count = deleted_count

        return DeleteResult(deleted)

    async def create_index(self, keys, **kwargs):
        """Simulate index creation (no-op)"""
        logger.debug(f"Simulated index creation on {self.name}: {keys}")
        return True

    def _matches_filter(self, document: dict, filter_dict: dict) -> bool:
        """Simple filter matching"""
        if not filter_dict:
            return True

        for key, value in filter_dict.items():
            if key not in document:
                return False
            doc_value = document[key]
            if isinstance(value, dict):
                if not self._apply_operators(doc_value, value):
                    return False
            else:
                if doc_value != value:
                    return False
        return True

    # --- Cursor emulation -------------------------------------------------------
    class _Cursor:
        def __init__(self, docs):
            self._docs = list(docs)
            self._skip = 0
            self._limit = None

        def sort(self, key, direction=1):
            reverse = direction == -1
            try:
                self._docs.sort(key=lambda d: d.get(key), reverse=reverse)
            except Exception:
                pass
            return self

        def skip(self, n: int):
            self._skip = max(0, int(n))
            return self

        def limit(self, n: int):
            self._limit = None if n is None else int(n)
            return self

        async def to_list(self, length=None):
            start = self._skip
            if self._limit is not None:
                end = start + self._limit
            elif length is not None and length >= 0:
                end = start + length
            else:
                end = None
            return self._docs[start:end]

    def find(self, filter_dict: dict):
        """Return a simple cursor over matching docs"""
        matched = [
            doc for doc in self.documents if self._matches_filter(doc, filter_dict)
        ]
        return InMemoryCollection._Cursor(matched)


class DatabaseManager:
    """MongoDB database manager with connection pooling"""

    def __init__(self):
        self._client: Optional[AsyncIOMotorClient] = None
        self._database: Optional[AsyncIOMotorDatabase] = None
        self._settings = get_settings()

    async def connect(self) -> None:
        """Establish connection to database (MongoDB or fallback to SQLite simulation)"""
        if self._client is not None:
            return

        if not self._settings.USE_MONGODB:
            # Use SQLite simulation mode
            logger.info("Using SQLite simulation mode (no MongoDB required)")
            self._database = SQLiteSimulation(self._settings.DATABASE_NAME)
            return

        try:
            # Enhanced MongoDB connection with security configurations
            connection_options = {
                # Allow more time for Atlas/replica set discovery
                "serverSelectionTimeoutMS": 10000,
                "connectTimeoutMS": 10000,
                "socketTimeoutMS": 30000,
                "maxPoolSize": 20,
                "minPoolSize": 2,
                "maxIdleTimeMS": 60000,
                "heartbeatFrequencyMS": 15000,
                "retryWrites": True,
                "retryReads": True,
                # Connection stability improvements
                "maxConnecting": 5,
                "waitQueueTimeoutMS": 10000,
            }

            # Add SSL/TLS configuration for production
            if self._settings.ENVIRONMENT == "production":
                connection_options.update(
                    {
                        "tls": True,
                        "tlsAllowInvalidCertificates": False,
                        "tlsAllowInvalidHostnames": False,
                    }
                )
                logger.info("MongoDB connection configured with SSL/TLS for production")

            # Check if authentication is already in URL
            import urllib.parse

            parsed_url = urllib.parse.urlparse(self._settings.MONGODB_URL)
            has_auth_in_url = parsed_url.username and parsed_url.password
            has_auth_source_in_url = "authSource=" in self._settings.MONGODB_URL

            # Only add auth options if not already in URL
            if has_auth_in_url and not has_auth_source_in_url:
                connection_options.update(
                    {
                        "authSource": getattr(
                            self._settings, "MONGODB_AUTH_SOURCE", "admin"
                        ),
                        "authMechanism": getattr(
                            self._settings, "MONGODB_AUTH_MECHANISM", "SCRAM-SHA-256"
                        ),
                    }
                )
                logger.info("MongoDB connection configured with authentication options")
            elif has_auth_in_url:
                logger.info("MongoDB connection using authentication from URL")

            self._client = AsyncIOMotorClient(
                self._settings.MONGODB_URL, **connection_options
            )

            # Test connection
            await self._client.admin.command("ping")

            self._database = self._client[self._settings.DATABASE_NAME]

            # Create indexes in the background
            asyncio.create_task(self._create_indexes())

            logger.info(f"Connected to MongoDB: {self._settings.DATABASE_NAME}")

        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.warning(f"Failed to connect to MongoDB: {e}")
            logger.info("Falling back to SQLite simulation mode")
            self._database = SQLiteSimulation(self._settings.DATABASE_NAME)
            self._client = None
        except Exception as e:
            # Catch broad exceptions such as DNS resolution failures from dnspython
            logger.warning(f"MongoDB connection error (non-fatal): {e}")
            logger.info("Falling back to SQLite simulation mode")
            self._database = SQLiteSimulation(self._settings.DATABASE_NAME)
            self._client = None

    async def disconnect(self) -> None:
        """Close MongoDB connection"""
        if self._client:
            self._client.close()
            self._client = None
            self._database = None
            logger.info("Disconnected from MongoDB")

    @property
    def database(self) -> AsyncIOMotorDatabase:
        """Get database instance"""
        if self._database is None:
            raise RuntimeError("Database not connected. Call connect() first.")
        return self._database

    def get_collection(self, name: str) -> AsyncIOMotorCollection:
        """Get collection by name"""
        return self.database[name]

    async def health_check(self) -> bool:
        """Check database connection health"""
        try:
            if self._client is None:
                return False

            # Ping with timeout
            await asyncio.wait_for(self._client.admin.command("ping"), timeout=5.0)
            return True
        except Exception as e:
            logger.warning(f"Database health check failed: {e}")
            return False

    async def _create_indexes(self) -> None:
        """Create database indexes for optimal performance"""
        try:
            # Users collection indexes
            users = self.get_collection("users")
            await users.create_index("telegram_id", unique=True)
            await users.create_index("username")
            await users.create_index("created_at")

            # Wallets collection indexes
            wallets = self.get_collection("wallets")
            await wallets.create_index("user_id", unique=True)
            await wallets.create_index("updated_at")

            # Purchases collection indexes
            purchases = self.get_collection("purchases")
            await purchases.create_index("user_id")
            await purchases.create_index("created_at")
            await purchases.create_index([("user_id", 1), ("created_at", -1)])
            # Create idempotency_key as unique + sparse, reconcile conflicts if index exists without sparse/unique
            try:
                await purchases.create_index(
                    "idempotency_key",
                    unique=True,
                    sparse=True,
                    name="idempotency_key_1",
                )
            except OperationFailure as ie:
                if getattr(ie, "code", None) == 86:  # IndexKeySpecsConflict
                    try:
                        indexes = await purchases.list_indexes().to_list(None)
                        for idx in indexes:
                            if idx.get("name") == "idempotency_key_1":
                                if not idx.get("sparse", False) or not idx.get(
                                    "unique", False
                                ):
                                    await purchases.drop_index(idx["name"])
                                    await purchases.create_index(
                                        "idempotency_key",
                                        unique=True,
                                        sparse=True,
                                        name="idempotency_key_1",
                                    )
                                break
                    except Exception as fix_err:
                        logger.warning(
                            f"Could not reconcile purchases.idempotency_key index: {fix_err}"
                        )
                else:
                    raise
            await purchases.create_index("api_req_id", sparse=True)

            # Transactions collection indexes
            transactions = self.get_collection("transactions")
            await transactions.create_index("user_id")
            await transactions.create_index("created_at")
            await transactions.create_index(
                [("user_id", 1), ("type", 1), ("created_at", -1)]
            )
            # Ensure unique + sparse index on hash. Resolve existing conflicting index if present.
            try:
                await transactions.create_index(
                    "hash", unique=True, sparse=True, name="hash_1"
                )
            except OperationFailure as ie:
                # Handle IndexKeySpecsConflict when an existing non-sparse index named hash_1 exists
                if getattr(ie, "code", None) == 86:
                    try:
                        indexes = await transactions.list_indexes().to_list(None)
                        for idx in indexes:
                            # Identify index on {hash:1}
                            key = idx.get("key") or {}
                            # key can be an OrderedDict-like; normalize to list of tuples
                            items = (
                                list(getattr(key, "items", lambda: key.items())())
                                if hasattr(key, "items")
                                else []
                            )
                            if idx.get("name") == "hash_1" or items == [("hash", 1)]:
                                # If not sparse or not unique, drop and recreate
                                if not idx.get("sparse", False) or not idx.get(
                                    "unique", False
                                ):
                                    await transactions.drop_index(idx["name"])
                                    await transactions.create_index(
                                        "hash", unique=True, sparse=True, name="hash_1"
                                    )
                                break
                    except Exception as fix_err:
                        logger.warning(
                            f"Could not reconcile transactions.hash index: {fix_err}"
                        )
                else:
                    raise

            # Cards collection indexes
            cards = self.get_collection("cards")
            await cards.create_index("purchase_id")
            await cards.create_index("created_at")

            # Audit logs collection indexes
            audit_logs = self.get_collection("audit_logs")
            await audit_logs.create_index("actor_id")
            await audit_logs.create_index("created_at")
            await audit_logs.create_index("action")
            await audit_logs.create_index("hash", unique=True)

            # Catalog items collection indexes
            catalog_items = self.get_collection("catalog_items")
            await catalog_items.create_index("sku", unique=True)
            await catalog_items.create_index("active")
            await catalog_items.create_index([("active", 1), ("price", 1)])
            await catalog_items.create_index("country")
            await catalog_items.create_index("brand")
            await catalog_items.create_index("type")
            await catalog_items.create_index("bin")

            # Saved filters collection indexes
            saved_filters = self.get_collection("saved_filters")
            await saved_filters.create_index("user_id")
            await saved_filters.create_index("created_at")

            # App settings collection indexes
            app_settings = self.get_collection("app_settings")
            await app_settings.create_index("key", unique=True)

            # API Management collection indexes
            api_configurations = self.get_collection("api_configurations")
            await api_configurations.create_index("name")
            await api_configurations.create_index("status")
            await api_configurations.create_index("environment")
            await api_configurations.create_index("created_by")
            await api_configurations.create_index("created_at")
            await api_configurations.create_index("is_deleted")

            api_health_status = self.get_collection("api_health_status")
            await api_health_status.create_index("api_config_id", unique=True)
            await api_health_status.create_index("is_healthy")
            await api_health_status.create_index("last_check_at")
            await api_health_status.create_index("next_check_at")

            api_usage_metrics = self.get_collection("api_usage_metrics")
            await api_usage_metrics.create_index("api_config_id")
            await api_usage_metrics.create_index("period_type")
            await api_usage_metrics.create_index("period_start")
            await api_usage_metrics.create_index(
                [("api_config_id", 1), ("period_start", -1)]
            )

            api_request_logs = self.get_collection("api_request_logs")
            await api_request_logs.create_index("api_config_id")
            await api_request_logs.create_index("created_at")
            await api_request_logs.create_index("status_code")
            await api_request_logs.create_index(
                [("api_config_id", 1), ("created_at", -1)]
            )

            api_credentials = self.get_collection("api_credentials")
            await api_credentials.create_index("api_config_id")
            await api_credentials.create_index("environment")
            await api_credentials.create_index("is_active")
            await api_credentials.create_index("expires_at")

            api_audit_logs = self.get_collection("api_audit_logs")
            await api_audit_logs.create_index("resource_type")
            await api_audit_logs.create_index("resource_id")
            await api_audit_logs.create_index("actor_id")
            await api_audit_logs.create_index("timestamp")
            await api_audit_logs.create_index("operation")

            # Checkout jobs collection indexes (prevent duplicate active jobs)
            checkout_jobs = self.get_collection("checkout_jobs")
            await checkout_jobs.create_index("created_at")
            await checkout_jobs.create_index("status")
            # Unique idempotency per user for active jobs (queued or processing)
            try:
                await checkout_jobs.create_index(
                    [("user_id", 1), ("idempotency_key", 1)],
                    unique=True,
                    name="uniq_active_idem_per_user",
                    partialFilterExpression={
                        "status": {"$in": ["queued", "processing"]}
                    },
                )
            except OperationFailure as ie:
                # Best effort: if index exists with incompatible options, drop and recreate
                if getattr(ie, "code", None) == 86:  # IndexKeySpecsConflict
                    try:
                        idxs = await checkout_jobs.list_indexes().to_list(None)
                        for idx in idxs:
                            if idx.get("name") == "uniq_active_idem_per_user":
                                await checkout_jobs.drop_index(idx["name"])
                                await checkout_jobs.create_index(
                                    [("user_id", 1), ("idempotency_key", 1)],
                                    unique=True,
                                    name="uniq_active_idem_per_user",
                                    partialFilterExpression={
                                        "status": {"$in": ["queued", "processing"]}
                                    },
                                )
                                break
                    except Exception as fix_err:
                        logger.warning(
                            f"Could not reconcile checkout_jobs unique index: {fix_err}"
                        )
                else:
                    raise

            logger.info("Database indexes created successfully")

        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
            raise


# Global database manager instance
db_manager = DatabaseManager()


async def init_database() -> None:
    """Initialize database connection"""
    await db_manager.connect()


async def close_database() -> None:
    """Close database connection"""
    await db_manager.disconnect()


def get_database() -> AsyncIOMotorDatabase:
    """Get database instance"""
    return db_manager.database


def get_collection(name: str) -> AsyncIOMotorCollection:
    """Get collection by name"""
    return db_manager.get_collection(name)


@asynccontextmanager
async def database_transaction() -> AsyncGenerator[AsyncIOMotorDatabase, None]:
    """
    Context manager for database operations
    Note: MongoDB doesn't have traditional transactions like SQL databases,
    but this provides a consistent interface for database operations
    """
    try:
        yield get_database()
    except Exception as e:
        logger.error(f"Database operation failed: {e}")
        raise
