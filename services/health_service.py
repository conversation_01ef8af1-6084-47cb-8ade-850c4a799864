"""
Health monitoring service for the Demo Wallet Bot
"""

from __future__ import annotations

import asyncio
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
import logging

from database.connection import get_database, get_collection
from config.settings import get_settings
from utils.performance import perf_monitor

logger = logging.getLogger(__name__)


class HealthService:
    """Service for monitoring system health and performance"""

    def __init__(self):
        self.settings = get_settings()
        self.start_time = time.time()

    async def get_system_health(self) -> Dict[str, Any]:
        """
        Get comprehensive system health status
        
        Returns:
            Dictionary with health metrics
        """
        try:
            health_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "uptime_seconds": time.time() - self.start_time,
                "database": await self._check_database_health(),
                "performance": await self._get_performance_metrics(),
                "memory": await self._get_memory_usage(),
                "status": "healthy"
            }
            
            # Determine overall health status
            if not health_data["database"]["connected"]:
                health_data["status"] = "unhealthy"
            elif health_data["performance"]["avg_response_time"] > 2.0:
                health_data["status"] = "degraded"
            
            return health_data
            
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "status": "error",
                "error": str(e)
            }

    async def _check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            db = get_database()
            start_time = time.time()
            
            # Test basic connectivity
            await db.command("ping")
            ping_time = time.time() - start_time
            
            # Test collection access
            users_collection = get_collection("users")
            user_count = await users_collection.count_documents({})
            
            return {
                "connected": True,
                "ping_time_ms": round(ping_time * 1000, 2),
                "user_count": user_count,
                "type": "MongoDB" if hasattr(db, "command") else "SQLite_Simulation"
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "connected": False,
                "error": str(e),
                "type": "unknown"
            }

    async def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics from the performance monitor"""
        try:
            stats = perf_monitor.get_stats()
            
            # Calculate overall metrics
            all_times = []
            for operation_stats in stats.values():
                all_times.extend([operation_stats.get("avg_time", 0)])
            
            avg_response_time = sum(all_times) / len(all_times) if all_times else 0
            
            return {
                "avg_response_time": round(avg_response_time, 3),
                "operations_tracked": len(stats),
                "detailed_stats": stats
            }
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {"error": str(e)}

    async def _get_memory_usage(self) -> Dict[str, Any]:
        """Get basic memory usage information"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            return {
                "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
                "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
                "cpu_percent": process.cpu_percent()
            }
            
        except ImportError:
            # psutil not available, return basic info
            return {"available": False, "reason": "psutil not installed"}
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return {"error": str(e)}

    async def run_health_checks(self) -> Dict[str, Any]:
        """
        Run comprehensive health checks
        
        Returns:
            Dictionary with detailed health check results
        """
        try:
            checks = {
                "database_connectivity": await self._test_database_operations(),
                "model_validation": await self._test_model_validation(),
                "service_availability": await self._test_service_availability(),
                "configuration": await self._test_configuration()
            }
            
            # Count passed/failed checks
            passed = sum(1 for check in checks.values() if check.get("status") == "pass")
            total = len(checks)
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "summary": {
                    "passed": passed,
                    "total": total,
                    "success_rate": round((passed / total) * 100, 1) if total > 0 else 0
                },
                "checks": checks
            }
            
        except Exception as e:
            logger.error(f"Error running health checks: {e}")
            return {"error": str(e)}

    async def _test_database_operations(self) -> Dict[str, Any]:
        """Test basic database operations"""
        try:
            users_collection = get_collection("users")
            
            # Test read operation
            await users_collection.count_documents({})
            
            # Test write operation (insert and delete a test document)
            test_doc = {"test": True, "created_at": datetime.utcnow()}
            result = await users_collection.insert_one(test_doc)
            await users_collection.delete_one({"_id": result.inserted_id})
            
            return {"status": "pass", "message": "Database operations working"}
            
        except Exception as e:
            return {"status": "fail", "message": f"Database test failed: {e}"}

    async def _test_model_validation(self) -> Dict[str, Any]:
        """Test model validation"""
        try:
            from models import User, Wallet, Transaction
            
            # Test User model
            user = User(
                telegram_id=123456789,
                username="test_user",
                first_name="Test",
                role="user"
            )
            
            # Test Wallet model
            wallet = Wallet(
                user_id="test_id",
                balance=100.0,
                currency="USD"
            )
            
            return {"status": "pass", "message": "Model validation working"}
            
        except Exception as e:
            return {"status": "fail", "message": f"Model validation failed: {e}"}

    async def _test_service_availability(self) -> Dict[str, Any]:
        """Test service availability"""
        try:
            from services.user_service import UserService
            
            service = UserService()
            # Test service initialization
            if service.users_collection is None:
                raise Exception("Service not properly initialized")
            
            return {"status": "pass", "message": "Services available"}
            
        except Exception as e:
            return {"status": "fail", "message": f"Service test failed: {e}"}

    async def _test_configuration(self) -> Dict[str, Any]:
        """Test configuration validity"""
        try:
            settings = get_settings()
            
            # Check required settings
            required_fields = ["BOT_TOKEN", "DATABASE_NAME", "LOG_LEVEL"]
            missing_fields = []
            
            for field in required_fields:
                if not getattr(settings, field, None):
                    missing_fields.append(field)
            
            if missing_fields:
                return {
                    "status": "fail", 
                    "message": f"Missing configuration: {', '.join(missing_fields)}"
                }
            
            return {"status": "pass", "message": "Configuration valid"}
            
        except Exception as e:
            return {"status": "fail", "message": f"Configuration test failed: {e}"}
