"""
API Security Service for role-based access control, credential management,
and security auditing for API management system.
"""

from __future__ import annotations

import hashlib
import logging
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any
from enum import Enum

from models.api import APIAuditLog, APICredential, AuthenticationType, APIEnvironment
from models.base import now_utc
from database.connection import get_collection
from api_v1.utils.encryption import EncryptionService
from config.settings import get_settings

logger = logging.getLogger(__name__)


class APIPermission(str, Enum):
    """API management permissions"""
    VIEW = "apis:view"
    CREATE = "apis:create"
    EDIT = "apis:edit"
    DELETE = "apis:delete"
    TEST = "apis:test"
    EXPORT = "apis:export"
    IMPORT = "apis:import"
    MANAGE_CREDENTIALS = "apis:credentials"
    VIEW_ANALYTICS = "apis:analytics"
    MANAGE_HEALTH = "apis:health"
    ADMIN = "apis:admin"


class APIRole(str, Enum):
    """Predefined API management roles"""
    VIEWER = "api_viewer"
    OPERATOR = "api_operator"
    ADMIN = "api_admin"
    SUPER_ADMIN = "api_super_admin"


# Role to permissions mapping
ROLE_PERMISSIONS: Dict[APIRole, Set[APIPermission]] = {
    APIRole.VIEWER: {
        APIPermission.VIEW,
        APIPermission.VIEW_ANALYTICS,
    },
    APIRole.OPERATOR: {
        APIPermission.VIEW,
        APIPermission.TEST,
        APIPermission.VIEW_ANALYTICS,
        APIPermission.MANAGE_HEALTH,
    },
    APIRole.ADMIN: {
        APIPermission.VIEW,
        APIPermission.CREATE,
        APIPermission.EDIT,
        APIPermission.TEST,
        APIPermission.EXPORT,
        APIPermission.IMPORT,
        APIPermission.VIEW_ANALYTICS,
        APIPermission.MANAGE_HEALTH,
        APIPermission.MANAGE_CREDENTIALS,
    },
    APIRole.SUPER_ADMIN: {
        APIPermission.VIEW,
        APIPermission.CREATE,
        APIPermission.EDIT,
        APIPermission.DELETE,
        APIPermission.TEST,
        APIPermission.EXPORT,
        APIPermission.IMPORT,
        APIPermission.VIEW_ANALYTICS,
        APIPermission.MANAGE_HEALTH,
        APIPermission.MANAGE_CREDENTIALS,
        APIPermission.ADMIN,
    },
}


class APISecurityService:
    """Service for API management security and access control"""
    
    def __init__(self):
        self.settings = get_settings()
        self.encryption = EncryptionService()
        self.user_roles = get_collection("user_api_roles")
        self.api_credentials = get_collection("api_credentials")
        self.api_audit = get_collection("api_audit_logs")
        self.security_events = get_collection("api_security_events")
    
    async def check_permission(self, user_id: str, permission: APIPermission) -> bool:
        """Check if user has specific API management permission"""
        try:
            # Super admins (from settings) have all permissions
            if int(user_id) in self.settings.admin_ids:
                return True
            
            # Get user's API roles
            user_roles = await self.get_user_roles(user_id)
            
            # Check if any role grants the permission
            for role in user_roles:
                if permission in ROLE_PERMISSIONS.get(role, set()):
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking permission for user {user_id}: {e}")
            return False
    
    async def get_user_roles(self, user_id: str) -> List[APIRole]:
        """Get API management roles for a user"""
        try:
            doc = await self.user_roles.find_one({"user_id": user_id})
            if not doc:
                return []
            
            roles = doc.get("roles", [])
            return [APIRole(role) for role in roles if role in [r.value for r in APIRole]]
            
        except Exception as e:
            logger.error(f"Error getting user roles for {user_id}: {e}")
            return []
    
    async def assign_role(self, user_id: str, role: APIRole, assigned_by: str) -> bool:
        """Assign API management role to user"""
        try:
            # Check if assigner has admin permission
            if not await self.check_permission(assigned_by, APIPermission.ADMIN):
                raise PermissionError("Insufficient permissions to assign roles")
            
            # Get current roles
            current_roles = await self.get_user_roles(user_id)
            
            # Add new role if not already present
            if role not in current_roles:
                current_roles.append(role)
                
                await self.user_roles.update_one(
                    {"user_id": user_id},
                    {
                        "$set": {
                            "roles": [r.value for r in current_roles],
                            "updated_at": now_utc(),
                            "updated_by": assigned_by
                        }
                    },
                    upsert=True
                )
                
                # Log the role assignment
                await self._log_security_event(
                    event_type="role_assigned",
                    user_id=user_id,
                    actor_id=assigned_by,
                    details={"role": role.value},
                    success=True
                )
                
                logger.info(f"Assigned role {role.value} to user {user_id}")
                return True
            
            return False  # Role already assigned
            
        except Exception as e:
            logger.error(f"Error assigning role {role.value} to user {user_id}: {e}")
            await self._log_security_event(
                event_type="role_assignment_failed",
                user_id=user_id,
                actor_id=assigned_by,
                details={"role": role.value, "error": str(e)},
                success=False
            )
            return False
    
    async def revoke_role(self, user_id: str, role: APIRole, revoked_by: str) -> bool:
        """Revoke API management role from user"""
        try:
            # Check if revoker has admin permission
            if not await self.check_permission(revoked_by, APIPermission.ADMIN):
                raise PermissionError("Insufficient permissions to revoke roles")
            
            # Get current roles
            current_roles = await self.get_user_roles(user_id)
            
            # Remove role if present
            if role in current_roles:
                current_roles.remove(role)
                
                await self.user_roles.update_one(
                    {"user_id": user_id},
                    {
                        "$set": {
                            "roles": [r.value for r in current_roles],
                            "updated_at": now_utc(),
                            "updated_by": revoked_by
                        }
                    }
                )
                
                # Log the role revocation
                await self._log_security_event(
                    event_type="role_revoked",
                    user_id=user_id,
                    actor_id=revoked_by,
                    details={"role": role.value},
                    success=True
                )
                
                logger.info(f"Revoked role {role.value} from user {user_id}")
                return True
            
            return False  # Role not assigned
            
        except Exception as e:
            logger.error(f"Error revoking role {role.value} from user {user_id}: {e}")
            await self._log_security_event(
                event_type="role_revocation_failed",
                user_id=user_id,
                actor_id=revoked_by,
                details={"role": role.value, "error": str(e)},
                success=False
            )
            return False
    
    async def create_secure_credential(
        self,
        api_config_id: str,
        credential_type: AuthenticationType,
        credential_data: Dict[str, Any],
        environment: APIEnvironment,
        created_by: str,
        name: str,
        description: Optional[str] = None,
        expires_at: Optional[datetime] = None
    ) -> Optional[str]:
        """Create securely encrypted API credential"""
        try:
            # Check permissions
            if not await self.check_permission(created_by, APIPermission.MANAGE_CREDENTIALS):
                raise PermissionError("Insufficient permissions to manage credentials")
            
            # Encrypt the credential data
            encrypted_data = self.encryption.encrypt(json.dumps(credential_data))
            encryption_key_id = self._generate_key_id()
            
            # Create credential document
            credential = APICredential(
                api_config_id=api_config_id,
                credential_type=credential_type,
                name=name,
                description=description,
                encrypted_data=encrypted_data,
                encryption_key_id=encryption_key_id,
                environment=environment,
                expires_at=expires_at,
                created_by=created_by,
                created_at=now_utc()
            )
            
            # Insert into database
            result = await self.api_credentials.insert_one(credential.to_mongo())
            credential_id = str(result.inserted_id)
            
            # Log credential creation
            await self._log_security_event(
                event_type="credential_created",
                user_id=created_by,
                actor_id=created_by,
                details={
                    "credential_id": credential_id,
                    "api_config_id": api_config_id,
                    "credential_type": credential_type.value,
                    "environment": environment.value
                },
                success=True
            )
            
            logger.info(f"Created secure credential for API {api_config_id}")
            return credential_id
            
        except Exception as e:
            logger.error(f"Error creating secure credential: {e}")
            await self._log_security_event(
                event_type="credential_creation_failed",
                user_id=created_by,
                actor_id=created_by,
                details={"error": str(e)},
                success=False
            )
            return None
    
    async def get_credential(self, credential_id: str, requested_by: str) -> Optional[Dict[str, Any]]:
        """Get and decrypt credential data"""
        try:
            # Check permissions
            if not await self.check_permission(requested_by, APIPermission.MANAGE_CREDENTIALS):
                raise PermissionError("Insufficient permissions to access credentials")
            
            doc = await self.api_credentials.find_one({
                "_id": credential_id,
                "is_deleted": {"$ne": True}
            })
            
            if not doc:
                return None
            
            credential = APICredential.from_mongo(doc)
            
            # Check if credential is expired
            if credential.is_expired():
                logger.warning(f"Attempted to access expired credential: {credential_id}")
                return None
            
            # Decrypt credential data
            decrypted_data = json.loads(self.encryption.decrypt(credential.encrypted_data))
            
            # Update last used timestamp
            await self.api_credentials.update_one(
                {"_id": credential_id},
                {"$set": {"last_used_at": now_utc()}}
            )
            
            # Log credential access
            await self._log_security_event(
                event_type="credential_accessed",
                user_id=requested_by,
                actor_id=requested_by,
                details={"credential_id": credential_id},
                success=True
            )
            
            return {
                "id": credential_id,
                "name": credential.name,
                "type": credential.credential_type.value,
                "environment": credential.environment.value,
                "data": decrypted_data,
                "expires_at": credential.expires_at,
                "last_used_at": credential.last_used_at
            }
            
        except Exception as e:
            logger.error(f"Error getting credential {credential_id}: {e}")
            await self._log_security_event(
                event_type="credential_access_failed",
                user_id=requested_by,
                actor_id=requested_by,
                details={"credential_id": credential_id, "error": str(e)},
                success=False
            )
            return None
    
    async def rotate_credential(self, credential_id: str, new_data: Dict[str, Any], rotated_by: str) -> bool:
        """Rotate (update) credential with new data"""
        try:
            # Check permissions
            if not await self.check_permission(rotated_by, APIPermission.MANAGE_CREDENTIALS):
                raise PermissionError("Insufficient permissions to rotate credentials")
            
            # Get existing credential
            doc = await self.api_credentials.find_one({"_id": credential_id})
            if not doc:
                return False
            
            # Encrypt new data
            encrypted_data = self.encryption.encrypt(json.dumps(new_data))
            new_key_id = self._generate_key_id()
            
            # Update credential
            result = await self.api_credentials.update_one(
                {"_id": credential_id},
                {
                    "$set": {
                        "encrypted_data": encrypted_data,
                        "encryption_key_id": new_key_id,
                        "last_modified_by": rotated_by,
                        "updated_at": now_utc()
                    }
                }
            )
            
            if result.modified_count > 0:
                # Log credential rotation
                await self._log_security_event(
                    event_type="credential_rotated",
                    user_id=rotated_by,
                    actor_id=rotated_by,
                    details={"credential_id": credential_id},
                    success=True
                )
                
                logger.info(f"Rotated credential: {credential_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error rotating credential {credential_id}: {e}")
            await self._log_security_event(
                event_type="credential_rotation_failed",
                user_id=rotated_by,
                actor_id=rotated_by,
                details={"credential_id": credential_id, "error": str(e)},
                success=False
            )
            return False
    
    async def audit_user_activity(
        self, 
        user_id: str, 
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """Get audit trail for user's API management activities"""
        try:
            end_date = now_utc()
            start_date = end_date - timedelta(days=days)
            
            cursor = self.api_audit.find({
                "actor_id": user_id,
                "timestamp": {"$gte": start_date, "$lte": end_date}
            }).sort("timestamp", -1)
            
            audit_docs = await cursor.to_list(None)
            
            activities = []
            for doc in audit_docs:
                activities.append({
                    "timestamp": doc["timestamp"],
                    "operation": doc["operation"],
                    "resource_type": doc["resource_type"],
                    "resource_id": doc["resource_id"],
                    "success": doc.get("success", True),
                    "error_message": doc.get("error_message"),
                    "changes": doc.get("changes", {}),
                    "session_id": doc.get("session_id"),
                    "user_agent": doc.get("user_agent")
                })
            
            return activities
            
        except Exception as e:
            logger.error(f"Error getting audit trail for user {user_id}: {e}")
            return []
    
    async def detect_suspicious_activity(self, user_id: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Detect potentially suspicious API management activities"""
        try:
            end_date = now_utc()
            start_date = end_date - timedelta(hours=hours)
            
            # Get recent activities
            cursor = self.api_audit.find({
                "actor_id": user_id,
                "timestamp": {"$gte": start_date, "$lte": end_date}
            }).sort("timestamp", -1)
            
            activities = await cursor.to_list(None)
            
            suspicious_patterns = []
            
            # Pattern 1: Too many failed operations
            failed_ops = [a for a in activities if not a.get("success", True)]
            if len(failed_ops) > 10:
                suspicious_patterns.append({
                    "type": "excessive_failures",
                    "description": f"User has {len(failed_ops)} failed operations in {hours} hours",
                    "severity": "medium",
                    "count": len(failed_ops)
                })
            
            # Pattern 2: Rapid credential access
            credential_accesses = [a for a in activities if a.get("operation") == "credential_accessed"]
            if len(credential_accesses) > 20:
                suspicious_patterns.append({
                    "type": "excessive_credential_access",
                    "description": f"User accessed credentials {len(credential_accesses)} times in {hours} hours",
                    "severity": "high",
                    "count": len(credential_accesses)
                })
            
            # Pattern 3: Bulk deletions
            delete_ops = [a for a in activities if a.get("operation") == "delete"]
            if len(delete_ops) > 5:
                suspicious_patterns.append({
                    "type": "bulk_deletions",
                    "description": f"User performed {len(delete_ops)} delete operations in {hours} hours",
                    "severity": "high",
                    "count": len(delete_ops)
                })
            
            # Pattern 4: Off-hours activity (if configured)
            off_hours_activities = []
            for activity in activities:
                hour = activity["timestamp"].hour
                if hour < 6 or hour > 22:  # Outside 6 AM - 10 PM
                    off_hours_activities.append(activity)
            
            if len(off_hours_activities) > 5:
                suspicious_patterns.append({
                    "type": "off_hours_activity",
                    "description": f"User has {len(off_hours_activities)} activities outside normal hours",
                    "severity": "low",
                    "count": len(off_hours_activities)
                })
            
            return suspicious_patterns
            
        except Exception as e:
            logger.error(f"Error detecting suspicious activity for user {user_id}: {e}")
            return []
    
    async def generate_security_report(self, days: int = 30) -> Dict[str, Any]:
        """Generate comprehensive security report"""
        try:
            end_date = now_utc()
            start_date = end_date - timedelta(days=days)
            
            # Get all audit logs for the period
            cursor = self.api_audit.find({
                "timestamp": {"$gte": start_date, "$lte": end_date}
            })
            
            audit_logs = await cursor.to_list(None)
            
            # Analyze security metrics
            total_operations = len(audit_logs)
            failed_operations = sum(1 for log in audit_logs if not log.get("success", True))
            unique_users = len(set(log["actor_id"] for log in audit_logs))
            
            # Operation breakdown
            operation_counts = defaultdict(int)
            for log in audit_logs:
                operation_counts[log["operation"]] += 1
            
            # User activity breakdown
            user_activity = defaultdict(int)
            for log in audit_logs:
                user_activity[log["actor_id"]] += 1
            
            # Most active users
            top_users = sorted(user_activity.items(), key=lambda x: x[1], reverse=True)[:10]
            
            # Failed operations by type
            failed_by_operation = defaultdict(int)
            for log in audit_logs:
                if not log.get("success", True):
                    failed_by_operation[log["operation"]] += 1
            
            # Get security events
            security_cursor = self.security_events.find({
                "timestamp": {"$gte": start_date, "$lte": end_date}
            })
            security_events = await security_cursor.to_list(None)
            
            return {
                "report_metadata": {
                    "generated_at": now_utc(),
                    "period_start": start_date,
                    "period_end": end_date,
                    "period_days": days
                },
                "overview": {
                    "total_operations": total_operations,
                    "failed_operations": failed_operations,
                    "success_rate": ((total_operations - failed_operations) / total_operations * 100) if total_operations > 0 else 0,
                    "unique_users": unique_users,
                    "security_events": len(security_events)
                },
                "operation_breakdown": dict(operation_counts),
                "failed_operations": dict(failed_by_operation),
                "top_users": top_users,
                "security_events": [
                    {
                        "timestamp": event["timestamp"],
                        "event_type": event["event_type"],
                        "user_id": event["user_id"],
                        "severity": event.get("severity", "unknown"),
                        "details": event.get("details", {})
                    }
                    for event in security_events
                ]
            }
            
        except Exception as e:
            logger.error(f"Error generating security report: {e}")
            return {"error": str(e)}
    
    def _generate_key_id(self) -> str:
        """Generate unique encryption key ID"""
        return hashlib.sha256(
            f"{now_utc().isoformat()}{secrets.token_hex(16)}".encode()
        ).hexdigest()[:16]
    
    async def _log_security_event(
        self,
        event_type: str,
        user_id: str,
        actor_id: str,
        details: Dict[str, Any],
        success: bool,
        severity: str = "info"
    ) -> None:
        """Log security-related events"""
        try:
            event = {
                "event_type": event_type,
                "user_id": user_id,
                "actor_id": actor_id,
                "details": details,
                "success": success,
                "severity": severity,
                "timestamp": now_utc()
            }
            
            await self.security_events.insert_one(event)
            
        except Exception as e:
            logger.error(f"Failed to log security event: {e}")
# Note: Avoid creating global instances at import time to prevent
# side effects during test collection or when DB is not connected.
# Create APISecurityService() explicitly where needed.
