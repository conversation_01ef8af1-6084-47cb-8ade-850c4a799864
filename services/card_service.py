"""
Card service for fetching and managing card data from external API
Enhanced with comprehensive API request/response logging for 403 error diagnosis
"""

from __future__ import annotations

import logging
from typing import Dict, Any, Optional
import asyncio

from config.settings import get_settings
from utils.performance import monitor_performance
from utils.api_logging import get_api_logger, LogLevel
from services.external_api_service import (
    ExternalAPIService,
    get_external_api_service,
    ListItemsParams,
)

logger = logging.getLogger(__name__)
api_logger = get_api_logger("card_service", LogLevel.DEBUG)


class CardService:
    """Service for fetching card data from external API"""

    def __init__(self, external_api_service: Optional[ExternalAPIService] = None):
        self.settings = get_settings()
        # Allow callers to inject a pre-configured API client (e.g., per-user selection)
        self.external_api = external_api_service or get_external_api_service()

        # No local headers/cookies; HTTP/auth handled by external_api_service

    async def close(self) -> None:
        """Nothing to close; external_api_service manages HTTP session"""
        return None

    def _apply_filters_to_params(
        self, params: Dict[str, str], filters: Dict[str, Any]
    ) -> None:
        """
        Apply filters to API parameters with proper validation and mapping

        Args:
            params: API parameters dictionary to modify
            filters: User filters to apply
        """
        # Filter mapping from UI filter names to API parameter names
        filter_mapping = {
            "bank": "bank",
            "country": "country",
            "brand": "brand",
            "type": "type",
            "level": "level",
            "priceFrom": "priceFrom",
            "priceTo": "priceTo",
            "base": "base",
            "bin": "bin",
            "state": "state",
            "city": "city",
            "zip": "zip",
        }

        # Boolean filters mapping
        boolean_filters = {
            "zipCheck": "zipCheck",
            "address": "address",
            "phone": "phone",
            "email": "email",
            "withoutcvv": "withoutcvv",
            "refundable": "refundable",
            "expirethismonth": "expirethismonth",
            "dob": "dob",
            "ssn": "ssn",
            "mmn": "mmn",
            "ip": "ip",
            "dl": "dl",
            "ua": "ua",
            "discount": "discount",
        }

        # Apply string/value filters
        for filter_key, param_key in filter_mapping.items():
            if filter_key in filters and filters[filter_key] is not None:
                value = filters[filter_key]
                # Clean and validate the value
                if isinstance(value, str):
                    value = value.strip()
                if value:  # Only apply non-empty values
                    params[param_key] = str(value)
                    logger.debug(
                        f"Applied filter {filter_key}={value} to param {param_key}"
                    )

        # Apply boolean filters
        for filter_key, param_key in boolean_filters.items():
            if filter_key in filters and filters[filter_key] is not None:
                value = filters[filter_key]
                # Convert to string boolean
                if isinstance(value, bool):
                    params[param_key] = "true" if value else "false"
                elif isinstance(value, str) and value.lower() in ("true", "false"):
                    params[param_key] = value.lower()
                logger.debug(
                    f"Applied boolean filter {filter_key}={params[param_key]} to param {param_key}"
                )

    def _build_default_params(self, page: int, limit: int) -> Dict[str, str]:
        """Create default API parameter payload matching demo defaults."""
        return {
            "page": str(page),
            "limit": str(limit),
            "base": "",
            "bank": "",
            "bin": "",
            "country": "",
            "state": "",
            "city": "",
            "brand": "",
            "type": "",
            "level": "",
            "zip": "",
            "priceFrom": "0",
            "priceTo": "500",
            "zipCheck": "false",
            "address": "false",
            "phone": "false",
            "email": "false",
            "withoutcvv": "false",
            "refundable": "false",
            "expirethismonth": "false",
            "dob": "false",
            "ssn": "false",
            "mmn": "false",
            "ip": "false",
            "dl": "false",
            "ua": "false",
            "discount": "false",
        }

    @staticmethod
    def _to_bool(value: Any) -> bool:
        if isinstance(value, bool):
            return value
        if value is None:
            return False
        return str(value).strip().lower() == "true"

    @staticmethod
    def _to_float(value: Any) -> float:
        if value in (None, ""):
            return 0.0
        try:
            return float(value)
        except (TypeError, ValueError):
            return 0.0

    def _params_to_list_items(self, params: Dict[str, str]) -> ListItemsParams:
        """Map parameter dictionary into the dataclass expected by external API."""
        return ListItemsParams(
            page=int(params.get("page", 1)),
            limit=int(params.get("limit", 10)),
            base=params.get("base", ""),
            bank=params.get("bank", ""),
            bin=params.get("bin", ""),
            country=params.get("country", ""),
            state=params.get("state", ""),
            city=params.get("city", ""),
            brand=params.get("brand", ""),
            type=params.get("type", ""),
            level=params.get("level", ""),
            zip=params.get("zip", ""),
            price_from=self._to_float(params.get("priceFrom")),
            price_to=self._to_float(params.get("priceTo")),
            zip_check=self._to_bool(params.get("zipCheck")),
            address=self._to_bool(params.get("address")),
            phone=self._to_bool(params.get("phone")),
            email=self._to_bool(params.get("email")),
            without_cvv=self._to_bool(params.get("withoutcvv")),
            refundable=self._to_bool(params.get("refundable")),
            expire_this_month=self._to_bool(params.get("expirethismonth")),
            dob=self._to_bool(params.get("dob")),
            ssn=self._to_bool(params.get("ssn")),
            mmn=self._to_bool(params.get("mmn")),
            ip=self._to_bool(params.get("ip")),
            dl=self._to_bool(params.get("dl")),
            ua=self._to_bool(params.get("ua")),
            discount=self._to_bool(params.get("discount")),
        )

    @monitor_performance("fetch_cards")
    async def fetch_cards(
        self,
        page: int = 1,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Fetch cards from the external API with comprehensive logging

        Args:
            page: Page number (default: 1)
            limit: Number of items per page (default: 10)
            filters: Optional filters to apply
            user_id: User ID for logging context

        Returns:
            Dictionary containing success status, data, and metadata
        """
        # Create logging context
        context = api_logger.create_context(user_id=user_id, operation="fetch_cards")

        try:
            # Build query parameters with defaults
            params = self._build_default_params(page, limit)

            # Apply filters if provided with proper validation and mapping
            if filters:
                self._apply_filters_to_params(params, filters)
                logger.info(f"Applied filters: {filters}")

            # Delegate to external API service list_items
            list_params = self._params_to_list_items(params)

            api_resp = await self.external_api.list_items(
                params=list_params, user_id=user_id
            )

            if api_resp.success and isinstance(api_resp.data, dict):
                data = api_resp.data
                cards_count = len(data.get("data", []))
                total_count = data.get("totalCount", 0)
                logger.info(
                    f"Successfully fetched {cards_count} cards (page {page}, total: {total_count})"
                )
                return data
            else:
                error_msg = api_resp.error or "List items failed"
                logger.error(f"API returned error: {error_msg}")
                return {"success": False, "error": error_msg, "data": [], "totalCount": 0}
        except asyncio.TimeoutError:
            logger.error("API request timed out")

            # Log timeout error
            api_logger.log_response(
                context=context,
                status_code=408,
                status_message="Request Timeout",
                headers={},
                body=None,
                error_type="timeout_error",
                error_message="Request timed out after 30 seconds",
            )

            return {
                "success": False,
                "error": "Request timed out",
                "data": [],
                "totalCount": 0,
            }
        except Exception as e:
            logger.error(f"Error fetching cards: {e}")

            # Log general error
            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Error",
                headers={},
                body=None,
                error_type="exception",
                error_message=str(e),
            )

            return {"success": False, "error": str(e), "data": [], "totalCount": 0}

    async def search_cards(
        self,
        bank: Optional[str] = None,
        country: Optional[str] = None,
        card_type: Optional[str] = None,
        brand: Optional[str] = None,
        price_from: Optional[float] = None,
        price_to: Optional[float] = None,
        page: int = 1,
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Search cards with specific criteria

        Args:
            bank: Bank name filter
            country: Country code filter
            card_type: Card type filter (CREDIT/DEBIT)
            brand: Card brand filter
            price_from: Minimum price filter
            price_to: Maximum price filter
            page: Page number
            limit: Items per page

        Returns:
            Dictionary containing search results
        """
        filters = {}

        if bank:
            filters["bank"] = bank
        if country:
            filters["country"] = country
        if card_type:
            filters["type"] = card_type
        if brand:
            filters["brand"] = brand
        if price_from is not None:
            filters["priceFrom"] = str(price_from)
        if price_to is not None:
            filters["priceTo"] = str(price_to)

        return await self.fetch_cards(page=page, limit=limit, filters=filters)

    async def fetch_filter_options(
        self,
        filter_name: str,
        filters: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Retrieve dynamic filter options from the external API."""

        params = self._build_default_params(page=1, limit=10)

        if filters:
            self._apply_filters_to_params(params, filters)

        list_params = self._params_to_list_items(params)

        api_resp = await self.external_api.get_filters(
            filter_name=filter_name, params=list_params, user_id=user_id
        )

        if api_resp.success and isinstance(api_resp.data, dict):
            return api_resp.data

        error_msg = api_resp.error or "Filter request failed"
        logger.error(f"Error fetching filter '{filter_name}': {error_msg}")
        return {"success": False, "error": error_msg, "data": []}

    def format_card_for_display(self, card: Dict[str, Any]) -> str:
        """
        Format a card object for display in Telegram

        Args:
            card: Card data dictionary

        Returns:
            Formatted string for display
        """
        try:
            # Extract key information
            card_id = card.get("_id", "N/A")
            bank = card.get("bank", "Unknown Bank")
            bin_number = card.get("bin", "N/A")
            card_type = card.get("type", "N/A")
            level = card.get("level", "N/A")
            country = card.get("country", "N/A")
            price = card.get("price", "0.00")
            exp = card.get("exp", "N/A")
            refund_rate = card.get("refund_rate", "0.00")

            # Format the card information
            formatted = (
                f"💳 <b>Card #{card_id}</b>\n"
                f"🏦 <b>Bank:</b> {bank}\n"
                f"🔢 <b>BIN:</b> {bin_number}\n"
                f"📊 <b>Type:</b> {card_type} ({level})\n"
                f"🌍 <b>Country:</b> {country}\n"
                f"📅 <b>Expires:</b> {exp}\n"
                f"💰 <b>Price:</b> ${price}\n"
                f"📈 <b>Refund Rate:</b> {refund_rate}%\n"
            )

            # Add additional features if available
            features = []
            if card.get("address"):
                features.append("📍 Address")
            if card.get("phone"):
                features.append("📞 Phone")
            if card.get("email"):
                features.append("📧 Email")
            if card.get("ip"):
                features.append("🌐 IP")
            if card.get("refundable"):
                features.append("🔄 Refundable")

            if features:
                formatted += f"✨ <b>Features:</b> {', '.join(features)}\n"

            return formatted

        except Exception as e:
            logger.error(f"Error formatting card for display: {e}")
            return f"💳 Card #{card.get('_id', 'N/A')} - Error displaying details"

    async def get_card_summary(self, cards_data: Dict[str, Any]) -> str:
        """
        Generate a summary of the cards data

        Args:
            cards_data: Response from fetch_cards

        Returns:
            Formatted summary string
        """
        try:
            if not cards_data.get("success", False):
                return "❌ Failed to fetch card data"

            cards = cards_data.get("data", [])
            total_count = cards_data.get("totalCount", 0)

            if not cards:
                return "📭 No cards found matching your criteria"

            # Generate statistics
            card_types = {}
            banks = {}
            countries = {}

            for card in cards:
                # Count card types
                card_type = card.get("type", "Unknown")
                card_types[card_type] = card_types.get(card_type, 0) + 1

                # Count banks
                bank = card.get("bank", "Unknown")
                banks[bank] = banks.get(bank, 0) + 1

                # Count countries
                country = card.get("country", "Unknown")
                countries[country] = countries.get(country, 0) + 1

            summary = (
                f"📊 <b>Card Catalog Summary</b>\n\n"
                f"📈 <b>Total Available:</b> {total_count:,} cards\n"
                f"📄 <b>Showing:</b> {len(cards)} of {total_count:,}\n\n"
                f"🏦 <b>Top Banks:</b>\n"
            )

            # Show top 3 banks
            top_banks = sorted(banks.items(), key=lambda x: x[1], reverse=True)[:3]
            for bank, count in top_banks:
                bank_short = bank[:30] + "..." if len(bank) > 30 else bank
                summary += f"  • {bank_short}: {count}\n"

            summary += f"\n💳 <b>Card Types:</b>\n"
            for card_type, count in sorted(card_types.items()):
                summary += f"  • {card_type}: {count}\n"

            summary += f"\n🌍 <b>Countries:</b> {', '.join(sorted(countries.keys()))}\n"

            return summary

        except Exception as e:
            logger.error(f"Error generating card summary: {e}")
            return "❌ Error generating summary"
