"""
Product Service for managing multi-product API architecture
"""

from __future__ import annotations

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta

from database.connection import get_collection
from models.product import (
    ProductType, 
    ProductInfo, 
    APIInfo, 
    APIStatus,
    UserProductPreference, 
    ProductConfiguration,
    DEFAULT_PRODUCT_CONFIG
)
from models.base import now_utc
from services.external_api_service import ExternalAPIService
from utils.performance import monitor_performance
from api_v1.services.api_config import get_api_config_service

logger = logging.getLogger(__name__)


class ProductService:
    """Service for managing products and API selection"""
    
    def __init__(self):
        self.preferences_collection = get_collection("user_product_preferences")
        self.config_collection = get_collection("product_configurations")
        self.external_api_service = ExternalAPIService()
        self.api_config_service = get_api_config_service()
        self._config_cache: Optional[ProductConfiguration] = None
        self._cache_expiry: Optional[datetime] = None
        self._cache_ttl = timedelta(minutes=15)
    
    @monitor_performance("get_product_config")
    async def get_product_config(self) -> ProductConfiguration:
        """Get current product configuration with caching"""
        try:
            # Check cache
            if (self._config_cache and self._cache_expiry and 
                datetime.utcnow() < self._cache_expiry):
                return self._config_cache
            
            # Try to load from database
            config_doc = await self.config_collection.find_one(
                {"is_active": True, "deleted_at": None},
                sort=[("created_at", -1)]
            )
            
            if config_doc:
                config = ProductConfiguration(**config_doc)
            else:
                # Create default configuration
                config = DEFAULT_PRODUCT_CONFIG
                await self.config_collection.insert_one(config.to_mongo())
                logger.info("Created default product configuration")
            
            # Update cache
            self._config_cache = config
            self._cache_expiry = datetime.utcnow() + self._cache_ttl
            
            return config
            
        except Exception as e:
            logger.error(f"Error loading product configuration: {e}")
            return DEFAULT_PRODUCT_CONFIG
    
    @monitor_performance("get_user_preferences")
    async def get_user_preferences(self, telegram_id: int) -> UserProductPreference:
        """Get user's product preferences"""
        try:
            pref_doc = await self.preferences_collection.find_one({
                "telegram_id": telegram_id,
                "deleted_at": None
            })
            
            if pref_doc:
                return UserProductPreference(**pref_doc)
            else:
                # Create new preferences
                config = await self.get_product_config()
                default_product = None
                default_api = None
                
                # Set defaults to first active product and API
                active_products = config.get_active_products()
                if active_products:
                    default_product = active_products[0].type
                    default_api_info = active_products[0].get_default_api()
                    if default_api_info:
                        default_api = default_api_info.id
                
                new_pref = UserProductPreference(
                    user_id="",  # Will be set when user is created
                    telegram_id=telegram_id,
                    selected_product=default_product,
                    selected_api=default_api,
                    preferred_product=default_product
                )
                
                if default_product and default_api:
                    new_pref.preferred_apis[default_product.value] = default_api
                
                result = await self.preferences_collection.insert_one(new_pref.to_mongo())
                new_pref.id = result.inserted_id
                
                return new_pref
                
        except Exception as e:
            logger.error(f"Error getting user preferences for {telegram_id}: {e}")
            # Return minimal preferences
            return UserProductPreference(
                user_id="",
                telegram_id=telegram_id
            )
    
    @monitor_performance("update_user_selection")
    async def update_user_selection(
        self, 
        telegram_id: int, 
        product: ProductType, 
        api_id: str
    ) -> bool:
        """Update user's current product and API selection"""
        try:
            preferences = await self.get_user_preferences(telegram_id)
            preferences.update_selection(product, api_id)
            
            await self.preferences_collection.update_one(
                {"telegram_id": telegram_id},
                {"$set": preferences.to_mongo()},
                upsert=True
            )
            
            logger.info(f"Updated selection for user {telegram_id}: {product.value} -> {api_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating user selection: {e}")
            return False
    
    async def get_available_products(self) -> List[ProductInfo]:
        """Get list of available products"""
        config = await self.get_product_config()
        return config.get_active_products()
    
    async def get_product_apis(self, product_type: ProductType) -> List[APIInfo]:
        """Get available APIs for a specific product"""
        config = await self.get_product_config()
        product = config.get_product_by_type(product_type)
        return product.apis if product else []
    
    async def get_active_apis(self, product_type: ProductType) -> List[APIInfo]:
        """Get active APIs for a specific product"""
        apis = await self.get_product_apis(product_type)
        return [api for api in apis if api.status == APIStatus.ACTIVE]
    
    async def get_api_info(self, api_id: str) -> Optional[APIInfo]:
        """Get API information by ID"""
        config = await self.get_product_config()
        return config.get_api_by_id(api_id)
    
    async def get_user_current_selection(self, telegram_id: int) -> Tuple[Optional[ProductType], Optional[str]]:
        """Get user's current product and API selection"""
        preferences = await self.get_user_preferences(telegram_id)
        return preferences.selected_product, preferences.selected_api
    
    async def get_recommended_api(self, telegram_id: int, product_type: ProductType) -> Optional[APIInfo]:
        """Get recommended API for user based on preferences and availability"""
        try:
            preferences = await self.get_user_preferences(telegram_id)
            active_apis = await self.get_active_apis(product_type)
            
            if not active_apis:
                return None
            
            # Check user's preferred API for this product
            preferred_api_id = preferences.preferred_apis.get(product_type.value)
            if preferred_api_id:
                for api in active_apis:
                    if api.id == preferred_api_id:
                        return api
            
            # Return default API for the product
            config = await self.get_product_config()
            product = config.get_product_by_type(product_type)
            return product.get_default_api() if product else None
            
        except Exception as e:
            logger.error(f"Error getting recommended API: {e}")
            return None
    
    async def is_api_available(self, api_id: str) -> bool:
        """Check if an API is currently available"""
        api_info = await self.get_api_info(api_id)
        return api_info is not None and api_info.status == APIStatus.ACTIVE
    
    async def get_api_config_name(self, api_id: str) -> Optional[str]:
        """Get the configuration name for an API (for external API service)"""
        api_info = await self.get_api_info(api_id)
        return api_info.config_name if api_info else None
    
    async def get_user_stats(self, telegram_id: int) -> Dict[str, Any]:
        """Get user's product usage statistics"""
        try:
            preferences = await self.get_user_preferences(telegram_id)
            
            return {
                "current_product": preferences.selected_product.value if preferences.selected_product else None,
                "current_api": preferences.selected_api,
                "preferred_product": preferences.preferred_product.value if preferences.preferred_product else None,
                "product_usage": preferences.product_usage_count,
                "api_usage": preferences.api_usage_count,
                "last_product_change": preferences.last_product_change,
                "last_api_change": preferences.last_api_change
            }
            
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            return {}
    
    async def clear_cache(self) -> None:
        """Clear configuration cache"""
        self._config_cache = None
        self._cache_expiry = None
        logger.debug("Product configuration cache cleared")
    
    async def get_external_api_service_for_user(self, telegram_id: int):
        """Get configured external API service for user's current selection"""
        try:
            current_product, current_api = await self.get_user_current_selection(telegram_id)
            if not current_product or not current_api:
                return None

            config_name = await self.get_api_config_name(current_api)
            if not config_name:
                return None

            # Load API configuration details for the selected service
            api_config = await self.api_config_service.get_api_config_by_name(
                config_name, decrypt_sensitive=True
            )

            if config_name.lower() == "api2" and api_config:
                from api_v2.services.adapter import APIV2ExternalBrowseAdapter

                return APIV2ExternalBrowseAdapter(api_config)

            # Default to legacy external API service (BASE 1)
            return ExternalAPIService()

        except Exception as e:
            logger.error(f"Error getting API service for user {telegram_id}: {e}")
            return None

    async def switch_user_to_api(self, telegram_id: int, api_id: str) -> bool:
        """Switch user to a specific API and update their preferences"""
        try:
            api_info = await self.get_api_info(api_id)
            if not api_info:
                logger.error(f"API {api_id} not found")
                return False

            if api_info.status != APIStatus.ACTIVE:
                logger.error(f"API {api_id} is not active")
                return False

            # Find the product type for this API
            config = await self.get_product_config()
            for product in config.products:
                for api in product.apis:
                    if api.id == api_id:
                        return await self.update_user_selection(telegram_id, product.type, api_id)

            logger.error(f"Could not find product for API {api_id}")
            return False

        except Exception as e:
            logger.error(f"Error switching user to API {api_id}: {e}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on product service"""
        try:
            config = await self.get_product_config()
            active_products = config.get_active_products()

            health_data = {
                "status": "healthy",
                "products_configured": len(config.products),
                "active_products": len(active_products),
                "total_apis": sum(len(p.apis) for p in config.products),
                "active_apis": sum(len(p.get_active_apis()) for p in active_products),
                "cache_status": "hit" if self._config_cache else "miss"
            }

            # Check if we have at least one active product with one active API
            if not active_products or not any(p.get_active_apis() for p in active_products):
                health_data["status"] = "degraded"
                health_data["warning"] = "No active APIs available"

            return health_data

        except Exception as e:
            logger.error(f"Product service health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
