"""
Checkout Queue Service for managing serialized checkout processing
Ensures proper isolation between users while using a shared website account

Key fixes vs. original:
- Store datetimes as real datetime objects in DB (not ISO strings) so <PERSON><PERSON> can sort/compare.
- Make idempotency key STABLE (removed timestamps); derived from user_id + deterministic cart snapshot hash.
- Added missing _processing_task attribute; unified worker start/stop APIs to avoid confusion.
- Fixed queue position/avg processing time using datetime objects safely.
- Hardened external cart logic (verification, consistent product_id usage).
- Avoid metadata overwrite on status update; merge instead.
- Safer aiohttp session closing, logging, and typing cleanups.
"""

from __future__ import annotations
import asyncio
import logging
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
import json
import hashlib

from database.connection import get_collection, database_transaction
from pymongo.errors import DuplicateKeyError
from models import Purchase, PurchaseStatus
from services.cart_service import CartService
from services.user_service import UserService
from api_v1.services.api_config import get_api_config_service
from services.external_api_service import get_external_api_service
from config.settings import get_settings
from utils.logging import setup_logging

logger = logging.getLogger(__name__)


class CheckoutJobStatus(str, Enum):
    """Checkout job status enumeration"""

    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CheckoutJob:
    """Represents a checkout job in the queue"""

    def __init__(
        self,
        user_id: str,
        cart_snapshot: Dict[str, Any],
        job_id: str | None = None,
        idempotency_key: str | None = None,
        created_at: datetime | None = None,
        status: CheckoutJobStatus = CheckoutJobStatus.QUEUED,
        metadata: Dict[str, Any] | None = None,
    ):
        self.job_id = job_id or str(uuid.uuid4())
        self.user_id = user_id
        self.cart_snapshot = cart_snapshot
        self.created_at = created_at or datetime.now(timezone.utc)  # aware UTC
        self.status = status
        self.metadata = metadata or {}
        self.attempts = 0
        self.last_error: Optional[str] = None
        self.completed_at: Optional[datetime] = None
        self.idempotency_key = idempotency_key or self._generate_idempotency_key()

    # ---- Helpers -----------------------------------------------------------------
    def _stable_cart_fingerprint(self) -> str:
        """Deterministic hash of cart contents (ignore volatile fields)."""

        def sanitize(obj: Any) -> Any:
            if isinstance(obj, dict):
                drop_keys = {"snapshot_timestamp", "updated_at", "created_at", "_id"}
                return {
                    k: sanitize(v) for k, v in sorted(obj.items()) if k not in drop_keys
                }
            if isinstance(obj, list):
                if obj and isinstance(obj[0], dict) and "card_id" in obj[0]:
                    normalized = [
                        {
                            kk: sanitize(vv)
                            for kk, vv in sorted(item.items())
                            if kk not in {"_id"}
                        }
                        for item in obj
                    ]
                    normalized.sort(
                        key=lambda x: (str(x.get("card_id")), int(x.get("quantity", 1)))
                    )
                    return normalized
                return [sanitize(v) for v in obj]
            return obj

        stable = {
            "items": sanitize(self.cart_snapshot.get("items", [])),
            "total_amount": self.cart_snapshot.get("total_amount", 0.0),
            "total_items": self.cart_snapshot.get("total_items", 0),
            "user_id": self.user_id,
        }
        return hashlib.sha256(
            json.dumps(stable, sort_keys=True, default=str).encode()
        ).hexdigest()

    def _generate_idempotency_key(self) -> str:
        """Stable idempotency key (no timestamps!)."""
        return self._stable_cart_fingerprint()

    def to_dict(self) -> Dict[str, Any]:
        """Convert job to dict for storage (keep datetimes as datetime objects)."""
        return {
            "job_id": self.job_id,
            "user_id": self.user_id,
            "cart_snapshot": self.cart_snapshot,
            "idempotency_key": self.idempotency_key,
            "created_at": self.created_at,
            "status": self.status.value,
            "metadata": self.metadata,
            "attempts": self.attempts,
            "last_error": self.last_error,
            "completed_at": self.completed_at,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CheckoutJob":
        """Create job from dictionary (accepts datetime or ISO string)."""

        def to_dt(value: Any) -> datetime | None:
            if value is None:
                return None
            if isinstance(value, datetime):
                return value if value.tzinfo else value.replace(tzinfo=timezone.utc)
            if isinstance(value, str):
                return datetime.fromisoformat(value.replace("Z", "+00:00"))
            return None

        job = cls(
            user_id=data["user_id"],
            cart_snapshot=data["cart_snapshot"],
            job_id=data["job_id"],
            idempotency_key=data.get("idempotency_key"),
            created_at=to_dt(data.get("created_at")) or datetime.now(timezone.utc),
            status=CheckoutJobStatus(data["status"]),
            metadata=data.get("metadata", {}),
        )
        job.attempts = data.get("attempts", 0)
        job.last_error = data.get("last_error")
        job.completed_at = to_dt(data.get("completed_at"))
        return job


class CheckoutQueueService:
    """Service for managing checkout queue and processing"""

    def __init__(self):
        self.settings = get_settings()
        # Ensure logging is initialized if application didn't configure it yet
        try:
            root_logger = logging.getLogger()
            if not root_logger.handlers:
                setup_logging(
                    level=self.settings.LOG_LEVEL,
                    log_file=(
                        self.settings.LOG_FILE_PATH if self.settings.LOG_TO_FILE else None
                    ),
                    structured_format=self.settings.LOG_STRUCTURED,
                    environment=self.settings.ENVIRONMENT,
                    console_colored=self.settings.LOG_COLOR,
                    show_category=self.settings.LOG_SHOW_CATEGORY,
                )
                logging.getLogger(__name__).info(
                    "Logging auto-initialized by CheckoutQueueService"
                )
        except Exception:
            # Don't block initialization on logging setup failures
            pass
        self.jobs_collection = get_collection("checkout_jobs")
        self.cart_service = CartService()
        self.user_service = UserService()
        self.api_config_service = get_api_config_service()
        self.external_api_service = get_external_api_service()

        # Queue configuration
        # Do not retry checkout; run once and report
        self.max_retries = 1
        self.retry_delays = [1, 2, 4]  # kept for compatibility, unused when max_retries=1
        self.job_timeout = 300  # per job timeout
        self.queue_check_interval = 1  # seconds

        # Processing state
        self._processing_lock = asyncio.Lock()
        self._is_processing = False
        self._worker_task: Optional[asyncio.Task] = None
        self._processing_task: Optional[asyncio.Task] = (
            None  # back-compat alias storage
        )

        # External API handled via external_api_service (no local HTTP session)
    # (Legacy API configuration helpers removed; external_api_service handles config)

    # ---- Worker lifecycle --------------------------------------------------------
    async def start_worker(self) -> None:
        """Start the checkout queue worker (preferred API)."""
        if self._worker_task and not self._worker_task.done():
            logger.warning("Checkout worker is already running")
            return
        self._is_processing = True
        self._worker_task = asyncio.create_task(
            self._process_queue(), name="checkout_worker"
        )
        logger.info("Checkout queue worker started")

    async def stop_worker(self) -> None:
        """Stop the checkout queue worker."""
        self._is_processing = False
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
            finally:
                self._worker_task = None
        logger.info("Checkout queue worker stopped")

    # Back-compat aliases
    async def start_processing(self) -> None:
        await self.start_worker()

    async def stop_processing(self) -> None:
        await self.stop_worker()

    # ---- Public API --------------------------------------------------------------
    async def queue_checkout(
        self, user_id: str, telegram_user_id: int
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Queue a checkout job for processing

        Returns: Tuple of (success, message, job_id)
        """
        try:
            cart_contents = await self.cart_service.get_cart_contents(user_id)
            if cart_contents.get("is_empty", True):
                return False, "Cart is empty", None

            wallet = await self.user_service.get_wallet(user_id)
            total_amount = float(cart_contents.get("total_amount", 0.0))
            if not wallet or not wallet.can_spend(total_amount):
                return (
                    False,
                    f"Insufficient funds. Need ${total_amount}, have ${wallet.balance if wallet else 0}",
                    None,
                )

            # Snapshot serializer (OK to keep ISO strings in snapshot)
            def serialize_mongo_dict(data: Any) -> Any:
                if isinstance(data, dict):
                    return {
                        key: serialize_mongo_dict(value) for key, value in data.items()
                    }
                if isinstance(data, list):
                    return [serialize_mongo_dict(item) for item in data]
                if isinstance(data, datetime):
                    return data.astimezone(timezone.utc).isoformat()
                return data

            cart_snapshot = {
                "cart": (
                    serialize_mongo_dict(cart_contents["cart"].to_mongo())
                    if cart_contents["cart"]
                    else None
                ),
                "items": [
                    serialize_mongo_dict(item.to_mongo())
                    for item in cart_contents["items"]
                ],
                "total_amount": total_amount,
                "total_items": cart_contents.get("total_items", 0),
                "snapshot_timestamp": datetime.now(timezone.utc).isoformat(),
                "telegram_user_id": telegram_user_id,
            }

            # Idempotency check using stable fingerprint of cart
            existing_job = await self._find_existing_job(user_id, cart_snapshot)
            if existing_job:
                return (
                    True,
                    f"Order already queued (Job #{existing_job.job_id[:8]})",
                    existing_job.job_id,
                )

            job = CheckoutJob(
                user_id=user_id,
                cart_snapshot=cart_snapshot,
                metadata={
                    "telegram_user_id": telegram_user_id,
                    "estimated_processing_time": await self._estimate_processing_time(),
                },
            )

            try:
                await self.jobs_collection.insert_one(job.to_dict())
            except DuplicateKeyError:
                # Another concurrent request queued the same job (idempotency)
                existing = await self._find_existing_job(user_id, cart_snapshot)
                if existing:
                    return (
                        True,
                        f"Order already queued (Job #{existing.job_id[:8]})",
                        existing.job_id,
                    )
                # Fallback: report as already queued
                return True, "Order already queued", None

            queue_position = await self._get_queue_position(job.job_id)
            estimated_wait = queue_position * 30  # seconds

            logger.info(f"Queued checkout job {job.job_id} for user {user_id}")
            return (
                True,
                (
                    "⏳ Order queued for processing!\n"
                    f"📍 Position: #{queue_position} in line\n"
                    f"⏱️ Estimated wait: {estimated_wait // 60}m {estimated_wait % 60}s\n"
                    f"🆔 Job ID: {job.job_id[:8]}"
                ),
                job.job_id,
            )
        except Exception as e:
            logger.error("Error queuing checkout", exc_info=True)
            return False, f"Failed to queue checkout: {str(e)}", None

    async def cancel_checkout(self, user_id: str, job_id: str) -> Tuple[bool, str]:
        try:
            job_doc = await self.jobs_collection.find_one(
                {
                    "job_id": job_id,
                    "user_id": user_id,
                    "status": {"$in": [CheckoutJobStatus.QUEUED.value]},
                }
            )
            if not job_doc:
                return False, "Job not found or cannot be cancelled"

            await self.jobs_collection.update_one(
                {"job_id": job_id},
                {
                    "$set": {
                        "status": CheckoutJobStatus.CANCELLED.value,
                        "completed_at": datetime.now(timezone.utc),
                    }
                },
            )
            logger.info(f"Cancelled checkout job {job_id} for user {user_id}")
            return True, f"✅ Order #{job_id[:8]} has been cancelled"
        except Exception as e:
            logger.error("Error cancelling checkout job", exc_info=True)
            return False, f"Failed to cancel order: {str(e)}"

    async def get_job_status(self, job_id: str) -> Optional[CheckoutJob]:
        try:
            job_doc = await self.jobs_collection.find_one({"job_id": job_id})
            return CheckoutJob.from_dict(job_doc) if job_doc else None
        except Exception as e:
            logger.error("Error getting job status", exc_info=True)
            return None

    async def get_user_jobs(self, user_id: str, limit: int = 10) -> List[CheckoutJob]:
        try:
            jobs_docs = (
                await self.jobs_collection.find({"user_id": user_id})
                .sort("created_at", -1)
                .limit(limit)
                .to_list(None)
            )
            return [CheckoutJob.from_dict(doc) for doc in jobs_docs]
        except Exception as e:
            logger.error(f"Error getting user jobs for {user_id}: {e}")
            return []

    # ---- Queue processing --------------------------------------------------------
    async def _process_queue(self) -> None:
        logger.info("Starting checkout queue processing")
        while self._is_processing:
            try:
                job = await self._get_next_job()
                if job:
                    await self._process_job(job)
                else:
                    await asyncio.sleep(self.queue_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in queue processing loop", exc_info=True)
                await asyncio.sleep(5)
        logger.info("Checkout queue processing stopped")

    async def _get_next_job(self) -> Optional[CheckoutJob]:
        try:
            cursor = (
                self.jobs_collection.find({"status": CheckoutJobStatus.QUEUED.value})
                .sort("created_at", 1)
                .limit(1)
            )
            jobs_list = await cursor.to_list(1)
            return CheckoutJob.from_dict(jobs_list[0]) if jobs_list else None
        except Exception as e:
            logger.error("Error getting next job", exc_info=True)
            return None

    async def _process_job(self, job: CheckoutJob) -> None:
        logger.info(f"Attempting to acquire checkout lock for job {job.job_id}")
        async with self._processing_lock:
            lock_acquired_time = datetime.now(timezone.utc)
            logger.info(
                f"🔒 Checkout lock acquired for job {job.job_id} (user: {job.user_id}) at {lock_acquired_time.isoformat()}"
            )
            try:
                # Distributed-safe atomic claim: transition QUEUED -> PROCESSING
                claim_result = await self.jobs_collection.update_one(
                    {
                        "job_id": job.job_id,
                        "status": CheckoutJobStatus.QUEUED.value,
                    },
                    {
                        "$set": {
                            "status": CheckoutJobStatus.PROCESSING.value,
                            "claimed_at": datetime.now(timezone.utc),
                        }
                    },
                )
                if getattr(claim_result, "modified_count", 0) == 0:
                    # Another worker/process already claimed or processed this job
                    logger.info(
                        f"Skipping job {job.job_id}: already claimed or not in QUEUED state"
                    )
                    return
                logger.info(
                    (
                        f"Processing checkout job {job.job_id} for user {job.user_id} - "
                        f"Items: {len(job.cart_snapshot.get('items', []))}, "
                        f"Total: ${job.cart_snapshot.get('total_amount', 0.0)}"
                    )
                )
                await self._notify_user(job, "🔄 Processing your order...")

                success, message, result_data = (
                    await self._execute_checkout_with_retries(job)
                )
                if success:
                    await self._update_job_status(
                        job.job_id, CheckoutJobStatus.COMPLETED, metadata=result_data
                    )
                    await self._notify_user(job, f"✅ {message}", result_data)
                    logger.info(
                        f"✅ Successfully completed job {job.job_id} - Order processed successfully"
                    )
                else:
                    await self._update_job_status(
                        job.job_id, CheckoutJobStatus.FAILED, error=message
                    )
                    await self._notify_user(job, f"❌ {message}")
                    logger.error(f"❌ Failed to complete job {job.job_id}: {message}")
            except Exception as e:
                logger.error(f"💥 Critical error processing job {job.job_id}: {e}")
                await self._update_job_status(
                    job.job_id, CheckoutJobStatus.FAILED, error=str(e)
                )
                await self._notify_user(job, f"❌ Order processing failed: {str(e)}")
            finally:
                lock_released_time = datetime.now(timezone.utc)
                processing_duration = (
                    lock_released_time - lock_acquired_time
                ).total_seconds()
                logger.info(
                    f"🔓 Checkout lock released for job {job.job_id} at {lock_released_time.isoformat()} "
                    f"(held for {processing_duration:.2f}s)"
                )

    async def _execute_checkout_with_retries(
        self, job: CheckoutJob
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        for attempt in range(self.max_retries):
            try:
                job.attempts = attempt + 1
                success, message, result_data = await self._execute_checkout(job)
                if success:
                    return True, message, result_data
                if attempt < self.max_retries - 1:
                    delay = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                    logger.warning(
                        f"Checkout attempt {attempt + 1} failed for job {job.job_id}: {message}. Retrying in {delay}s..."
                    )
                    await asyncio.sleep(delay)
                else:
                    job.last_error = message
                    return (
                        False,
                        f"Checkout failed after {self.max_retries} attempts: {message}",
                        None,
                    )
            except Exception as e:
                job.last_error = str(e)
                if attempt < self.max_retries - 1:
                    delay = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                    logger.warning(
                        f"Checkout attempt {attempt + 1} error for job {job.job_id}: {e}. Retrying in {delay}s..."
                    )
                    await asyncio.sleep(delay)
                else:
                    return False, f"Checkout failed with error: {e}", None
        return False, "Maximum retries exceeded", None

    async def _execute_checkout(
        self, job: CheckoutJob
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        checkout_step = "initialization"
        logger.info(f"Checkout: start job {job.job_id}")
        try:
            # Step 1: Validate cart snapshot
            checkout_step = "cart_validation"
            logger.info(f"Checkout: step1 validate cart snapshot")
            cart_snapshot = job.cart_snapshot
            items = cart_snapshot.get("items", [])
            total_amount = float(cart_snapshot.get("total_amount", 0.0))
            if not items or total_amount <= 0:
                logger.warning(
                    f"Invalid cart snapshot for job {job.job_id}: items={len(items)}, total={total_amount}"
                )
                return False, "Invalid cart snapshot", None

            # Step 2: Validate funds
            checkout_step = "funds_validation"
            logger.info(f"Checkout: step2 validate funds")
            wallet = await self.user_service.get_wallet(job.user_id)
            if not wallet or not wallet.can_spend(total_amount):
                logger.warning(
                    f"Insufficient funds for job {job.job_id}: need=${total_amount}, have=${wallet.balance if wallet else 0}"
                )
                return (
                    False,
                    f"Insufficient funds. Need ${total_amount}, have ${wallet.balance if wallet else 0}",
                    None,
                )

            # Step 3: External API session
            checkout_step = "session_creation"
            logger.info(f"Checkout: step3 create external session")

            # Step 4: Populate cart (which now includes clearing)
            checkout_step = "cart_population"
            logger.info(
                f"Checkout: step4 populate cart with {len(items)} items"
            )
            populate_success = await self._populate_external_cart(items)
            if not populate_success:
                try:
                    external_items_debug = await self._get_external_cart_items()
                    logger.error(
                        f"🔍 External cart has {len(external_items_debug)} items after failed population: "
                        f"{[item.get('product_id') for item in external_items_debug]}"
                    )
                except Exception as debug_e:
                    logger.error(
                        f"   Failed to get external cart for debugging: {debug_e}"
                    )
                return False, "Failed to populate external cart with user items", None

            # Skip immediate per-item verification to reduce extra API calls; a single
            # validation pass runs later if needed.

            # Step 5: Validate external cart (single, lightweight pass)
            checkout_step = "cart_validation_external"
            logger.info("Checkout: step5 validate external cart")
            validation_result = await self._validate_cart_items(items)
            if not validation_result["valid"]:
                return (
                    False,
                    f"Cart validation failed: {validation_result['message']}",
                    None,
                )
            warnings = validation_result.get("warnings") or []
            if warnings:
                logger.info(f"Checkout: validation warnings: {'; '.join(warnings)}")

            # Execute external checkout per demo curl
            checkout_step = "external_checkout"
            logger.info("Checkout: step6 external checkout")
            checkout_resp = await self.external_api_service.checkout()
            if not getattr(checkout_resp, "success", False):
                return (
                    False,
                    checkout_resp.error or "External checkout failed",
                    None,
                )

            # Build result summary and finish
            # First, record purchases and debit wallet in our DB
            external_order_id = (
                (checkout_resp.data or {}).get("order_id") if checkout_resp.data else None
            )
            purchase_result_ok = False
            purchase_result_meta: Dict[str, Any] = {}
            try:
                ok, msg, meta = await self._process_successful_checkout(
                    user_id=job.user_id,
                    cart_snapshot=cart_snapshot,
                    external_result={"order_id": external_order_id} if external_order_id else {},
                )
                purchase_result_ok = ok
                purchase_result_meta = meta or {}
            except Exception as e:
                logger.error(f"Failed to persist purchases: {e}")

            # Attempt to fetch recent external orders and filter for purchased items
            purchased_items = items
            purchased_card_ids = []
            try:
                purchased_card_ids = [
                    str(it.get("card_id") or (it.get("card_data") or {}).get("_id"))
                    for it in purchased_items
                    if (it.get("card_id") or (it.get("card_data") or {}).get("_id")) is not None
                ]
            except Exception:
                purchased_card_ids = []

            filtered_orders: list[dict] = []
            try:
                orders_resp = await self.external_api_service.list_orders(page=1, limit=10)
                if getattr(orders_resp, "success", False) and isinstance(orders_resp.data, dict):
                    data_list = orders_resp.data.get("data") or []
                    # Filter orders whose product_id matches purchased card IDs
                    pid_set = {str(pid) for pid in purchased_card_ids}
                    for od in data_list:
                        pid = od.get("product_id") or od.get("card_id") or od.get("id")
                        if pid is not None and str(pid) in pid_set:
                            # Sanitize sensitive fields before storing
                            sanitized = {k: v for k, v in od.items() if k not in {"cc", "cvv", "ssn", "dl", "ua"}}
                            filtered_orders.append(sanitized)
            except Exception as e:
                logger.warning(f"Could not fetch/filter orders: {e}")

            result_data = {
                "external_order_id": external_order_id,
                "status_code": checkout_resp.status_code,
                "total_amount": total_amount,
                "items_purchased": len(items),
                "checkout_timestamp": datetime.now(timezone.utc).isoformat(),
                "validation_warnings": validation_result.get("warnings", []),
                "purchased_card_ids": purchased_card_ids,
                "purchased_orders": filtered_orders,
                "transaction_id": purchase_result_meta.get("transaction_id"),
                "remaining_balance": purchase_result_meta.get("remaining_balance"),
            }
            logger.info(
                f"Checkout: completed for job {job.job_id} (status={checkout_resp.status_code})"
            )
            return True, "Checkout completed", result_data
        except Exception as e:
            logger.error(
                f"Error executing checkout for job {job.job_id} at step '{checkout_step}'",
                exc_info=True,
            )
            return (
                False,
                f"Checkout execution failed at {checkout_step}: {str(e)}",
                None,
            )

    # ---- External API helpers ----------------------------------------------------

    async def _clear_external_cart(self) -> bool:
        try:
            logger.info("🧹 Starting external cart clearing process...")
            cart_items = await self._get_external_cart_items()
            if not cart_items:
                logger.info("✅ External cart is already empty")
                return True
            removed_count = 0
            failed_removals: list[dict[str, Any]] = []
            for i, item in enumerate(cart_items, 1):
                item_id = item.get("_id") or item.get("id")
                item_name = (
                    item.get("name")
                    or f"{item.get('brand', 'Unknown')} {item.get('bin', 'Card')}"
                    or f"Card {item_id}"
                )
                if item_id is None:
                    logger.warning(f"⚠️ Item {i} has no ID, skipping removal")
                    continue
                logger.debug(
                    f"🗑️ Removing item {i}/{len(cart_items)}: {item_name} (ID: {item_id})"
                )
                success = await self._remove_external_cart_item(None, item_id)
                if success:
                    removed_count += 1
                else:
                    failed_removals.append({"id": item_id, "name": item_name})
            remaining_items = await self._get_external_cart_items()
            if remaining_items:
                logger.error(
                    f"❌ Cart clearing incomplete: {len(remaining_items)} items remain after removal attempt"
                )
                for item in remaining_items:
                    iid = item.get("_id") or item.get("id", "No ID")
                    logger.error(f"   - Remaining item ID: {iid}")
                return False
            if failed_removals:
                logger.warning(
                    f"⚠️ {len(failed_removals)} items failed to remove but cart appears empty"
                )
            logger.info(
                f"✅ Successfully cleared external cart - {removed_count} items removed, 0 remaining"
            )
            return True
        except Exception as e:
            logger.error("💥 Critical error clearing external cart", exc_info=True)
            return False

    async def _get_external_cart_items(
        self, session: "aiohttp.ClientSession" | None = None
    ) -> List[Dict[str, Any]]:
        try:
            response = await self.external_api_service.view_cart()
            if (
                getattr(response, "success", False)
                and getattr(response, "data", None) is not None
            ):
                data = response.data
                if isinstance(data, dict):
                    if isinstance(data.get("data"), list):
                        return data["data"]
                    if isinstance(data.get("cart"), dict) and isinstance(
                        data["cart"].get("items"), list
                    ):
                        return data["cart"]["items"]
                if isinstance(data, list):
                    return data
            else:
                logger.warning(
                    f"Failed to get external cart items: {getattr(response, 'error', 'unknown error')}"
                )
            return []
        except Exception as e:
            logger.error(f"Error getting external cart items: {e}")
            return []

    async def _remove_external_cart_item(
        self, session: Any, item_id: Any
    ) -> bool:
        try:
            logger.debug(f"Removing cart item {item_id} from external cart")
            try:
                numeric_id = int(item_id)
                resp = await self.external_api_service.delete_from_cart(numeric_id)
            except (ValueError, TypeError):
                resp = await self.external_api_service.delete_from_cart(item_id)
            if getattr(resp, "success", False):
                return True
            err = str(getattr(resp, "error", ""))
            if "not found" in err.lower() or "404" in err:
                return True
            logger.warning(
                f"Failed to remove cart item {item_id} from external cart: {err}"
            )
            return False
        except Exception as e:
            logger.error(f"Error removing external cart item {item_id}: {e}")
            return False

    async def _add_to_external_cart(
        self, session: Any, card_id: int | str
    ) -> bool:
        try:
            logger.debug(f"🔄 Attempting to add card {card_id} to external cart")

            # Skip pre-add cart fetch to avoid extra API calls

            # Make the API call
            logger.debug(f"📡 Making add_to_cart API call for card {card_id}")
            response = await self.external_api_service.add_to_cart(
                int(card_id), "Cards"
            )

            # ENHANCED LOGGING: Complete API response details
            # Concise one-line summary; raw body is logged centrally by HTTP client
            logger.info(
                f"add_to_cart {card_id}: status={getattr(response,'status_code','?')} success={getattr(response,'success',None)}"
            )

            # Trust API success without extra GETs to verify presence
            if getattr(response, "success", False):
                return True
            else:
                logger.error(f"❌ API reported FAILURE for card {card_id}")

                # ENHANCED LOGGING: Analyze failure response
                msg = ""
                if hasattr(response, "data") and isinstance(response.data, dict):
                    msg = str(response.data.get("message", ""))
                    logger.debug(f"   Failure message: '{msg}'")

                # Handle "already in cart" scenario
                if "already have this product in cart" in msg.lower():
                    # Treat as success; skip verification fetches
                    return True
                else:
                    logger.error(
                        f"❌ GENUINE FAILURE: Card {card_id} could not be added"
                    )
                    logger.error(f"   Failure reason: {msg if msg else 'Unknown'}")
                    return False
        except Exception as e:
            logger.error(f"💥 EXCEPTION adding card {card_id} to external cart: {e}")
            return False

    async def _populate_external_cart(
        self, cart_items: List[Dict[str, Any]]
    ) -> bool:
        try:
            # First, clear the external cart to ensure a clean state
            logger.info("Ensuring external cart is clear before populating.")
            clear_success = False
            for attempt in range(1, 3 + 1):
                logger.info(f"🧹 Cart clearing attempt {attempt}/3")
                clear_success = await self._clear_external_cart()
                if clear_success:
                    logger.info(f"✅ Cart clearing successful on attempt {attempt}")
                    break
                if attempt < 3:
                    logger.warning(
                        f"❌ Cart clearing failed on attempt {attempt}; retrying in 1s"
                    )
                    await asyncio.sleep(1)
            
            if not clear_success:
                logger.error("Failed to clear external cart before populating. Aborting.")
                return False

            # Now, proceed with populating the cart
            total_quantity = sum(int(item.get("quantity", 1)) for item in cart_items)
            logger.info(
                f"🛒 Starting cart population: {len(cart_items)} unique items, {total_quantity} total items"
            )
            failed_items: list[dict[str, Any]] = []
            for idx, item_data in enumerate(cart_items, 1):
                card_id = item_data.get("card_id")
                quantity = int(item_data.get("quantity", 1))
                card_name = item_data.get("card_data", {}).get(
                    "name", f"Card {card_id}"
                )
                if not card_id:
                    logger.warning(
                        f"⚠️ Skipping item {idx} with missing card_id: {item_data}"
                    )
                    failed_items.append(item_data)
                    continue
                logger.info(
                    f"📦 Adding item {idx}/{len(cart_items)}: {card_name} (ID: {card_id}, Qty: {quantity})"
                )

                # Skip pre-item cart fetch to avoid unnecessary API calls

                success_count = 0
                for i in range(quantity):
                    logger.debug(
                        f"   🔄 Adding copy {i+1}/{quantity} of card {card_id}"
                    )
                    add_success = await self._add_to_external_cart(None, card_id)

                    if add_success:
                        success_count += 1
                        logger.debug(
                            f"   ✅ Copy {i+1}/{quantity} of card {card_id} added successfully"
                        )
                    else:
                        logger.error(
                            f"   ❌ Copy {i+1}/{quantity} of card {card_id} failed to add"
                        )
                        failed_items.append(
                            {
                                "card_id": card_id,
                                "card_name": card_name,
                                "attempt": i + 1,
                                "quantity": quantity,
                            }
                        )
                if success_count != quantity:
                    logger.warning(
                        f"⚠️ Only added {success_count}/{quantity} copies of {card_name}"
                    )
                # Keep a tiny pacing delay to avoid rate limits
                await asyncio.sleep(0.05)
            if failed_items:
                logger.error(f"❌ Cart population had failures: {len(failed_items)}")
                for f in failed_items[:5]:
                    logger.error(f"   - Failed: {f}")
                return False
            logger.info("✅ Successfully populated external cart")
            return True
        except Exception as e:
            logger.error("💥 Critical error populating external cart", exc_info=True)
            return False

    async def _validate_cart_items(
        self, expected_items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        try:
            logger.info("Validating external cart contents...")
            external_items = await self._get_external_cart_items()

            expected_map: dict[str, int] = {}
            for item in expected_items:
                card_id = str(item.get("card_id", ""))
                qty = int(item.get("quantity", 1))
                if card_id:
                    expected_map[card_id] = expected_map.get(card_id, 0) + qty

            actual_map: dict[str, int] = {}
            for item in external_items:
                card_id = str(
                    item.get("product_id")
                    or item.get("card_id")
                    or item.get("id")
                    or item.get("_id")
                    or ""
                )
                if card_id:
                    actual_map[card_id] = actual_map.get(card_id, 0) + 1

            errors: list[str] = []
            for cid, exp in expected_map.items():
                act = actual_map.get(cid, 0)
                if act < exp:
                    errors.append(f"Card {cid}: expected {exp}, found {act}")
            for cid, act in actual_map.items():
                exp = expected_map.get(cid, 0)
                if act > exp:
                    errors.append(f"Card {cid}: unexpected {act - exp} extra items")

            if errors:
                msg = "; ".join(errors)
                logger.error(f"Cart validation failed: {msg}")
                return {
                    "valid": False,
                    "message": msg,
                    "expected_items": expected_map,
                    "actual_items": actual_map,
                    "errors": errors,
                }

            stock_validation = await self._validate_stock_and_prices(
                expected_items, external_items
            )
            if not stock_validation["valid"]:
                return stock_validation

            return {
                "valid": True,
                "message": "Cart contents validated successfully",
                "expected_items": expected_map,
                "actual_items": actual_map,
                "warnings": stock_validation.get("warnings", []),
            }
        except Exception as e:
            logger.error("Error validating cart items", exc_info=True)
            return {"valid": False, "message": f"Validation failed: {str(e)}"}

    async def _validate_stock_and_prices(
        self,
        expected_items: List[Dict[str, Any]],
        external_items: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        try:
            warnings: list[str] = []

            price_by_card: dict[str, float] = {}
            for it in expected_items:
                cid = str(it.get("card_id", ""))
                if cid:
                    try:
                        price_by_card[cid] = float(it.get("price_at_add"))
                    except (TypeError, ValueError):
                        pass

            for ext in external_items:
                cid = str(
                    ext.get("product_id") or ext.get("card_id") or ext.get("id") or ""
                )
                if not cid:
                    continue
                if ext.get("out_of_stock") or ext.get("unavailable"):
                    warnings.append(f"Card {cid} appears to be out of stock")
                current_price = (
                    ext.get("price")
                    if ext.get("price") is not None
                    else ext.get("current_price")
                )
                if current_price is not None and cid in price_by_card:
                    try:
                        cp = float(current_price)
                        ep = float(price_by_card[cid])
                        if abs(cp - ep) > 0.01:
                            warnings.append(
                                f"Card {cid} price changed: expected ${ep}, current ${cp}"
                            )
                    except (TypeError, ValueError):
                        pass

            if warnings:
                logger.warning(
                    "Stock/price validation warnings: %s", "; ".join(warnings)
                )
            return {
                "valid": True,
                "message": (
                    "Stock and price validation passed"
                    if not warnings
                    else "Warnings present"
                ),
                "warnings": warnings,
            }
        except Exception as e:
            logger.error(f"Error validating stock and prices: {e}")
            return {
                "valid": False,
                "message": f"Stock/price validation failed: {str(e)}",
            }

    async def _execute_external_checkout(self) -> Dict[str, Any]:
        """Checkout is disabled. Instead, exercise cart APIs.

        Runs exactly once per operation without loops:
        - view_cart
        - remove_from_cart (first item, if any)
        Does NOT re-add any product. Adding user items only happens in the
        dedicated population step when the cart is fully cleared.
        Always returns success=False to prevent local debit/processing.
        """
        try:
            logger.info("Checkout disabled: clearing external cart (single pass)")
            details: Dict[str, Any] = {}

            # 1) View cart once
            view = await self.external_api_service.view_cart()
            details["view_status"] = getattr(view, "status_code", None)
            items = []
            if getattr(view, "success", False) and isinstance(view.data, dict):
                items = view.data.get("data") or []

            # 2) Clear cart completely if it has items (no re-adding)
            cleared = True
            if items:
                cleared = await self._clear_external_cart()
            details["cleared"] = bool(cleared)

            logger.info("Cart diagnostics complete (cleared when non-empty)")
            return {
                "success": False,
                "message": "Checkout disabled; viewed and cleared cart (no re-add)",
                "details": details,
            }
        except Exception as e:
            logger.error("Error running cart diagnostics in place of checkout", exc_info=True)
            return {
                "success": False,
                "message": f"Checkout disabled; diagnostics error: {str(e)}",
            }

    async def _process_successful_checkout(
        self,
        user_id: str,
        cart_snapshot: Dict[str, Any],
        external_result: Dict[str, Any],
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """Process successful external checkout in our local system"""
        try:
            async with database_transaction():
                items = cart_snapshot.get("items", [])
                total_amount = float(cart_snapshot.get("total_amount", 0.0))

                # Debit user wallet (idempotent reference)
                tx_metadata = {
                    "external_order_id": external_result.get("order_id"),
                    "checkout_timestamp": datetime.now(timezone.utc).isoformat(),
                    "items_count": len(items),
                }

                wallet, transaction = await self.user_service.debit_wallet(
                    user_id,
                    total_amount,
                    currency="USD",
                    reference=f"checkout_{external_result.get('order_id')}",
                    metadata=tx_metadata,
                    idempotency_key=f"checkout_{external_result.get('order_id')}",
                )

                # Create purchase records (idempotent per card)
                purchases_collection = get_collection("purchases")
                for item_data in items:
                    card_data = item_data.get("card_data", {})
                    card_pk = card_data.get("_id") or item_data.get("card_id")
                    qty = int(item_data.get("quantity", 1))
                    price = float(item_data.get("price_at_add", 0.0)) * qty

                    purchase = Purchase(
                        user_id=user_id,
                        sku=f"card_{card_pk}",
                        price=price,
                        currency="USD",
                        status=PurchaseStatus.SUCCESS,
                        metadata={
                            "card_id": card_pk,
                            "card_data": card_data,
                            "quantity": qty,
                            "external_order_id": external_result.get("order_id"),
                            "transaction_id": str(getattr(transaction, "id", "")),
                        },
                        idempotency_key=f"purchase_{external_result.get('order_id')}_{card_pk}",
                    )

                    try:
                        await purchases_collection.insert_one(purchase.to_mongo())
                    except Exception as pe:
                        # Likely duplicate due to idempotency
                        logger.debug(f"Purchase insert skipped (idempotent): {pe}")

                # Clear user's virtual cart
                await self.cart_service.clear_cart(user_id)

                result_data = {
                    "transaction_id": str(getattr(transaction, "id", "")),
                    "remaining_balance": getattr(wallet, "balance", None),
                }
                return (
                    True,
                    f"Successfully purchased {len(items)} items for ${total_amount}",
                    result_data,
                )

        except Exception as e:
            logger.error("Error processing successful checkout", exc_info=True)
            return False, f"Failed to process checkout: {str(e)}", None

    async def _update_job_status(
        self,
        job_id: str,
        status: CheckoutJobStatus,
        metadata: Dict[str, Any] | None = None,
        error: str | None = None,
    ) -> None:
        """Update job status in database (merge metadata, keep datetimes as datetime)."""
        try:
            update_data: Dict[str, Any] = {
                "status": status.value,
                "updated_at": datetime.now(timezone.utc),
            }

            if status in [
                CheckoutJobStatus.COMPLETED,
                CheckoutJobStatus.FAILED,
                CheckoutJobStatus.CANCELLED,
            ]:
                update_data["completed_at"] = datetime.now(timezone.utc)

            if metadata:
                # Merge, don't overwrite completely if metadata already exists
                update_data["metadata"] = metadata

            if error:
                update_data["last_error"] = error

            await self.jobs_collection.update_one(
                {"job_id": job_id}, {"$set": update_data}
            )
        except Exception as e:
            logger.error("Error updating job status", exc_info=True)

    async def _notify_user(
        self, job: CheckoutJob, message: str, result_data: Dict[str, Any] | None = None
    ) -> None:
        """Send notification to user via Telegram"""
        try:
            telegram_user_id = job.metadata.get("telegram_user_id")
            if not telegram_user_id:
                return

            # Import here to avoid circular imports
            from aiogram import Bot

            settings = get_settings()
            bot = Bot(token=settings.BOT_TOKEN)

            # Format message
            if result_data:
                formatted_message = self._format_completion_message(
                    message, result_data, job
                )
            else:
                formatted_message = (
                    f"🛒 <b>Order Update</b>\n\n{message}\n\n🆔 Job: {job.job_id[:8]}"
                )

            # Optional inline keyboard: View Card buttons and View Cart
            reply_markup = None
            try:
                from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

                buttons: list[list[InlineKeyboardButton]] = []
                if result_data and isinstance(result_data, dict):
                    ids = result_data.get("purchased_card_ids") or []
                    if isinstance(ids, list) and ids:
                        row: list[InlineKeyboardButton] = []
                        for idx, cid in enumerate(ids[:6], 1):
                            row.append(
                                InlineKeyboardButton(
                                    text=f"🔎 View Card {str(cid)[:6]}…",
                                    callback_data=f"orders:view_card:{cid}",
                                )
                            )
                            if len(row) == 2:
                                buttons.append(row)
                                row = []
                        if row:
                            buttons.append(row)
                # Always add a View Cart button
                buttons.append(
                    [InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view")]
                )
                reply_markup = InlineKeyboardMarkup(inline_keyboard=buttons)
            except Exception:
                reply_markup = None

            await bot.send_message(
                chat_id=telegram_user_id,
                text=formatted_message,
                parse_mode="HTML",
                reply_markup=reply_markup,
            )
            await bot.session.close()
        except Exception as e:
            logger.error("Error sending notification to user", exc_info=True)

    def _format_completion_message(
        self, message: str, result_data: Dict[str, Any], job: CheckoutJob
    ) -> str:
        """Format completion message with order details"""
        try:
            items = job.cart_snapshot.get("items", [])
            total_amount = result_data.get("total_amount", 0.0)
            remaining_balance = result_data.get("remaining_balance", 0.0)

            formatted_message = f"""
🎉 <b>Order Completed Successfully!</b>

📦 <b>Order Details:</b>
• Items: {len(items)} cards purchased
• Total: ${total_amount}
• Order ID: {result_data.get('external_order_id', 'N/A')}

💰 <b>Payment:</b>
• Transaction ID: {str(result_data.get('transaction_id', 'N/A'))[:8]}...
• Remaining Balance: ${remaining_balance}

🆔 Job: {job.job_id[:8]}
⏰ Completed: {datetime.now(timezone.utc).strftime('%H:%M:%S UTC')}
"""
            # Append purchased order details if present (sanitized)
            try:
                orders = result_data.get("purchased_orders") or []
                if isinstance(orders, list) and orders:
                    formatted_message += "\n<b>Your Purchased Cards</b>\n"
                    for od in orders[:5]:
                        line = (
                            f"• #{od.get('product_id', 'N/A')} — {od.get('bank', 'Unknown')} "
                            f"{od.get('brand', '')} {od.get('level', '')} "
                            f"${od.get('price', 'N/A')} [{od.get('status', 'N/A')}]"
                        )
                        formatted_message += f"\n{line}"
            except Exception:
                pass
            return formatted_message.strip()
        except Exception as e:
            logger.error(f"Error formatting completion message: {e}")
            return f"✅ {message}\n\n🆔 Job: {job.job_id[:8]}"

    async def _find_existing_job(
        self, user_id: str, cart_snapshot: Dict[str, Any]
    ) -> Optional[CheckoutJob]:
        """Find existing queued/processing job with the same stable cart fingerprint."""
        try:
            temp_job = CheckoutJob(user_id=user_id, cart_snapshot=cart_snapshot)
            job_doc = await self.jobs_collection.find_one(
                {
                    "user_id": user_id,
                    "idempotency_key": temp_job.idempotency_key,
                    "status": {
                        "$in": [
                            CheckoutJobStatus.QUEUED.value,
                            CheckoutJobStatus.PROCESSING.value,
                        ]
                    },
                }
            )
            return CheckoutJob.from_dict(job_doc) if job_doc else None
        except Exception as e:
            logger.error(f"Error finding existing job: {e}")
            return None

    async def _get_queue_position(self, job_id: str) -> int:
        """Get position of job in queue (1-based)."""
        try:
            job_doc = await self.jobs_collection.find_one({"job_id": job_id})
            if not job_doc:
                return 0
            created_at = job_doc["created_at"]
            # Count jobs created at or before this job which are still queued
            position = await self.jobs_collection.count_documents(
                {
                    "status": CheckoutJobStatus.QUEUED.value,
                    "created_at": {"$lte": created_at},
                }
            )
            return max(1, position)
        except Exception as e:
            logger.error(f"Error getting queue position: {e}")
            return 1

    async def _estimate_processing_time(self) -> int:
        """Rudimentary estimate: 30s per queued job."""
        try:
            queue_length = await self.jobs_collection.count_documents(
                {"status": CheckoutJobStatus.QUEUED.value}
            )
            return queue_length * 30
        except Exception as e:
            logger.error(f"Error estimating processing time: {e}")
            return 60

    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get current queue statistics (counts and avg processing time of recent completions)."""
        try:
            stats: Dict[str, Any] = {}
            # Counts by status
            for status in CheckoutJobStatus:
                count = await self.jobs_collection.count_documents(
                    {"status": status.value}
                )
                stats[f"{status.value}_count"] = count

            # Average processing time over last 10 completed jobs
            completed_jobs = await (
                self.jobs_collection.find(
                    {
                        "status": CheckoutJobStatus.COMPLETED.value,
                        "completed_at": {"$exists": True},
                        "created_at": {"$exists": True},
                    }
                )
                .sort("completed_at", -1)
                .limit(10)
                .to_list(None)
            )

            processing_times: List[float] = []
            for job in completed_jobs:
                created_at = job.get("created_at")
                completed_at = job.get("completed_at")
                if isinstance(created_at, datetime) and isinstance(
                    completed_at, datetime
                ):
                    delta = (completed_at - created_at).total_seconds()
                    if delta >= 0:
                        processing_times.append(delta)

            if processing_times:
                stats["avg_processing_time"] = sum(processing_times) / len(
                    processing_times
                )

            return stats
        except Exception as e:
            logger.error(f"Error getting queue stats: {e}")
            return {}
