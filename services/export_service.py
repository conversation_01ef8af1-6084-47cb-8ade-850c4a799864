"""
Export service for generating transaction reports and data exports
"""

from __future__ import annotations

import csv
import io
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
import logging

from database.connection import get_collection
from models import Transaction, User, Wallet
from utils.performance import monitor_performance

logger = logging.getLogger(__name__)


class ExportService:
    """Service for exporting user data and transaction reports"""

    def __init__(self):
        self.transactions_collection = get_collection("transactions")
        self.users_collection = get_collection("users")
        self.wallets_collection = get_collection("wallets")

    @monitor_performance("export_transactions_csv")
    async def export_transactions_csv(
        self,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        transaction_type: Optional[str] = None
    ) -> str:
        """
        Export user transactions as CSV format
        
        Args:
            user_id: User ID to export transactions for
            start_date: Optional start date filter
            end_date: Optional end date filter
            transaction_type: Optional transaction type filter
            
        Returns:
            CSV content as string
        """
        try:
            # Build query
            query = {"user_id": user_id}
            
            if start_date or end_date:
                date_filter = {}
                if start_date:
                    date_filter["$gte"] = start_date
                if end_date:
                    date_filter["$lte"] = end_date
                query["created_at"] = date_filter
            
            if transaction_type:
                query["type"] = transaction_type
            
            # Fetch transactions
            cursor = self.transactions_collection.find(query).sort("created_at", -1)
            transactions = await cursor.to_list(length=1000)  # Limit to 1000 for performance
            
            # Generate CSV
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow([
                "Date", "Type", "Amount", "Currency", "Status", 
                "Reference", "Description", "Hash"
            ])
            
            # Write data
            for tx in transactions:
                writer.writerow([
                    tx.get("created_at", "").isoformat() if tx.get("created_at") else "",
                    tx.get("type", ""),
                    tx.get("amount", 0),
                    tx.get("currency", "USD"),
                    tx.get("status", ""),
                    tx.get("reference", ""),
                    tx.get("description", ""),
                    tx.get("hash", "")[:16] + "..." if tx.get("hash") else ""  # Truncate hash
                ])
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Failed to export transactions for user {user_id}: {e}")
            raise

    @monitor_performance("generate_wallet_report")
    async def generate_wallet_report(self, user_id: str) -> Dict[str, Any]:
        """
        Generate comprehensive wallet report for a user
        
        Args:
            user_id: User ID to generate report for
            
        Returns:
            Dictionary containing wallet statistics and insights
        """
        try:
            # Get user and wallet data
            user_doc = await self.users_collection.find_one({"_id": user_id})
            wallet_doc = await self.wallets_collection.find_one({"user_id": user_id})
            
            if not user_doc or not wallet_doc:
                raise ValueError("User or wallet not found")
            
            user = User.from_mongo(user_doc)
            wallet = Wallet.from_mongo(wallet_doc)
            
            # Get transaction statistics
            now = datetime.utcnow()
            thirty_days_ago = now - timedelta(days=30)
            seven_days_ago = now - timedelta(days=7)
            
            # Aggregate transaction data
            pipeline = [
                {"$match": {"user_id": user_id, "created_at": {"$gte": thirty_days_ago}}},
                {"$group": {
                    "_id": "$type",
                    "count": {"$sum": 1},
                    "total_amount": {"$sum": "$amount"},
                    "avg_amount": {"$avg": "$amount"}
                }}
            ]
            
            tx_stats = await self.transactions_collection.aggregate(pipeline).to_list(length=None)
            
            # Calculate spending patterns
            recent_pipeline = [
                {"$match": {"user_id": user_id, "created_at": {"$gte": seven_days_ago}}},
                {"$group": {
                    "_id": {"$dateToString": {"format": "%Y-%m-%d", "date": "$created_at"}},
                    "daily_total": {"$sum": "$amount"},
                    "transaction_count": {"$sum": 1}
                }},
                {"$sort": {"_id": 1}}
            ]
            
            daily_stats = await self.transactions_collection.aggregate(recent_pipeline).to_list(length=None)
            
            # Build report
            report = {
                "user_info": {
                    "telegram_id": user.telegram_id,
                    "username": user.username,
                    "first_name": user.first_name,
                    "member_since": user.created_at.isoformat(),
                    "role": user.role
                },
                "wallet_info": {
                    "balance": wallet.balance,
                    "currency": wallet.currency,
                    "daily_cap": wallet.daily_cap,
                    "monthly_cap": wallet.monthly_cap,
                    "locked": wallet.locked,
                    "last_updated": wallet.updated_at.isoformat() if wallet.updated_at else None
                },
                "transaction_summary": {
                    "last_30_days": {stat["_id"]: {
                        "count": stat["count"],
                        "total": round(stat["total_amount"], 2),
                        "average": round(stat["avg_amount"], 2)
                    } for stat in tx_stats},
                    "daily_activity": {stat["_id"]: {
                        "total": round(stat["daily_total"], 2),
                        "count": stat["transaction_count"]
                    } for stat in daily_stats}
                },
                "generated_at": now.isoformat()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate wallet report for user {user_id}: {e}")
            raise

    async def export_user_data(self, user_id: str) -> Dict[str, Any]:
        """
        Export all user data for GDPR compliance or backup
        
        Args:
            user_id: User ID to export data for
            
        Returns:
            Complete user data export
        """
        try:
            # Get all user-related data
            user_doc = await self.users_collection.find_one({"_id": user_id})
            wallet_doc = await self.wallets_collection.find_one({"user_id": user_id})
            
            transactions = await self.transactions_collection.find(
                {"user_id": user_id}
            ).sort("created_at", -1).to_list(length=None)
            
            # Build complete export
            export_data = {
                "export_info": {
                    "generated_at": datetime.utcnow().isoformat(),
                    "user_id": user_id,
                    "format_version": "1.0"
                },
                "user_profile": user_doc,
                "wallet": wallet_doc,
                "transactions": transactions,
                "summary": {
                    "total_transactions": len(transactions),
                    "account_age_days": (
                        datetime.utcnow() - user_doc["created_at"]
                    ).days if user_doc and user_doc.get("created_at") else 0
                }
            }
            
            return export_data
            
        except Exception as e:
            logger.error(f"Failed to export user data for user {user_id}: {e}")
            raise

    async def generate_admin_summary(self) -> Dict[str, Any]:
        """
        Generate summary statistics for admin dashboard
        
        Returns:
            Admin summary with key metrics
        """
        try:
            now = datetime.utcnow()
            
            # User statistics
            total_users = await self.users_collection.count_documents({})
            active_users = await self.users_collection.count_documents({
                "last_seen": {"$gte": now - timedelta(days=7)}
            })
            
            # Transaction statistics
            total_transactions = await self.transactions_collection.count_documents({})
            recent_transactions = await self.transactions_collection.count_documents({
                "created_at": {"$gte": now - timedelta(days=1)}
            })
            
            # Wallet statistics
            wallet_pipeline = [
                {"$group": {
                    "_id": None,
                    "total_balance": {"$sum": "$balance"},
                    "avg_balance": {"$avg": "$balance"},
                    "locked_wallets": {"$sum": {"$cond": ["$locked", 1, 0]}}
                }}
            ]
            
            wallet_stats = await self.wallets_collection.aggregate(wallet_pipeline).to_list(length=1)
            wallet_data = wallet_stats[0] if wallet_stats else {}
            
            return {
                "users": {
                    "total": total_users,
                    "active_last_7_days": active_users,
                    "activity_rate": round((active_users / total_users * 100), 2) if total_users > 0 else 0
                },
                "transactions": {
                    "total": total_transactions,
                    "last_24_hours": recent_transactions
                },
                "wallets": {
                    "total_balance": round(wallet_data.get("total_balance", 0), 2),
                    "average_balance": round(wallet_data.get("avg_balance", 0), 2),
                    "locked_count": wallet_data.get("locked_wallets", 0)
                },
                "generated_at": now.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to generate admin summary: {e}")
            raise
