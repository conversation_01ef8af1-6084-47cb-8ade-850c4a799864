"""
Data retention service for cleaning up old records
"""

from __future__ import annotations

import logging
from datetime import timedelta
from typing import Dict, Any

from database.connection import get_database
from config.settings import get_settings

logger = logging.getLogger(__name__)


class RetentionService:
    """Service for managing data retention and cleanup"""

    def __init__(self):
        self.settings = get_settings()
        self.db = None

    async def _get_database(self):
        """Get database instance"""
        if self.db is None:
            self.db = get_database()
        return self.db

    async def purge_old_data(self, retention_days: int) -> int:
        """
        Purge old data based on retention policy

        Args:
            retention_days: Number of days to retain data

        Returns:
            Number of records removed
        """
        try:
            from models.base import now_utc

            db = await self._get_database()
            cutoff_date = now_utc() - timedelta(days=retention_days)
            total_removed = 0

            # Define collections and their date fields for cleanup
            cleanup_targets = [
                ("audit_logs", "created_at"),
                ("transactions", "created_at"),
                ("purchases", "created_at"),
                # Note: Don't clean up users or wallets as they're core data
            ]

            for collection_name, date_field in cleanup_targets:
                try:
                    collection = db[collection_name]

                    # Count documents to be removed
                    count = await collection.count_documents(
                        {date_field: {"$lt": cutoff_date}}
                    )

                    if count > 0:
                        # Remove old documents
                        result = await collection.delete_many(
                            {date_field: {"$lt": cutoff_date}}
                        )

                        removed = result.deleted_count
                        total_removed += removed

                        logger.info(
                            f"Removed {removed} old records from {collection_name}"
                        )

                except Exception as e:
                    logger.error(f"Failed to clean up {collection_name}: {e}")
                    continue

            if total_removed > 0:
                logger.info(
                    f"Data retention cleanup completed: {total_removed} records removed"
                )
            else:
                logger.debug("No old data found for cleanup")

            return total_removed

        except Exception as e:
            logger.error(f"Data retention cleanup failed: {e}")
            return 0

    async def get_retention_stats(self) -> Dict[str, Any]:
        """
        Get statistics about data retention

        Returns:
            Dictionary with retention statistics
        """
        try:
            from models.base import now_utc

            db = await self._get_database()
            cutoff_date = now_utc() - timedelta(
                days=self.settings.RETENTION_DAYS
            )

            stats = {
                "retention_days": self.settings.RETENTION_DAYS,
                "cutoff_date": cutoff_date.isoformat(),
                "collections": {},
            }

            # Check each collection
            collections_to_check = [
                "users",
                "wallets",
                "transactions",
                "purchases",
                "audit_logs",
                "catalog_items",
            ]

            for collection_name in collections_to_check:
                try:
                    collection = db[collection_name]

                    total_count = await collection.count_documents({})
                    old_count = await collection.count_documents(
                        {"created_at": {"$lt": cutoff_date}}
                    )

                    stats["collections"][collection_name] = {
                        "total_records": total_count,
                        "old_records": old_count,
                        "retention_eligible": old_count > 0,
                    }

                except Exception as e:
                    logger.warning(f"Failed to get stats for {collection_name}: {e}")
                    stats["collections"][collection_name] = {"error": str(e)}

            return stats

        except Exception as e:
            logger.error(f"Failed to get retention stats: {e}")
            return {"error": str(e)}

    async def cleanup_test_data(self) -> int:
        """
        Clean up test/demo data (for development/testing)

        Returns:
            Number of test records removed
        """
        try:
            db = await self._get_database()
            total_removed = 0

            # Remove test users (those with telegram_id < 1000 are likely test accounts)
            test_users = (
                await db["users"].find({"telegram_id": {"$lt": 1000}}).to_list(None)
            )

            for user in test_users:
                user_id = str(user["_id"])

                # Remove associated data
                collections_to_clean = [
                    ("wallets", "user_id"),
                    ("transactions", "user_id"),
                    ("purchases", "user_id"),
                    ("saved_filters", "user_id"),
                    ("audit_logs", "actor_id"),
                ]

                for collection_name, user_field in collections_to_clean:
                    result = await db[collection_name].delete_many(
                        {user_field: user_id}
                    )
                    total_removed += result.deleted_count

                # Remove the user
                await db["users"].delete_one({"_id": user["_id"]})
                total_removed += 1

            if total_removed > 0:
                logger.info(
                    f"Test data cleanup completed: {total_removed} records removed"
                )

            return total_removed

        except Exception as e:
            logger.error(f"Test data cleanup failed: {e}")
            return 0
