"""
Enhanced API Configuration Validation and Testing Service
Provides comprehensive validation, testing, and error reporting for API configurations
"""

from __future__ import annotations

import asyncio
import aiohttp
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlparse
from dataclasses import dataclass
from enum import Enum

from api_v1.services.api_config import APIConfiguration, APIEndpoint as APIEndpoint, APICredentials

logger = logging.getLogger(__name__)


class ValidationLevel(str, Enum):
    """Validation severity levels"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ValidationResult:
    """Result of a validation check"""
    level: ValidationLevel
    field: str
    message: str
    suggestion: Optional[str] = None
    code: Optional[str] = None


@dataclass
class TestResult:
    """Result of an API connection test"""
    success: bool
    status_code: Optional[int] = None
    response_time_ms: Optional[int] = None
    error_message: Optional[str] = None
    suggestions: List[str] = None
    endpoint_results: Dict[str, Any] = None

    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []
        if self.endpoint_results is None:
            self.endpoint_results = {}


class APIConfigValidationService:
    """Enhanced validation and testing service for API configurations"""
    
    def __init__(self):
        self.timeout = aiohttp.ClientTimeout(total=30)
    
    async def validate_configuration(self, config: APIConfiguration) -> List[ValidationResult]:
        """Perform comprehensive validation of an API configuration"""
        results = []
        
        # Validate basic fields
        results.extend(self._validate_basic_fields(config))
        
        # Validate URLs
        results.extend(self._validate_urls(config))
        
        # Validate endpoints
        results.extend(self._validate_endpoints(config))
        
        # Validate credentials
        results.extend(self._validate_credentials(config))
        
        # Validate configuration consistency
        results.extend(self._validate_consistency(config))
        
        return results
    
    def _validate_basic_fields(self, config: APIConfiguration) -> List[ValidationResult]:
        """Validate basic configuration fields"""
        results = []
        
        # Service name validation
        if not config.service_name:
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                "service_name",
                "Service name is required",
                "Provide a unique, descriptive service name using lowercase letters and underscores"
            ))
        elif not re.match(r'^[a-z][a-z0-9_]*$', config.service_name):
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                "service_name",
                "Invalid service name format",
                "Use only lowercase letters, numbers, and underscores. Start with a letter."
            ))
        
        # Display name validation
        if not config.display_name:
            results.append(ValidationResult(
                ValidationLevel.WARNING,
                "display_name",
                "Display name is empty",
                "Provide a human-readable name for better organization"
            ))
        
        # Description validation
        if not config.description:
            results.append(ValidationResult(
                ValidationLevel.INFO,
                "description",
                "Description is empty",
                "Add a description to help other administrators understand this API's purpose"
            ))
        
        # Category validation
        valid_categories = ["general", "ecommerce", "payment", "notification", "analytics", "authentication"]
        if config.category not in valid_categories:
            results.append(ValidationResult(
                ValidationLevel.WARNING,
                "category",
                f"Unknown category: {config.category}",
                f"Consider using one of: {', '.join(valid_categories)}"
            ))
        
        return results
    
    def _validate_urls(self, config: APIConfiguration) -> List[ValidationResult]:
        """Validate URL formats and accessibility"""
        results = []
        
        # Base URL validation
        if not config.base_url:
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                "base_url",
                "Base URL is required",
                "Provide the root URL for the API (e.g., https://api.example.com)"
            ))
        else:
            parsed = urlparse(config.base_url)
            if not parsed.scheme:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    "base_url",
                    "Base URL must include protocol (http:// or https://)",
                    "Add https:// or http:// to the beginning of the URL"
                ))
            elif parsed.scheme not in ['http', 'https']:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    "base_url",
                    f"Unsupported protocol: {parsed.scheme}",
                    "Use http:// or https:// protocol"
                ))
            elif parsed.scheme == 'http':
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    "base_url",
                    "Using insecure HTTP protocol",
                    "Consider using HTTPS for better security"
                ))
            
            if not parsed.netloc:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    "base_url",
                    "Invalid URL format - missing domain",
                    "Ensure the URL includes a valid domain name"
                ))
        
        # Documentation URL validation
        if config.documentation_url:
            parsed = urlparse(config.documentation_url)
            if not parsed.scheme or not parsed.netloc:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    "documentation_url",
                    "Invalid documentation URL format",
                    "Provide a complete URL including protocol"
                ))
        
        return results
    
    def _validate_endpoints(self, config: APIConfiguration) -> List[ValidationResult]:
        """Validate endpoint configurations"""
        results = []
        
        if not config.endpoints:
            results.append(ValidationResult(
                ValidationLevel.WARNING,
                "endpoints",
                "No endpoints configured",
                "Add at least one endpoint to make the API functional"
            ))
            return results
        
        for name, endpoint in config.endpoints.items():
            # Endpoint name validation
            if not re.match(r'^[a-z][a-z0-9_]*$', name):
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    f"endpoints.{name}",
                    f"Endpoint name '{name}' should use lowercase letters and underscores",
                    "Use descriptive names like 'get_user' or 'create_order'"
                ))
            
            # URL validation
            if not endpoint.url:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    f"endpoints.{name}.url",
                    f"Endpoint '{name}' has no URL",
                    "Provide a valid endpoint path or full URL"
                ))
            
            # Method validation
            valid_methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS']
            if endpoint.method not in valid_methods:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    f"endpoints.{name}.method",
                    f"Invalid HTTP method: {endpoint.method}",
                    f"Use one of: {', '.join(valid_methods)}"
                ))
            
            # Timeout validation
            if endpoint.timeout <= 0:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    f"endpoints.{name}.timeout",
                    f"Invalid timeout value: {endpoint.timeout}",
                    "Timeout must be a positive number (recommended: 30-60 seconds)"
                ))
            elif endpoint.timeout > 300:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    f"endpoints.{name}.timeout",
                    f"Very long timeout: {endpoint.timeout} seconds",
                    "Consider using a shorter timeout (30-60 seconds) for better user experience"
                ))
            
            # Retry configuration validation
            if endpoint.retry_count < 0:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    f"endpoints.{name}.retry_count",
                    f"Invalid retry count: {endpoint.retry_count}",
                    "Retry count must be 0 or positive (recommended: 2-3)"
                ))
            elif endpoint.retry_count > 10:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    f"endpoints.{name}.retry_count",
                    f"High retry count: {endpoint.retry_count}",
                    "Consider using fewer retries (2-3) to avoid long delays"
                ))
        
        return results
    
    def _validate_credentials(self, config: APIConfiguration) -> List[ValidationResult]:
        """Validate authentication credentials"""
        results = []
        
        if not config.credentials:
            results.append(ValidationResult(
                ValidationLevel.WARNING,
                "credentials",
                "No authentication configured",
                "Most APIs require authentication - add API key, token, or other credentials"
            ))
            return results
        
        # Check if any authentication is configured
        has_auth = (
            config.credentials.login_token or
            config.credentials.session_cookies or
            any(key.lower() in ['authorization', 'x-api-key', 'api-key'] 
                for key in config.credentials.headers.keys())
        )
        
        if not has_auth:
            results.append(ValidationResult(
                ValidationLevel.INFO,
                "credentials",
                "No authentication detected",
                "If this API requires authentication, add tokens, API keys, or other credentials"
            ))
        
        # Validate headers
        for header_name, header_value in config.credentials.headers.items():
            if not header_name.strip():
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    "credentials.headers",
                    "Empty header name found",
                    "Remove empty header entries or provide valid header names"
                ))
            
            if not header_value.strip():
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    f"credentials.headers.{header_name}",
                    f"Empty value for header '{header_name}'",
                    "Provide a value for this header or remove it"
                ))
        
        return results
    
    def _validate_consistency(self, config: APIConfiguration) -> List[ValidationResult]:
        """Validate configuration consistency and best practices"""
        results = []
        
        # Check if health check endpoint exists
        if config.health_check_endpoint:
            health_endpoint_found = any(
                config.health_check_endpoint in endpoint.url 
                for endpoint in config.endpoints.values()
            )
            if not health_endpoint_found:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    "health_check_endpoint",
                    "Health check endpoint not found in configured endpoints",
                    "Add the health check endpoint to your endpoints list or remove this setting"
                ))
        
        # Check for common endpoint patterns
        endpoint_names = set(config.endpoints.keys())
        if 'create' in endpoint_names and 'list' not in endpoint_names:
            results.append(ValidationResult(
                ValidationLevel.INFO,
                "endpoints",
                "Has 'create' endpoint but no 'list' endpoint",
                "Consider adding a 'list' endpoint for better API completeness"
            ))
        
        return results
    
    async def test_api_connection(self, config: APIConfiguration) -> TestResult:
        """Test API connection and basic functionality"""
        if not config.enabled:
            return TestResult(
                success=False,
                error_message="Configuration is disabled",
                suggestions=["Enable the configuration before testing"]
            )
        
        # Test basic connectivity first
        basic_test = await self._test_basic_connectivity(config)
        if not basic_test.success:
            return basic_test
        
        # Test individual endpoints
        endpoint_results = {}
        for name, endpoint in config.endpoints.items():
            try:
                result = await self._test_endpoint(config, endpoint)
                endpoint_results[name] = result
            except Exception as e:
                endpoint_results[name] = {
                    "success": False,
                    "error": str(e)
                }
        
        # Analyze results
        successful_endpoints = sum(1 for r in endpoint_results.values() if r.get("success", False))
        total_endpoints = len(endpoint_results)
        
        return TestResult(
            success=successful_endpoints > 0,
            endpoint_results=endpoint_results,
            suggestions=self._generate_test_suggestions(config, endpoint_results)
        )
    
    async def _test_basic_connectivity(self, config: APIConfiguration) -> TestResult:
        """Test basic connectivity to the API base URL"""
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                start_time = asyncio.get_event_loop().time()
                
                async with session.get(
                    config.base_url,
                    headers=config.credentials.headers,
                    cookies=config.credentials.session_cookies
                ) as response:
                    end_time = asyncio.get_event_loop().time()
                    response_time = int((end_time - start_time) * 1000)
                    
                    return TestResult(
                        success=response.status < 500,
                        status_code=response.status,
                        response_time_ms=response_time,
                        suggestions=self._get_status_suggestions(response.status)
                    )
                    
        except asyncio.TimeoutError:
            return TestResult(
                success=False,
                error_message="Connection timeout",
                suggestions=[
                    "Check if the API URL is correct",
                    "Verify network connectivity",
                    "Consider increasing timeout values"
                ]
            )
        except Exception as e:
            return TestResult(
                success=False,
                error_message=str(e),
                suggestions=[
                    "Verify the API URL is correct and accessible",
                    "Check authentication credentials",
                    "Ensure the API server is running"
                ]
            )
    
    async def _test_endpoint(self, config: APIConfiguration, endpoint: APIEndpoint) -> Dict[str, Any]:
        """Test a specific endpoint"""
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                start_time = asyncio.get_event_loop().time()
                
                # Build full URL
                if endpoint.url.startswith('http'):
                    url = endpoint.url
                else:
                    url = config.base_url.rstrip('/') + '/' + endpoint.url.lstrip('/')
                
                async with session.request(
                    endpoint.method,
                    url,
                    headers=config.credentials.headers,
                    cookies=config.credentials.session_cookies
                ) as response:
                    end_time = asyncio.get_event_loop().time()
                    response_time = int((end_time - start_time) * 1000)
                    
                    return {
                        "success": response.status < 500,
                        "status_code": response.status,
                        "response_time_ms": response_time,
                        "url": url
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": endpoint.url
            }
    
    def _get_status_suggestions(self, status_code: int) -> List[str]:
        """Get suggestions based on HTTP status code"""
        if status_code == 401:
            return [
                "Check authentication credentials",
                "Verify API key or token is valid",
                "Ensure proper authorization headers are set"
            ]
        elif status_code == 403:
            return [
                "Check if your account has permission to access this API",
                "Verify API key has required scopes",
                "Contact API provider for access"
            ]
        elif status_code == 404:
            return [
                "Verify the API URL is correct",
                "Check if the endpoint path exists",
                "Ensure API version is supported"
            ]
        elif status_code >= 500:
            return [
                "API server is experiencing issues",
                "Try again later",
                "Contact API provider if problem persists"
            ]
        elif status_code >= 400:
            return [
                "Check request format and parameters",
                "Verify required headers are included",
                "Review API documentation for requirements"
            ]
        else:
            return ["Connection successful"]
    
    def _generate_test_suggestions(self, config: APIConfiguration, results: Dict[str, Any]) -> List[str]:
        """Generate suggestions based on test results"""
        suggestions = []
        
        failed_endpoints = [name for name, result in results.items() if not result.get("success", False)]
        
        if failed_endpoints:
            suggestions.append(f"Failed endpoints: {', '.join(failed_endpoints)}")
            suggestions.append("Check endpoint URLs and authentication")
        
        if all(not result.get("success", False) for result in results.values()):
            suggestions.extend([
                "All endpoints failed - check base configuration",
                "Verify API credentials and permissions",
                "Test with API documentation or tools like Postman"
            ])
        
        return suggestions


# Global instance
_validation_service = None

def get_validation_service() -> APIConfigValidationService:
    """Get the global validation service instance"""
    global _validation_service
    if _validation_service is None:
        _validation_service = APIConfigValidationService()
    return _validation_service
