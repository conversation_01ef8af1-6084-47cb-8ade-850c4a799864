"""
API Import/Export Service for configuration backup, restore, and migration
"""

from __future__ import annotations

import json
import yaml
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from io import StringIO
import aiohttp

from models.api import (
    APIConfiguration,
    APIEnvironment,
    APIStatus,
    AuthenticationType,
    RateLimitConfig,
    TimeoutConfig,
    RetryConfig,
    AuthenticationConfig,
    HealthCheckConfig
)
from models.base import now_utc
from api_v1.services.api_config import APIConfigurationService
from services.api_security import APISecurityService, APIPermission
from utils.validation import ValidationError

logger = logging.getLogger(__name__)


class APIImportExportService:
    """Service for importing and exporting API configurations"""
    
    def __init__(self):
        self.api_service = APIConfigurationService()
        self.security_service = APISecurityService()
    
    async def export_configurations(
        self,
        config_ids: Optional[List[str]] = None,
        format: str = "json",
        include_sensitive: bool = False,
        exported_by: str = ""
    ) -> Optional[str]:
        """Export API configurations to JSON or YAML format"""
        try:
            # Check permissions
            if not await self.security_service.check_permission(exported_by, APIPermission.EXPORT):
                raise PermissionError("Insufficient permissions to export configurations")
            
            # Get configurations to export
            if config_ids:
                configs = []
                for config_id in config_ids:
                    config = await self.api_service.get_api_config(
                        config_id, decrypt_sensitive=include_sensitive
                    )
                    if config:
                        configs.append(config)
            else:
                configs, _ = await self.api_service.list_api_configs(page=1, per_page=1000)
                if include_sensitive:
                    # Re-fetch with sensitive data
                    decrypted_configs = []
                    for config in configs:
                        decrypted_config = await self.api_service.get_api_config(
                            str(config.id), decrypt_sensitive=True
                        )
                        if decrypted_config:
                            decrypted_configs.append(decrypted_config)
                    configs = decrypted_configs
            
            # Prepare export data
            export_data = {
                "export_metadata": {
                    "version": "1.0",
                    "exported_at": now_utc().isoformat(),
                    "exported_by": exported_by,
                    "total_configs": len(configs),
                    "includes_sensitive_data": include_sensitive,
                    "format": format
                },
                "configurations": []
            }
            
            for config in configs:
                config_dict = config.dict()
                
                # Remove system fields that shouldn't be imported
                for field in ["id", "created_at", "updated_at", "deleted_at", "created_by", "last_modified_by"]:
                    config_dict.pop(field, None)
                
                # Handle sensitive data
                if not include_sensitive and "authentication" in config_dict:
                    auth = config_dict["authentication"]
                    # Redact sensitive fields
                    for sensitive_field in ["api_key", "bearer_token", "password", "oauth2_client_secret"]:
                        if sensitive_field in auth:
                            auth[sensitive_field] = "[REDACTED]"
                
                export_data["configurations"].append(config_dict)
            
            # Format output
            if format.lower() == "json":
                return json.dumps(export_data, indent=2, default=str)
            elif format.lower() == "yaml":
                return yaml.dump(export_data, default_flow_style=False, allow_unicode=True)
            else:
                raise ValueError(f"Unsupported export format: {format}")
                
        except Exception as e:
            logger.error(f"Failed to export configurations: {e}")
            return None
    
    async def import_configurations(
        self,
        import_data: str,
        format: str = "json",
        imported_by: str = "",
        overwrite_existing: bool = False,
        validate_only: bool = False
    ) -> Dict[str, Any]:
        """Import API configurations from JSON or YAML data"""
        try:
            # Check permissions
            if not await self.security_service.check_permission(imported_by, APIPermission.IMPORT):
                raise PermissionError("Insufficient permissions to import configurations")
            
            # Parse import data
            if format.lower() == "json":
                data = json.loads(import_data)
            elif format.lower() == "yaml":
                data = yaml.safe_load(import_data)
            else:
                raise ValueError(f"Unsupported import format: {format}")
            
            # Validate import data structure
            if not isinstance(data, dict) or "configurations" not in data:
                raise ValueError("Invalid import data structure")
            
            configurations = data["configurations"]
            if not isinstance(configurations, list):
                raise ValueError("Configurations must be a list")
            
            # Validate each configuration
            validation_results = []
            valid_configs = []
            
            for i, config_data in enumerate(configurations):
                try:
                    # Validate required fields
                    required_fields = ["name", "base_url", "authentication"]
                    for field in required_fields:
                        if field not in config_data:
                            raise ValueError(f"Missing required field: {field}")
                    
                    # Validate authentication structure
                    auth_data = config_data["authentication"]
                    if "type" not in auth_data:
                        raise ValueError("Authentication type is required")
                    
                    # Check for redacted sensitive data
                    has_redacted = False
                    for sensitive_field in ["api_key", "bearer_token", "password", "oauth2_client_secret"]:
                        if auth_data.get(sensitive_field) == "[REDACTED]":
                            has_redacted = True
                            break
                    
                    if has_redacted and not validate_only:
                        raise ValueError("Cannot import configuration with redacted sensitive data")
                    
                    # Try to create model instance for validation
                    test_config = APIConfiguration(**config_data)
                    
                    validation_results.append({
                        "index": i,
                        "name": config_data["name"],
                        "status": "valid",
                        "warnings": ["Contains redacted sensitive data"] if has_redacted else []
                    })
                    
                    if not has_redacted:
                        valid_configs.append(config_data)
                    
                except Exception as e:
                    validation_results.append({
                        "index": i,
                        "name": config_data.get("name", f"Config {i+1}"),
                        "status": "invalid",
                        "error": str(e)
                    })
            
            # If validation only, return results
            if validate_only:
                return {
                    "validation_only": True,
                    "total_configs": len(configurations),
                    "valid_configs": len(valid_configs),
                    "invalid_configs": len(configurations) - len(valid_configs),
                    "validation_results": validation_results
                }
            
            # Import valid configurations
            import_results = []
            successful_imports = 0
            
            for config_data in valid_configs:
                try:
                    # Check if API with same name already exists
                    existing_configs, _ = await self.api_service.list_api_configs(
                        page=1, per_page=1000, search=config_data["name"]
                    )
                    
                    name_exists = any(
                        c.name.lower() == config_data["name"].lower() 
                        for c in existing_configs
                    )
                    
                    if name_exists and not overwrite_existing:
                        import_results.append({
                            "name": config_data["name"],
                            "status": "skipped",
                            "reason": "API with same name already exists"
                        })
                        continue
                    
                    # Create the API configuration
                    api_config = await self.api_service.create_api_config(config_data, imported_by)
                    
                    import_results.append({
                        "name": config_data["name"],
                        "status": "imported",
                        "config_id": str(api_config.id)
                    })
                    
                    successful_imports += 1
                    
                except Exception as e:
                    import_results.append({
                        "name": config_data["name"],
                        "status": "failed",
                        "error": str(e)
                    })
            
            return {
                "validation_only": False,
                "total_configs": len(configurations),
                "valid_configs": len(valid_configs),
                "successful_imports": successful_imports,
                "failed_imports": len(valid_configs) - successful_imports,
                "validation_results": validation_results,
                "import_results": import_results
            }
            
        except Exception as e:
            logger.error(f"Failed to import configurations: {e}")
            return {"error": str(e)}
    
    async def import_from_url(
        self,
        url: str,
        format: str = "json",
        imported_by: str = "",
        validate_only: bool = False
    ) -> Dict[str, Any]:
        """Import API configurations from a URL"""
        try:
            # Check permissions
            if not await self.security_service.check_permission(imported_by, APIPermission.IMPORT):
                raise PermissionError("Insufficient permissions to import configurations")
            
            # Fetch data from URL
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status != 200:
                        raise ValueError(f"Failed to fetch data from URL: HTTP {response.status}")
                    
                    import_data = await response.text()
            
            # Import the data
            return await self.import_configurations(
                import_data, format, imported_by, validate_only=validate_only
            )
            
        except Exception as e:
            logger.error(f"Failed to import from URL {url}: {e}")
            return {"error": str(e)}
    
    async def create_backup(self, backup_name: Optional[str] = None) -> Optional[str]:
        """Create a complete backup of all API configurations"""
        try:
            if not backup_name:
                backup_name = f"api_backup_{now_utc().strftime('%Y%m%d_%H%M%S')}"
            
            # Export all configurations with sensitive data
            export_data = await self.export_configurations(
                config_ids=None,
                format="json",
                include_sensitive=True,
                exported_by="system_backup"
            )
            
            if export_data:
                # Store backup (in production, save to secure storage)
                backup_data = {
                    "backup_name": backup_name,
                    "created_at": now_utc(),
                    "data": export_data
                }
                
                logger.info(f"Created API configuration backup: {backup_name}")
                return json.dumps(backup_data, indent=2, default=str)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            return None
    
    async def restore_from_backup(
        self,
        backup_data: str,
        restored_by: str = "",
        clear_existing: bool = False
    ) -> Dict[str, Any]:
        """Restore API configurations from backup"""
        try:
            # Check permissions (requires super admin)
            if not await self.security_service.check_permission(restored_by, APIPermission.ADMIN):
                raise PermissionError("Insufficient permissions to restore from backup")
            
            # Parse backup data
            backup = json.loads(backup_data)
            
            if "data" not in backup:
                raise ValueError("Invalid backup format")
            
            # Clear existing configurations if requested
            if clear_existing:
                logger.warning("Clearing existing API configurations for restore")
                # This would be implemented with proper safeguards in production
            
            # Import configurations from backup
            return await self.import_configurations(
                backup["data"],
                format="json",
                imported_by=restored_by,
                overwrite_existing=True
            )
            
        except Exception as e:
            logger.error(f"Failed to restore from backup: {e}")
            return {"error": str(e)}
