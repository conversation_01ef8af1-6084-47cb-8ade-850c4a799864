"""
API Configuration Templates Service
Provides pre-built templates for common API types and configurations
"""

from __future__ import annotations

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from api_v1.services.api_config import (
    UnifiedAPIConfiguration,
    APIEndpointConfig,
    APICredentials,
    ConfigSource,
)

logger = logging.getLogger(__name__)


class TemplateCategory(str, Enum):
    """Template categories for organization"""

    ECOMMERCE = "ecommerce"
    PAYMENT = "payment"
    SOCIAL_MEDIA = "social_media"
    ANALYTICS = "analytics"
    NOTIFICATION = "notification"
    AUTHENTICATION = "authentication"
    DATABASE = "database"
    CLOUD_STORAGE = "cloud_storage"
    GENERAL = "general"
    # Multi-product categories
    BIN_CARDS = "bin_cards"
    DUMP_CARDS = "dump_cards"


@dataclass
class APIConfigTemplate:
    """Template for API configuration"""

    id: str
    name: str
    description: str
    category: TemplateCategory
    tags: List[str]
    icon: str
    base_url_placeholder: str
    endpoints: Dict[str, APIEndpointConfig]
    credentials_template: APICredentials
    required_fields: List[str]
    optional_fields: List[str]
    setup_instructions: str
    documentation_url: str = ""
    example_config: Dict[str, Any] = None

    def __post_init__(self):
        if self.example_config is None:
            self.example_config = {}


class APIConfigTemplateService:
    """Service for managing API configuration templates"""

    def __init__(self):
        self._templates: Dict[str, APIConfigTemplate] = {}
        self._initialize_default_templates()

    def _initialize_default_templates(self):
        """Initialize default templates for common API types"""

        # E-commerce Cart API Template
        self._templates["ecommerce_cart"] = APIConfigTemplate(
            id="ecommerce_cart",
            name="E-commerce Cart API",
            description="Template for shopping cart and checkout APIs",
            category=TemplateCategory.ECOMMERCE,
            tags=["cart", "checkout", "ecommerce", "shopping"],
            icon="🛒",
            base_url_placeholder="https://your-store.com/api",
            endpoints={
                "cart_view": APIEndpointConfig("cart_view", "/cart/", "GET", 30),
                "cart_add": APIEndpointConfig("cart_add", "/cart/", "POST", 30),
                "cart_remove": APIEndpointConfig("cart_remove", "/cart/", "DELETE", 30),
                "cart_update": APIEndpointConfig("cart_update", "/cart/", "PUT", 30),
                "checkout": APIEndpointConfig("checkout", "/checkout/", "POST", 60),
                "order_status": APIEndpointConfig(
                    "order_status", "/orders/{id}/", "GET", 30
                ),
            },
            credentials_template=APICredentials(
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                    "User-Agent": "Bot/1.0",
                }
            ),
            required_fields=["base_url", "login_token"],
            optional_fields=["session_cookies", "custom_headers"],
            setup_instructions=(
                "1. Enter your store's API base URL\n"
                "2. Provide authentication token or API key\n"
                "3. Configure session cookies if required\n"
                "4. Test the connection to verify setup"
            ),
            documentation_url="https://docs.example.com/cart-api",
        )

        # Payment Gateway Template
        self._templates["payment_gateway"] = APIConfigTemplate(
            id="payment_gateway",
            name="Payment Gateway API",
            description="Template for payment processing APIs",
            category=TemplateCategory.PAYMENT,
            tags=["payment", "gateway", "transaction", "billing"],
            icon="💳",
            base_url_placeholder="https://api.paymentgateway.com/v1",
            endpoints={
                "create_payment": APIEndpointConfig(
                    "create_payment", "/payments/", "POST", 45
                ),
                "get_payment": APIEndpointConfig(
                    "get_payment", "/payments/{id}/", "GET", 30
                ),
                "capture_payment": APIEndpointConfig(
                    "capture_payment", "/payments/{id}/capture/", "POST", 45
                ),
                "refund_payment": APIEndpointConfig(
                    "refund_payment", "/payments/{id}/refund/", "POST", 45
                ),
                "list_payments": APIEndpointConfig(
                    "list_payments", "/payments/", "GET", 30
                ),
            },
            credentials_template=APICredentials(
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                }
            ),
            required_fields=["base_url", "api_key"],
            optional_fields=["webhook_secret", "merchant_id"],
            setup_instructions=(
                "1. Enter payment gateway API URL\n"
                "2. Provide API key from your gateway account\n"
                "3. Set webhook secret for payment notifications\n"
                "4. Configure merchant ID if required"
            ),
        )

        # REST API Template
        self._templates["rest_api"] = APIConfigTemplate(
            id="rest_api",
            name="Generic REST API",
            description="Template for standard REST APIs",
            category=TemplateCategory.GENERAL,
            tags=["rest", "api", "generic", "standard"],
            icon="🔗",
            base_url_placeholder="https://api.example.com/v1",
            endpoints={
                "list": APIEndpointConfig("list", "/items/", "GET", 30),
                "create": APIEndpointConfig("create", "/items/", "POST", 30),
                "get": APIEndpointConfig("get", "/items/{id}/", "GET", 30),
                "update": APIEndpointConfig("update", "/items/{id}/", "PUT", 30),
                "delete": APIEndpointConfig("delete", "/items/{id}/", "DELETE", 30),
            },
            credentials_template=APICredentials(
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                }
            ),
            required_fields=["base_url"],
            optional_fields=["api_key", "bearer_token"],
            setup_instructions=(
                "1. Enter the API base URL\n"
                "2. Choose authentication method (API key or Bearer token)\n"
                "3. Configure any required headers\n"
                "4. Test the connection"
            ),
        )

        # Notification Service Template
        self._templates["notification_service"] = APIConfigTemplate(
            id="notification_service",
            name="Notification Service",
            description="Template for notification and messaging APIs",
            category=TemplateCategory.NOTIFICATION,
            tags=["notification", "messaging", "email", "sms"],
            icon="📧",
            base_url_placeholder="https://api.notifications.com/v1",
            endpoints={
                "send_email": APIEndpointConfig(
                    "send_email", "/email/send/", "POST", 30
                ),
                "send_sms": APIEndpointConfig("send_sms", "/sms/send/", "POST", 30),
                "send_push": APIEndpointConfig("send_push", "/push/send/", "POST", 30),
                "get_status": APIEndpointConfig(
                    "get_status", "/messages/{id}/status/", "GET", 30
                ),
                "list_templates": APIEndpointConfig(
                    "list_templates", "/templates/", "GET", 30
                ),
            },
            credentials_template=APICredentials(
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                }
            ),
            required_fields=["base_url", "api_key"],
            optional_fields=["sender_id", "webhook_url"],
            setup_instructions=(
                "1. Enter notification service API URL\n"
                "2. Provide API key from your account\n"
                "3. Set sender ID for SMS/email\n"
                "4. Configure webhook URL for delivery status"
            ),
        )

        # API 1 External Cart API Template (based on demo examples)
        self._templates["api1_external_cart_api"] = APIConfigTemplate(
            id="api1_external_cart_api",
            name="API 1 - External Cart API (Ronaldo Club)",
            description="Template for API 1 external cart API integration with comprehensive operations",
            category=TemplateCategory.ECOMMERCE,
            tags=["api1", "external", "cart", "ronaldo", "club", "api", "ecommerce"],
            icon="🏪",
            base_url_placeholder="https://ronaldo-club.to/api",
            endpoints={
                "list_items": APIEndpointConfig(
                    "list_items", "/cards/hq/list", "POST", 30
                ),
                "cart_view": APIEndpointConfig("cart_view", "/cart/", "GET", 30),
                "cart_add": APIEndpointConfig("cart_add", "/cart/", "POST", 30),
                "cart_remove": APIEndpointConfig(
                    "cart_remove", "/cart/{id}/", "DELETE", 30
                ),
                "user_info": APIEndpointConfig("user_info", "/user/getme", "GET", 30),
                "checkout": APIEndpointConfig("checkout", "/checkout/", "POST", 60),
            },
            credentials_template=APICredentials(
                session_cookies={
                    "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
                    "testcookie": "1",
                    "__ddg8_": "",
                    "__ddg9_": "",
                    "__ddg10_": "",
                },
                headers={
                    "accept": "application/json, text/plain, */*",
                    "accept-language": "en-US,en;q=0.9",
                    "origin": "https://ronaldo-club.to",
                    "priority": "u=1, i",
                    "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
                    "sec-ch-ua-mobile": "?1",
                    "sec-ch-ua-platform": '"Android"',
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "sec-gpc": "1",
                    "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
                    "AppleWebKit/537.36 (KHTML, like Gecko) "
                    "Chrome/140.0.0.0 Mobile Safari/537.36",
                },
            ),
            required_fields=["base_url", "login_token"],
            optional_fields=["session_cookies", "custom_headers"],
            setup_instructions=(
                "1. Enter the external API base URL (https://ronaldo-club.to/api)\n"
                "2. Provide your JWT login token from browser cookies\n"
                "3. Configure session cookies (__ddg1_, __ddg8_, __ddg9_, __ddg10_)\n"
                "4. Test the connection using the user info endpoint\n"
                "5. Verify cart operations work correctly"
            ),
            documentation_url="https://ronaldo-club.to/api/docs",
            example_config={
                "service_name": "external_cart",
                "base_url": "https://ronaldo-club.to/api",
                "login_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTc2NzcxODMsImV4cCI6MTc2MDI2OTE4M30.ZBLCQc3MjKFBXG1w_DEebJ2Q9esUZ0NOkDYlym5NyVI",
                "session_cookies": {
                    "__ddg1_": "u1UaBqLkngSC4ZTJRDQC",
                    "__ddg8_": "VIywnRkIRMee1yvS",
                    "__ddg9_": "**********",
                    "__ddg10_": "1757322900",
                    "testcookie": "1",
                },
            },
        )

        # BIN BASE 2 API Template
        self._templates["bin_base_2"] = APIConfigTemplate(
            id="bin_base_2",
            name="BIN BASE 2 API",
            description="Secondary BIN browse API using the VHQ endpoints",
            category=TemplateCategory.BIN_CARDS,
            tags=["bin", "cards", "secondary", "api2", "vhq"],
            icon="💳",
            base_url_placeholder="https://ronaldo-club.to/api/cards/vhq/",
            endpoints={
                "list_items": APIEndpointConfig("list_items", "/list", "POST", 30),
                "filters": APIEndpointConfig("filters", "/filters", "GET", 30),
                "orders": APIEndpointConfig("orders", "/orders", "GET", 30),
                "check_order": APIEndpointConfig("check_order", "/check", "POST", 30),
            },
            credentials_template=APICredentials(
                login_token="",
                headers={
                    "accept": "application/json, text/plain, */*",
                    "content-type": "application/json",
                    "authorization": "Bearer ",
                },
            ),
            required_fields=["base_url", "login_token"],
            optional_fields=["session_cookies", "custom_headers"],
            documentation_url="https://ronaldo-club.to/api/docs",
            setup_instructions=(
                "1. Copy the BASE 2 VHQ base URL from the control panel\n"
                "2. Provide the bearer token captured from the authenticated session\n"
                "3. (Optional) Add session cookies for hardened rate limits\n"
                "4. Test the browse endpoints via the validation script"
            ),
        )

        # BIN BASE 3 API Template
        self._templates["bin_base_3"] = APIConfigTemplate(
            id="bin_base_3",
            name="BIN BASE 3 API",
            description="Tertiary BIN API endpoint for specialized card data",
            category=TemplateCategory.BIN_CARDS,
            tags=["bin", "cards", "tertiary", "api3", "specialized"],
            icon="💳",
            base_url_placeholder="https://api3.example.com/api",
            endpoints={
                "list_items": APIEndpointConfig("list_items", "/v3/cards/search", "POST", 45),
                "cart_view": APIEndpointConfig("cart_view", "/v3/cart", "GET", 30),
                "cart_add": APIEndpointConfig("cart_add", "/v3/cart/items", "POST", 30),
                "cart_remove": APIEndpointConfig("cart_remove", "/v3/cart/items", "DELETE", 30),
                "checkout": APIEndpointConfig("checkout", "/v3/orders/create", "POST", 60),
                "user_info": APIEndpointConfig("user_info", "/v3/user/me", "GET", 30),
            },
            credentials_template=APICredentials(
                login_token="",
                headers={"Content-Type": "application/json", "X-API-Key": ""}
            ),
            required_fields=["base_url", "api_key"],
            optional_fields=["timeout", "retry_count"],
            documentation_url="https://api3.example.com/v3/docs",
            setup_instructions=(
                "1. Register for API key\n"
                "2. Configure base URL\n"
                "3. Set X-API-Key header\n"
                "4. Test endpoint connectivity"
            ),
        )

        # DUMP BASE 1 API Template
        self._templates["dump_base_1"] = APIConfigTemplate(
            id="dump_base_1",
            name="DUMP BASE 1 API",
            description="Primary DUMP API for complete card data sets",
            category=TemplateCategory.DUMP_CARDS,
            tags=["dump", "complete-data", "primary", "full-sets"],
            icon="🗂️",
            base_url_placeholder="https://dump1.example.com/api",
            endpoints={
                "list_items": APIEndpointConfig("list_items", "/dumps/search", "POST", 60),
                "cart_view": APIEndpointConfig("cart_view", "/cart/view", "GET", 30),
                "cart_add": APIEndpointConfig("cart_add", "/cart/add-dump", "POST", 45),
                "cart_remove": APIEndpointConfig("cart_remove", "/cart/remove-dump", "DELETE", 30),
                "checkout": APIEndpointConfig("checkout", "/orders/purchase-dumps", "POST", 120),
                "user_info": APIEndpointConfig("user_info", "/user/profile", "GET", 30),
            },
            credentials_template=APICredentials(
                login_token="",
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                    "X-Client-Type": "dump-client",
                    "Authorization": "Bearer "
                }
            ),
            required_fields=["base_url", "bearer_token"],
            optional_fields=["timeout", "retry_count"],
            documentation_url="https://dump1.example.com/docs",
            setup_instructions=(
                "1. Obtain bearer token for dump access\n"
                "2. Configure base URL\n"
                "3. Set client type header\n"
                "4. Test dump search functionality"
            ),
        )

        # DUMP BASE 2 API Template
        self._templates["dump_base_2"] = APIConfigTemplate(
            id="dump_base_2",
            name="DUMP BASE 2 API",
            description="Secondary DUMP API for alternative data sources",
            category=TemplateCategory.DUMP_CARDS,
            tags=["dump", "alternative", "secondary", "v2"],
            icon="🗂️",
            base_url_placeholder="https://dump2.example.com/api",
            endpoints={
                "list_items": APIEndpointConfig("list_items", "/v2/dumps/query", "POST", 60),
                "cart_view": APIEndpointConfig("cart_view", "/v2/basket", "GET", 30),
                "cart_add": APIEndpointConfig("cart_add", "/v2/basket/items", "PUT", 45),
                "cart_remove": APIEndpointConfig("cart_remove", "/v2/basket/items", "DELETE", 30),
                "checkout": APIEndpointConfig("checkout", "/v2/transactions/create", "POST", 120),
                "user_info": APIEndpointConfig("user_info", "/v2/account/info", "GET", 30),
            },
            credentials_template=APICredentials(
                login_token="",
                headers={
                    "Authorization": "",
                    "X-API-Version": "2.0",
                    "Content-Type": "application/json"
                }
            ),
            required_fields=["base_url", "authorization_header"],
            optional_fields=["timeout", "retry_count"],
            documentation_url="https://dump2.example.com/v2/docs",
            setup_instructions=(
                "1. Obtain authorization credentials\n"
                "2. Configure base URL\n"
                "3. Set API version header\n"
                "4. Test v2 endpoint access"
            ),
        )

    def get_template(self, template_id: str) -> Optional[APIConfigTemplate]:
        """Get a specific template by ID"""
        return self._templates.get(template_id)

    def list_templates(
        self, category: Optional[TemplateCategory] = None
    ) -> List[APIConfigTemplate]:
        """List all templates, optionally filtered by category"""
        templates = list(self._templates.values())
        if category:
            templates = [t for t in templates if t.category == category]
        return sorted(templates, key=lambda t: t.name)

    def get_categories(self) -> List[TemplateCategory]:
        """Get all available template categories"""
        return list(TemplateCategory)

    def search_templates(self, query: str) -> List[APIConfigTemplate]:
        """Search templates by name, description, or tags"""
        query = query.lower()
        results = []

        for template in self._templates.values():
            if (
                query in template.name.lower()
                or query in template.description.lower()
                or any(query in tag.lower() for tag in template.tags)
            ):
                results.append(template)

        return sorted(results, key=lambda t: t.name)

    def create_config_from_template(
        self, template_id: str, service_name: str, base_url: str, **kwargs
    ) -> Optional[UnifiedAPIConfiguration]:
        """Create an API configuration from a template"""
        template = self.get_template(template_id)
        if not template:
            return None

        # Build endpoints with actual base URL
        endpoints = {}
        for name, endpoint in template.endpoints.items():
            endpoint_url = base_url.rstrip("/") + endpoint.url
            endpoints[name] = APIEndpointConfig(
                name=endpoint.name,
                url=endpoint_url,
                method=endpoint.method,
                timeout=endpoint.timeout,
                retry_count=endpoint.retry_count,
                retry_delays=endpoint.retry_delays,
            )

        # Build credentials from template and provided values
        credentials = APICredentials(
            login_token=kwargs.get("login_token", ""),
            session_cookies=kwargs.get("session_cookies", {}),
            headers={
                **template.credentials_template.headers,
                **kwargs.get("headers", {}),
            },
        )

        # Create configuration
        config = UnifiedAPIConfiguration(
            service_name=service_name,
            base_url=base_url,
            endpoints=endpoints,
            credentials=credentials,
            enabled=True,
            source=ConfigSource.ADMIN_PANEL,
            display_name=kwargs.get("display_name", template.name),
            description=kwargs.get("description", template.description),
            category=template.category.value,
            tags=template.tags.copy(),
            environment=kwargs.get("environment", "development"),
            documentation_url=template.documentation_url,
        )

        return config


# Global instance
_template_service = None


def get_template_service() -> APIConfigTemplateService:
    """Get the global template service instance"""
    global _template_service
    if _template_service is None:
        _template_service = APIConfigTemplateService()
    return _template_service
