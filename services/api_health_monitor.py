"""
API Health Monitoring Service for periodic health checks, metrics collection,
and alerting for API configurations.
"""

from __future__ import annotations

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
import time

from models.api import (
    APIConfiguration,
    APIHealthStatus,
    APIUsageMetrics,
    APIRequestLog,
    APIStatus,
    HTTPMethod,
)
from models.base import now_utc
from database.connection import get_collection
from api_v1.services.api_config import APIConfigurationService
from config.settings import get_settings

logger = logging.getLogger(__name__)


class APIHealthMonitor:
    """Service for monitoring API health and collecting metrics"""

    def __init__(self):
        self.settings = get_settings()
        self.api_service = APIConfigurationService()
        self.api_configs = get_collection("api_configurations")
        self.api_health = get_collection("api_health_status")
        self.api_metrics = get_collection("api_usage_metrics")
        self.api_logs = get_collection("api_request_logs")
        self._monitoring_tasks: Dict[str, asyncio.Task] = {}
        self._is_running = False

    async def start_monitoring(self) -> None:
        """Start the health monitoring service"""
        if self._is_running:
            logger.warning("Health monitoring is already running")
            return

        self._is_running = True
        logger.info("Starting API health monitoring service")

        # Start the main monitoring loop
        asyncio.create_task(self._monitoring_loop())

    async def stop_monitoring(self) -> None:
        """Stop the health monitoring service"""
        self._is_running = False

        # Cancel all monitoring tasks
        for task in self._monitoring_tasks.values():
            task.cancel()

        self._monitoring_tasks.clear()
        logger.info("Stopped API health monitoring service")

    async def _monitoring_loop(self) -> None:
        """Main monitoring loop that schedules health checks"""
        while self._is_running:
            try:
                # Get all active API configurations
                configs, _ = await self.api_service.list_api_configs(
                    page=1, per_page=1000, status=APIStatus.ACTIVE
                )

                current_time = now_utc()

                for config in configs:
                    if not config.health_check.enabled:
                        continue

                    # Get current health status
                    health = await self._get_or_create_health_status(str(config.id))

                    # Check if it's time for a health check
                    # Ensure timezone-aware comparison
                    next_check_at = health.next_check_at
                    if next_check_at.tzinfo is None:
                        next_check_at = next_check_at.replace(tzinfo=dt.timezone.utc)

                    if current_time >= next_check_at:
                        # Schedule health check
                        task_key = f"health_check_{config.id}"
                        if task_key not in self._monitoring_tasks:
                            task = asyncio.create_task(
                                self._perform_health_check(config, health)
                            )
                            self._monitoring_tasks[task_key] = task

                # Clean up completed tasks
                completed_tasks = [
                    key for key, task in self._monitoring_tasks.items() if task.done()
                ]
                for key in completed_tasks:
                    del self._monitoring_tasks[key]

                # Wait before next monitoring cycle
                await asyncio.sleep(30)  # Check every 30 seconds

            except asyncio.CancelledError:
                # Exit promptly on cancellation
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                # Short backoff; don't delay shutdown
                try:
                    await asyncio.sleep(5)
                except asyncio.CancelledError:
                    break

    async def _perform_health_check(
        self, config: APIConfiguration, health: APIHealthStatus
    ) -> None:
        """Perform health check for a single API"""
        try:
            logger.debug(f"Performing health check for API: {config.name}")

            start_time = time.time()

            # Build test URL
            test_url = config.base_url
            if config.health_check.endpoint:
                test_url += config.health_check.endpoint

            # Prepare headers
            headers = config.default_headers.copy()
            auth_headers = self.api_service._build_auth_headers(config.authentication)
            headers.update(auth_headers)

            # Perform the health check request
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=config.health_check.timeout_seconds)
            ) as session:
                async with session.request(
                    config.health_check.method.value,
                    test_url,
                    headers=headers,
                    params=config.default_params,
                ) as response:
                    response_time_ms = int((time.time() - start_time) * 1000)
                    response_text = await response.text()

                    # Determine if check passed
                    is_healthy = (
                        response.status in config.health_check.expected_status_codes
                        and (
                            not config.health_check.expected_response_contains
                            or config.health_check.expected_response_contains
                            in response_text
                        )
                    )

                    # Update health status
                    await self._update_health_status(
                        config,
                        health,
                        is_healthy,
                        response.status,
                        response_time_ms,
                        None,
                    )

                    # Log the request
                    await self._log_health_check_request(
                        config,
                        test_url,
                        response.status,
                        response_time_ms,
                        len(response_text),
                        None,
                    )

                    logger.debug(
                        f"Health check completed for {config.name}: {'✅' if is_healthy else '❌'}"
                    )

        except asyncio.CancelledError:
            # Allow cancellation to propagate so tasks end quickly
            raise
        except asyncio.TimeoutError:
            logger.warning(f"Health check timeout for API: {config.name}")
            await self._update_health_status(
                config, health, False, None, None, "Request timeout"
            )
            await self._log_health_check_request(
                config, test_url, None, None, None, "Request timeout"
            )

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Health check failed for API {config.name}: {error_msg}")
            await self._update_health_status(
                config, health, False, None, None, error_msg
            )
            await self._log_health_check_request(
                config, test_url, None, None, None, error_msg
            )

    async def _get_or_create_health_status(self, config_id: str) -> APIHealthStatus:
        """Get existing health status or create new one"""
        try:
            doc = await self.api_health.find_one({"api_config_id": config_id})
            if doc:
                return APIHealthStatus.from_mongo(doc)

            # Create new health status
            health = APIHealthStatus(
                api_config_id=config_id,
                is_healthy=True,
                last_check_at=now_utc(),
                next_check_at=now_utc() + timedelta(minutes=5),
                consecutive_failures=0,
                consecutive_successes=0,
                total_checks=0,
                total_failures=0,
                uptime_percentage=100.0,
            )

            # Use upsert to avoid duplicate key errors
            await self.api_health.update_one(
                {"api_config_id": config_id},
                {"$setOnInsert": health.to_mongo()},
                upsert=True,
            )
            return health

        except Exception as e:
            logger.error(f"Failed to get/create health status for {config_id}: {e}")
            raise

    async def _update_health_status(
        self,
        config: APIConfiguration,
        health: APIHealthStatus,
        is_healthy: bool,
        status_code: Optional[int],
        response_time_ms: Optional[int],
        error_message: Optional[str],
    ) -> None:
        """Update health status after a check"""
        try:
            current_time = now_utc()

            # Update counters
            health.total_checks += 1

            if is_healthy:
                health.consecutive_successes += 1
                health.consecutive_failures = 0

                # Mark as healthy if we've reached success threshold
                if (
                    health.consecutive_successes
                    >= config.health_check.success_threshold
                ):
                    health.is_healthy = True
            else:
                health.consecutive_failures += 1
                health.consecutive_successes = 0
                health.total_failures += 1

                # Mark as unhealthy if we've reached failure threshold
                if health.consecutive_failures >= config.health_check.failure_threshold:
                    health.is_healthy = False
                    if not health.last_downtime_at:
                        health.last_downtime_at = current_time

            # Update metrics
            health.last_check_at = current_time
            health.next_check_at = current_time + timedelta(
                minutes=config.health_check.interval_minutes
            )
            health.response_time_ms = response_time_ms
            health.status_code = status_code
            health.error_message = error_message

            # Calculate uptime percentage
            if health.total_checks > 0:
                health.uptime_percentage = (
                    (health.total_checks - health.total_failures) / health.total_checks
                ) * 100

            # Update in database (exclude _id field to avoid immutable field error)
            await self.api_health.update_one(
                {"api_config_id": str(config.id)},
                {"$set": health.to_update_dict()},
                upsert=True,
            )

            # Update API status if health changed significantly
            if (
                not is_healthy
                and health.consecutive_failures >= config.health_check.failure_threshold
            ):
                await self.api_service.update_api_config(
                    str(config.id), {"status": APIStatus.ERROR.value}, "health_monitor"
                )
            elif is_healthy and config.status == APIStatus.ERROR:
                await self.api_service.update_api_config(
                    str(config.id), {"status": APIStatus.ACTIVE.value}, "health_monitor"
                )

        except Exception as e:
            logger.error(f"Failed to update health status for {config.id}: {e}")

    async def _log_health_check_request(
        self,
        config: APIConfiguration,
        url: str,
        status_code: Optional[int],
        response_time_ms: Optional[int],
        response_size_bytes: Optional[int],
        error_message: Optional[str],
    ) -> None:
        """Log health check request for analytics"""
        try:
            log_entry = APIRequestLog(
                api_config_id=str(config.id),
                method=config.health_check.method,
                url=url,
                status_code=status_code,
                response_time_ms=response_time_ms,
                response_size_bytes=response_size_bytes,
                error_message=error_message,
                initiated_by="health_monitor",
                created_at=now_utc(),
            )

            await self.api_logs.insert_one(log_entry.to_mongo())

        except Exception as e:
            logger.error(f"Failed to log health check request: {e}")

    async def get_health_summary(self) -> Dict[str, Any]:
        """Get overall health summary for all APIs"""
        try:
            # Get all health statuses
            cursor = self.api_health.find({})
            health_docs = await cursor.to_list(None)

            total_apis = len(health_docs)
            healthy_apis = sum(1 for doc in health_docs if doc.get("is_healthy", False))
            unhealthy_apis = total_apis - healthy_apis

            # Calculate average uptime
            avg_uptime = (
                sum(doc.get("uptime_percentage", 0) for doc in health_docs) / total_apis
                if total_apis > 0
                else 0
            )

            # Get APIs with recent failures
            recent_failures = [
                doc for doc in health_docs if doc.get("consecutive_failures", 0) > 0
            ]

            return {
                "total_apis": total_apis,
                "healthy_apis": healthy_apis,
                "unhealthy_apis": unhealthy_apis,
                "health_percentage": (
                    (healthy_apis / total_apis * 100) if total_apis > 0 else 0
                ),
                "average_uptime": avg_uptime,
                "recent_failures": len(recent_failures),
                "last_updated": now_utc(),
            }

        except Exception as e:
            logger.error(f"Failed to get health summary: {e}")
            return {
                "total_apis": 0,
                "healthy_apis": 0,
                "unhealthy_apis": 0,
                "health_percentage": 0,
                "average_uptime": 0,
                "recent_failures": 0,
                "last_updated": now_utc(),
            }

    async def force_health_check(self, config_id: str) -> Dict[str, Any]:
        """Force an immediate health check for a specific API"""
        try:
            config = await self.api_service.get_api_config(
                config_id, decrypt_sensitive=True
            )
            if not config:
                return {"success": False, "error": "API configuration not found"}

            health = await self._get_or_create_health_status(config_id)

            # Perform the health check
            await self._perform_health_check(config, health)

            # Get updated health status
            updated_health = await self._get_or_create_health_status(config_id)

            return {
                "success": True,
                "is_healthy": updated_health.is_healthy,
                "response_time_ms": updated_health.response_time_ms,
                "status_code": updated_health.status_code,
                "error_message": updated_health.error_message,
                "last_check_at": updated_health.last_check_at,
            }

        except Exception as e:
            logger.error(f"Failed to force health check for {config_id}: {e}")
            return {"success": False, "error": str(e)}

    async def get_unhealthy_apis(self) -> List[Dict[str, Any]]:
        """Get list of unhealthy APIs with details"""
        try:
            # Get unhealthy health statuses
            cursor = self.api_health.find({"is_healthy": False})
            unhealthy_docs = await cursor.to_list(None)

            results = []
            for doc in unhealthy_docs:
                config_id = doc["api_config_id"]
                config = await self.api_service.get_api_config(config_id)

                if config:
                    results.append(
                        {
                            "config_id": config_id,
                            "name": config.name,
                            "base_url": config.base_url,
                            "environment": config.environment.value,
                            "consecutive_failures": doc.get("consecutive_failures", 0),
                            "last_check_at": doc.get("last_check_at"),
                            "error_message": doc.get("error_message"),
                            "uptime_percentage": doc.get("uptime_percentage", 0),
                        }
                    )

            return results

        except Exception as e:
            logger.error(f"Failed to get unhealthy APIs: {e}")
            return []

    async def collect_usage_metrics(self, period_type: str = "hourly") -> None:
        """Collect and aggregate usage metrics for all APIs"""
        try:
            logger.info(f"Collecting {period_type} usage metrics")

            # Define time periods
            current_time = now_utc()
            if period_type == "hourly":
                period_start = current_time.replace(minute=0, second=0, microsecond=0)
                period_end = period_start + timedelta(hours=1)
            elif period_type == "daily":
                period_start = current_time.replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
                period_end = period_start + timedelta(days=1)
            else:
                logger.warning(f"Unsupported period type: {period_type}")
                return

            # Get all API configurations
            configs, _ = await self.api_service.list_api_configs(page=1, per_page=1000)

            for config in configs:
                await self._collect_api_metrics(
                    str(config.id), period_start, period_end, period_type
                )

            logger.info(
                f"Completed {period_type} metrics collection for {len(configs)} APIs"
            )

        except Exception as e:
            logger.error(f"Failed to collect usage metrics: {e}")

    async def _collect_api_metrics(
        self,
        config_id: str,
        period_start: datetime,
        period_end: datetime,
        period_type: str,
    ) -> None:
        """Collect metrics for a single API in the given period"""
        try:
            # Ensure datetime objects are timezone-aware for MongoDB comparison
            if period_start.tzinfo is None:
                period_start = period_start.replace(tzinfo=dt.timezone.utc)
            if period_end.tzinfo is None:
                period_end = period_end.replace(tzinfo=dt.timezone.utc)

            # Query request logs for the period
            cursor = self.api_logs.find(
                {
                    "api_config_id": config_id,
                    "created_at": {"$gte": period_start, "$lt": period_end},
                }
            )

            logs = await cursor.to_list(None)

            if not logs:
                return

            # Calculate metrics
            total_requests = len(logs)
            successful_requests = sum(
                1 for log in logs if 200 <= (log.get("status_code") or 0) < 300
            )
            failed_requests = total_requests - successful_requests

            # Response time metrics
            response_times = [
                log.get("response_time_ms", 0)
                for log in logs
                if log.get("response_time_ms")
            ]
            avg_response_time = (
                sum(response_times) / len(response_times) if response_times else 0
            )
            min_response_time = min(response_times) if response_times else 0
            max_response_time = max(response_times) if response_times else 0

            # Calculate percentiles
            sorted_times = sorted(response_times)
            p95_index = int(len(sorted_times) * 0.95)
            p99_index = int(len(sorted_times) * 0.99)
            p95_response_time = sorted_times[p95_index] if sorted_times else 0
            p99_response_time = sorted_times[p99_index] if sorted_times else 0

            # Status code breakdown
            status_2xx = sum(
                1 for log in logs if 200 <= (log.get("status_code") or 0) < 300
            )
            status_3xx = sum(
                1 for log in logs if 300 <= (log.get("status_code") or 0) < 400
            )
            status_4xx = sum(
                1 for log in logs if 400 <= (log.get("status_code") or 0) < 500
            )
            status_5xx = sum(
                1 for log in logs if 500 <= (log.get("status_code") or 0) < 600
            )

            # Error metrics
            error_rate = (
                (failed_requests / total_requests * 100) if total_requests > 0 else 0
            )
            timeout_count = sum(
                1
                for log in logs
                if "timeout" in (log.get("error_message") or "").lower()
            )
            connection_error_count = sum(
                1
                for log in logs
                if "connection" in (log.get("error_message") or "").lower()
            )

            # Data transfer
            total_bytes_sent = sum(log.get("request_size_bytes", 0) for log in logs)
            total_bytes_received = sum(
                log.get("response_size_bytes", 0) for log in logs
            )

            # Create metrics document
            metrics = APIUsageMetrics(
                api_config_id=config_id,
                period_start=period_start,
                period_end=period_end,
                period_type=period_type,
                total_requests=total_requests,
                successful_requests=successful_requests,
                failed_requests=failed_requests,
                avg_response_time_ms=avg_response_time,
                min_response_time_ms=min_response_time,
                max_response_time_ms=max_response_time,
                p95_response_time_ms=p95_response_time,
                p99_response_time_ms=p99_response_time,
                error_rate=error_rate,
                timeout_count=timeout_count,
                connection_error_count=connection_error_count,
                status_2xx_count=status_2xx,
                status_3xx_count=status_3xx,
                status_4xx_count=status_4xx,
                status_5xx_count=status_5xx,
                total_bytes_sent=total_bytes_sent,
                total_bytes_received=total_bytes_received,
                created_at=now_utc(),
            )

            # Upsert metrics (replace if exists for same period)
            await self.api_metrics.update_one(
                {
                    "api_config_id": config_id,
                    "period_start": period_start,
                    "period_type": period_type,
                },
                {"$set": metrics.to_mongo()},
                upsert=True,
            )

            logger.debug(
                f"Collected metrics for API {config_id}: {total_requests} requests"
            )

        except Exception as e:
            logger.error(f"Failed to collect metrics for API {config_id}: {e}")

    async def cleanup_old_data(self, days_to_keep: int = 90) -> None:
        """Clean up old monitoring data"""
        try:
            cutoff_date = now_utc() - timedelta(days=days_to_keep)

            # Ensure timezone-aware datetime for MongoDB comparison
            if cutoff_date.tzinfo is None:
                cutoff_date = cutoff_date.replace(tzinfo=dt.timezone.utc)

            # Clean up old request logs
            result = await self.api_logs.delete_many(
                {"created_at": {"$lt": cutoff_date}}
            )
            logger.info(f"Cleaned up {result.deleted_count} old request logs")

            # Clean up old metrics (keep longer for historical analysis)
            metrics_cutoff = now_utc() - timedelta(days=days_to_keep * 2)
            if metrics_cutoff.tzinfo is None:
                metrics_cutoff = metrics_cutoff.replace(tzinfo=dt.timezone.utc)

            result = await self.api_metrics.delete_many(
                {"created_at": {"$lt": metrics_cutoff}}
            )
            logger.info(f"Cleaned up {result.deleted_count} old metrics records")

        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")


# Note: Avoid creating global instances at import time to prevent
# side effects during test collection or when DB is not connected.
# Create APIHealthMonitor() explicitly where needed.
