"""
API Testing Service for comprehensive API testing capabilities
including request building, load testing, and test history
"""

from __future__ import annotations

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import aiohttp
from urllib.parse import urljoin, urlparse

from models.api import (
    APIConfiguration,
    APIRequestLog,
    HTTPMethod,
    AuthenticationType
)
from models.base import now_utc
from database.connection import get_collection
from api_v1.services.api_config import APIConfigurationService
from services.api_security import APISecurityService, APIPermission

logger = logging.getLogger(__name__)


class APITestResult:
    """Container for API test results"""
    
    def __init__(self):
        self.success: bool = False
        self.status_code: Optional[int] = None
        self.response_time_ms: Optional[int] = None
        self.response_headers: Dict[str, str] = {}
        self.response_body: Optional[str] = None
        self.response_size_bytes: Optional[int] = None
        self.error_message: Optional[str] = None
        self.request_details: Dict[str, Any] = {}
        self.timestamp: datetime = now_utc()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/display"""
        return {
            "success": self.success,
            "status_code": self.status_code,
            "response_time_ms": self.response_time_ms,
            "response_headers": self.response_headers,
            "response_body": self.response_body,
            "response_size_bytes": self.response_size_bytes,
            "error_message": self.error_message,
            "request_details": self.request_details,
            "timestamp": self.timestamp
        }


class APITestingService:
    """Service for testing APIs with various methods"""
    
    def __init__(self):
        self.api_service = APIConfigurationService()
        self.security_service = APISecurityService()
        self.test_history = get_collection("api_test_history")
        self.api_logs = get_collection("api_request_logs")
    
    async def test_api_endpoint(
        self,
        config_id: str,
        endpoint_path: str = "",
        method: HTTPMethod = HTTPMethod.GET,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, str]] = None,
        body: Optional[str] = None,
        tested_by: str = ""
    ) -> APITestResult:
        """Test a specific API endpoint"""
        try:
            # Check permissions
            if not await self.security_service.check_permission(tested_by, APIPermission.TEST):
                raise PermissionError("Insufficient permissions to test APIs")
            
            # Get API configuration
            config = await self.api_service.get_api_config(config_id, decrypt_sensitive=True)
            if not config:
                raise ValueError("API configuration not found")
            
            result = APITestResult()
            
            # Build request URL
            test_url = config.base_url
            if endpoint_path:
                test_url = urljoin(test_url, endpoint_path.lstrip('/'))
            
            # Prepare headers
            request_headers = config.default_headers.copy()
            if headers:
                request_headers.update(headers)
            
            # Add authentication headers
            auth_headers = self.api_service._build_auth_headers(config.authentication)
            request_headers.update(auth_headers)
            
            # Prepare parameters
            request_params = config.default_params.copy()
            if params:
                request_params.update(params)
            
            # Store request details
            result.request_details = {
                "url": test_url,
                "method": method.value,
                "headers": {k: v for k, v in request_headers.items() if "auth" not in k.lower()},  # Hide auth headers
                "params": request_params,
                "body": body,
                "config_id": config_id,
                "tested_by": tested_by
            }
            
            # Make the request
            start_time = time.time()
            
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=config.timeout.total_timeout)
            ) as session:
                async with session.request(
                    method.value,
                    test_url,
                    headers=request_headers,
                    params=request_params,
                    data=body.encode() if body else None
                ) as response:
                    response_time = int((time.time() - start_time) * 1000)
                    response_text = await response.text()
                    
                    # Populate result
                    result.success = 200 <= response.status < 400
                    result.status_code = response.status
                    result.response_time_ms = response_time
                    result.response_headers = dict(response.headers)
                    result.response_body = response_text
                    result.response_size_bytes = len(response_text)
            
            # Log the test request
            await self._log_test_request(config_id, result, tested_by)
            
            # Save to test history
            await self._save_test_history(config_id, result, tested_by)
            
            logger.info(f"API test completed for {config.name}: {'✅' if result.success else '❌'}")
            return result
        
        except asyncio.TimeoutError:
            result = APITestResult()
            result.error_message = "Request timeout"
            await self._log_test_request(config_id, result, tested_by)
            return result
        
        except Exception as e:
            result = APITestResult()
            result.error_message = str(e)
            logger.error(f"API test failed for {config_id}: {e}")
            await self._log_test_request(config_id, result, tested_by)
            return result
    
    async def load_test_api(
        self,
        config_id: str,
        concurrent_requests: int = 10,
        total_requests: int = 100,
        endpoint_path: str = "",
        tested_by: str = ""
    ) -> Dict[str, Any]:
        """Perform load testing on an API"""
        try:
            # Check permissions
            if not await self.security_service.check_permission(tested_by, APIPermission.TEST):
                raise PermissionError("Insufficient permissions to perform load testing")
            
            # Validate parameters
            if concurrent_requests > 50:
                raise ValueError("Concurrent requests limited to 50 for safety")
            if total_requests > 1000:
                raise ValueError("Total requests limited to 1000 for safety")
            
            config = await self.api_service.get_api_config(config_id, decrypt_sensitive=True)
            if not config:
                raise ValueError("API configuration not found")
            
            logger.info(f"Starting load test for {config.name}: {total_requests} requests, {concurrent_requests} concurrent")
            
            # Prepare test parameters
            test_url = config.base_url
            if endpoint_path:
                test_url = urljoin(test_url, endpoint_path.lstrip('/'))
            
            headers = config.default_headers.copy()
            auth_headers = self.api_service._build_auth_headers(config.authentication)
            headers.update(auth_headers)
            
            # Run load test
            start_time = time.time()
            results = []
            
            # Create semaphore to limit concurrent requests
            semaphore = asyncio.Semaphore(concurrent_requests)
            
            async def single_request():
                async with semaphore:
                    request_start = time.time()
                    try:
                        async with aiohttp.ClientSession(
                            timeout=aiohttp.ClientTimeout(total=config.timeout.total_timeout)
                        ) as session:
                            async with session.get(test_url, headers=headers, params=config.default_params) as response:
                                response_time = int((time.time() - request_start) * 1000)
                                return {
                                    "success": 200 <= response.status < 400,
                                    "status_code": response.status,
                                    "response_time_ms": response_time,
                                    "error": None
                                }
                    except Exception as e:
                        return {
                            "success": False,
                            "status_code": None,
                            "response_time_ms": int((time.time() - request_start) * 1000),
                            "error": str(e)
                        }
            
            # Execute all requests
            tasks = [single_request() for _ in range(total_requests)]
            results = await asyncio.gather(*tasks)
            
            total_time = time.time() - start_time
            
            # Analyze results
            successful_requests = sum(1 for r in results if r["success"])
            failed_requests = total_requests - successful_requests
            
            response_times = [r["response_time_ms"] for r in results if r["response_time_ms"]]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            min_response_time = min(response_times) if response_times else 0
            max_response_time = max(response_times) if response_times else 0
            
            # Calculate percentiles
            sorted_times = sorted(response_times)
            p50_index = int(len(sorted_times) * 0.5)
            p95_index = int(len(sorted_times) * 0.95)
            p99_index = int(len(sorted_times) * 0.99)
            
            p50_response_time = sorted_times[p50_index] if sorted_times else 0
            p95_response_time = sorted_times[p95_index] if sorted_times else 0
            p99_response_time = sorted_times[p99_index] if sorted_times else 0
            
            # Calculate requests per second
            rps = total_requests / total_time if total_time > 0 else 0
            
            load_test_result = {
                "config_id": config_id,
                "config_name": config.name,
                "test_parameters": {
                    "concurrent_requests": concurrent_requests,
                    "total_requests": total_requests,
                    "endpoint_path": endpoint_path,
                    "test_duration_seconds": total_time
                },
                "results": {
                    "successful_requests": successful_requests,
                    "failed_requests": failed_requests,
                    "success_rate": (successful_requests / total_requests * 100),
                    "requests_per_second": rps,
                    "avg_response_time_ms": avg_response_time,
                    "min_response_time_ms": min_response_time,
                    "max_response_time_ms": max_response_time,
                    "p50_response_time_ms": p50_response_time,
                    "p95_response_time_ms": p95_response_time,
                    "p99_response_time_ms": p99_response_time
                },
                "tested_by": tested_by,
                "tested_at": now_utc()
            }
            
            # Save load test results
            await self.test_history.insert_one({
                "test_type": "load_test",
                "config_id": config_id,
                "results": load_test_result,
                "created_at": now_utc(),
                "created_by": tested_by
            })
            
            logger.info(f"Load test completed for {config.name}: {successful_requests}/{total_requests} successful")
            return load_test_result
            
        except Exception as e:
            logger.error(f"Load test failed for {config_id}: {e}")
            return {"error": str(e)}
    
    async def _log_test_request(self, config_id: str, result: APITestResult, tested_by: str) -> None:
        """Log test request for analytics"""
        try:
            log_entry = APIRequestLog(
                api_config_id=config_id,
                method=HTTPMethod(result.request_details.get("method", "GET")),
                url=result.request_details.get("url", ""),
                status_code=result.status_code,
                response_time_ms=result.response_time_ms,
                response_size_bytes=result.response_size_bytes,
                error_message=result.error_message,
                initiated_by=tested_by,
                request_id=f"test_{int(time.time())}",
                created_at=result.timestamp
            )
            
            await self.api_logs.insert_one(log_entry.to_mongo())
            
        except Exception as e:
            logger.error(f"Failed to log test request: {e}")
    
    async def _save_test_history(self, config_id: str, result: APITestResult, tested_by: str) -> None:
        """Save test result to history"""
        try:
            history_entry = {
                "test_type": "endpoint_test",
                "config_id": config_id,
                "result": result.to_dict(),
                "created_at": result.timestamp,
                "created_by": tested_by
            }
            
            await self.test_history.insert_one(history_entry)
            
        except Exception as e:
            logger.error(f"Failed to save test history: {e}")
    
    async def get_test_history(
        self, 
        config_id: str, 
        limit: int = 50,
        test_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get test history for an API"""
        try:
            filter_dict = {"config_id": config_id}
            if test_type:
                filter_dict["test_type"] = test_type
            
            cursor = self.test_history.find(filter_dict).sort("created_at", -1).limit(limit)
            docs = await cursor.to_list(limit)
            
            return docs
            
        except Exception as e:
            logger.error(f"Failed to get test history for {config_id}: {e}")
            return []
    
    async def test_authentication(self, config_id: str, tested_by: str) -> Dict[str, Any]:
        """Test API authentication specifically"""
        try:
            # Check permissions
            if not await self.security_service.check_permission(tested_by, APIPermission.TEST):
                raise PermissionError("Insufficient permissions to test APIs")
            
            config = await self.api_service.get_api_config(config_id, decrypt_sensitive=True)
            if not config:
                raise ValueError("API configuration not found")
            
            auth_type = config.authentication.type
            
            # Test different scenarios based on auth type
            test_results = []
            
            # Test 1: Valid authentication
            valid_result = await self.test_api_endpoint(
                config_id, "", HTTPMethod.GET, tested_by=tested_by
            )
            test_results.append({
                "test_name": "Valid Authentication",
                "result": valid_result.to_dict()
            })
            
            # Test 2: Invalid authentication (if applicable)
            if auth_type != AuthenticationType.NONE:
                # Create config with invalid auth for testing
                invalid_headers = {}
                
                if auth_type == AuthenticationType.API_KEY:
                    invalid_headers[config.authentication.api_key_header or "X-API-Key"] = "invalid_key"
                elif auth_type == AuthenticationType.BEARER_TOKEN:
                    invalid_headers["Authorization"] = "Bearer invalid_token"
                elif auth_type == AuthenticationType.BASIC_AUTH:
                    invalid_headers["Authorization"] = "Basic aW52YWxpZDppbnZhbGlk"  # invalid:invalid
                
                invalid_result = await self._test_with_custom_headers(
                    config, "", HTTPMethod.GET, invalid_headers, tested_by
                )
                test_results.append({
                    "test_name": "Invalid Authentication",
                    "result": invalid_result.to_dict()
                })
            
            # Test 3: Missing authentication
            if auth_type != AuthenticationType.NONE:
                no_auth_result = await self._test_with_custom_headers(
                    config, "", HTTPMethod.GET, {}, tested_by
                )
                test_results.append({
                    "test_name": "Missing Authentication",
                    "result": no_auth_result.to_dict()
                })
            
            # Analyze results
            auth_test_summary = {
                "config_id": config_id,
                "config_name": config.name,
                "auth_type": auth_type.value,
                "tests_performed": len(test_results),
                "tests_passed": sum(1 for t in test_results if t["result"]["success"]),
                "test_results": test_results,
                "tested_by": tested_by,
                "tested_at": now_utc()
            }
            
            # Save auth test results
            await self.test_history.insert_one({
                "test_type": "auth_test",
                "config_id": config_id,
                "results": auth_test_summary,
                "created_at": now_utc(),
                "created_by": tested_by
            })
            
            return auth_test_summary
            
        except Exception as e:
            logger.error(f"Authentication test failed for {config_id}: {e}")
            return {"error": str(e)}
    
    async def _test_with_custom_headers(
        self,
        config: APIConfiguration,
        endpoint_path: str,
        method: HTTPMethod,
        custom_headers: Dict[str, str],
        tested_by: str
    ) -> APITestResult:
        """Test API with custom headers (for auth testing)"""
        result = APITestResult()
        
        try:
            test_url = config.base_url
            if endpoint_path:
                test_url = urljoin(test_url, endpoint_path.lstrip('/'))
            
            # Use only custom headers (no default auth)
            headers = config.default_headers.copy()
            headers.update(custom_headers)
            
            result.request_details = {
                "url": test_url,
                "method": method.value,
                "headers": headers,
                "params": config.default_params
            }
            
            start_time = time.time()
            
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=config.timeout.total_timeout)
            ) as session:
                async with session.request(
                    method.value,
                    test_url,
                    headers=headers,
                    params=config.default_params
                ) as response:
                    response_time = int((time.time() - start_time) * 1000)
                    response_text = await response.text()
                    
                    result.success = 200 <= response.status < 400
                    result.status_code = response.status
                    result.response_time_ms = response_time
                    result.response_headers = dict(response.headers)
                    result.response_body = response_text
                    result.response_size_bytes = len(response_text)
        
        except Exception as e:
            result.error_message = str(e)
        
        return result
    
    async def test_all_apis(self, tested_by: str) -> Dict[str, Any]:
        """Test all active APIs"""
        try:
            # Check permissions
            if not await self.security_service.check_permission(tested_by, APIPermission.TEST):
                raise PermissionError("Insufficient permissions to test APIs")
            
            # Get all active APIs
            configs, total = await self.api_service.list_api_configs(
                page=1, per_page=1000, status=APIStatus.ACTIVE
            )
            
            test_results = []
            successful_tests = 0
            
            for config in configs:
                try:
                    result = await self.test_api_endpoint(
                        str(config.id), tested_by=tested_by
                    )
                    
                    test_results.append({
                        "config_id": str(config.id),
                        "config_name": config.name,
                        "success": result.success,
                        "status_code": result.status_code,
                        "response_time_ms": result.response_time_ms,
                        "error_message": result.error_message
                    })
                    
                    if result.success:
                        successful_tests += 1
                        
                except Exception as e:
                    test_results.append({
                        "config_id": str(config.id),
                        "config_name": config.name,
                        "success": False,
                        "error_message": str(e)
                    })
            
            summary = {
                "total_apis_tested": len(configs),
                "successful_tests": successful_tests,
                "failed_tests": len(configs) - successful_tests,
                "success_rate": (successful_tests / len(configs) * 100) if configs else 0,
                "test_results": test_results,
                "tested_by": tested_by,
                "tested_at": now_utc()
            }
            
            # Save bulk test results
            await self.test_history.insert_one({
                "test_type": "bulk_test",
                "results": summary,
                "created_at": now_utc(),
                "created_by": tested_by
            })
            
            logger.info(f"Bulk API test completed: {successful_tests}/{len(configs)} successful")
            return summary
            
        except Exception as e:
            logger.error(f"Bulk API test failed: {e}")
            return {"error": str(e)}

# Note: Do not create a global APITestingService instance at import time to avoid
# requiring a database connection during test collection or module import.
# Intentionally omitted global instance.
